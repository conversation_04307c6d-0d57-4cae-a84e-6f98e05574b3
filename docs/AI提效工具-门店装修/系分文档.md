# \[2025-06\]  代运营-AI 提效工具-门店装修

PRD: [《【PRD】AI 提效工具-门店装修》](https://alidocs.dingtalk.com/i/nodes/Qnp9zOoBVBDEydnQUrQzwBvM81DK0g6l?utm_scene=team_space)

服务端系分： [《\[20250507\]AI 供给提效工具》](https://alidocs.dingtalk.com/i/nodes/b9Y4gmKWrdza7Ng1F9p1OgYPJGXn6lpz?dontjump=true)

# 排期表

[请至钉钉文档查看附件《AI 提效工具-门店装修排期表》](https://alidocs.dingtalk.com/i/nodes/G53mjyd80KXOR2zkfjKzLjBAJ6zbX04v?iframeQuery=anchorId%3DX02mas36xp9cizhk697we9)

时间：  前端开发工作量： 8.5pd

- 5.19  开始开发
- 5.30  联调,  联调  3  天
- 6.5  提测，  跟测  5  天
- 6.12  发布

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/54Lq35Am1wjBMl7E/img/607423ff-dc7b-4ad2-911c-6257d66d945e.png)

# 需求系分  (6pd)

## 新增   运维推荐素材页面（工时： 1.5pd）

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/54Lq35Am1wjBMl7E/img/4ec3ceee-c584-4069-a4d9-121d8472b397.png)

图  1

### 业务逻辑说明

1.  tab 不固定，接口动态返回，不同 tab  表格字段是一样的
2.  列表通用展示字段：

    1.  推荐素材：采购素材名称+素材缩略图，不同模块展示内容不同
    2.  推荐时间：最新采购时间
    3.  状态：展示当前素材的使用状态“已使用”“未使用”“处理中”

        1.  状态支持筛选，可选项“已使用”“未使用”“处理中”

    4.  操作：

        1.  “未使用”状态时，操作项为“去提交”
        2.  其他状态无操作项

3.  列表通用功能：支持多选、支持分页
4.  红框内的部分放在表格上方，不需要这个红框，红框内部的内容 flex  分散左右对齐即可
5.  上方表单中商户   用商户列表选择组件，门店用门店列表选择组件，门店要基于商户的 pid  请求下拉选项，下方数据根据表单数据变化刷新数据
6.  选择商户和选择门店两个组件的关联校验：

    1.  门店必填，商户非必填，填写门店后需要自动查询出关联的商户（因为要查询商户维度是否有企微群，发企微消息）
    2.  如先选择了商户，门店必选是该商户下的门店

7.  门店为必填信息，未选择门店时下方数据置空
8.  状态栏支持按已使用、未使用、处理中，多选过滤，修改状态时以接口传参形式重新请求数据
9.  支持多选后一键提交，未选择任何素材时禁用提交按钮
10. 点击去提交和一键提交调用提交接口，提交接口会返回一个跳转地址，自动跳转到该地址

11. 点击提交或一键提交需要先二次确认: title: **信息确认**，content: **发布 xx 张图片至 xx 门店，确认发布后则进入图片审核，审核通过会在高德 C 端对应门店展示该图,是否确认发布?**

12. 商户筛选旁边，如果商户有企微群，需要渲染一个“信息不全?  点击发送企微消息收集材料”，点击后打开“发送消息至企微群”弹窗

13. 推荐素材列要根据接口特定标识判断是否在最下方渲染红字  “  疑似线上已有，注意使用”

### 特殊要求

#### 人物管理  tab

1.  该  tab  下多选屏蔽，不可用

#### 相册 tab

1.  tabs  下再渲染一组  tabs

    1.  固定展示“全部”：全部中，展示该门店最新采购的所有图片
    2.  其他 tab：除全部外，根据当前门店最新采购相册对应的分相册名称进行分 tab 展示

2.  如相册中的素材有特殊标签，在素材名称下面用红色的  tags  渲染标签，素材列支持按标签筛选，可用的标签从接口取

## 新增   商户列表选择组件  (  工时： 0.5pd)

1.  单选
2.  支持传入  value  和 onChange
3.  对  onChange 传入的值应该是一个对象，包括了商户的名称，pid，是否有企微群等信息，同时兼容  Select 的 value  本身的逻辑，即  pid  为  value

## 新增门店列表选择组件(工时: 0.5pd)

1.  单选
2.  支持传入  value  和 onChange
3.  对  onChange 传入的值应该是一个对象，包括了   门店名称，门店  id  等

## 新增   发送消息至企微群弹窗  (工时  0.5pd)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/54Lq35Am1wjBMl7E/img/40617031-fa94-4d05-9661-2eaafb6a6a3c.png)

1.  接收 pid，即商户 id，以及商户名称两个字段
2.  商户名称、信息收集范围、信息内容必填
3.  消息内容的组件用 bd-text-tpl  组件
4.  信息收集范围根据接口动态返回
5.  勾选信息收集范围任何选项时需要打开素材弹窗
6.  素材弹窗的复选结果对应信息收集范围的选项存起来，在确认发送时一块传给接口

## 新增素材弹窗(工时： 0.5pd)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/54Lq35Am1wjBMl7E/img/2a03a21c-69e5-44e8-88bd-3a16ab7b463d.png)

1.  素材弹窗的 title  为接收参数
2.  内容和运维素材页面类似，没有筛选功能，只有复选框和确认
3.  点击确认需要调外部传入的  onOk  回调参数并关闭弹窗，下次打开弹窗需要自动勾选之前的结果

## 新增   表单收集页面（工时： 1.5pd）

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/54Lq35Am1wjBMl7E/img/0ac1b1d8-e3e9-4ec5-b4ad-0e8a6105ac11.png)

1.  该页面需要适配移动端，所有样式单位用 rpx，不要用 px
2.  该页面会携带一个 id  参数，需要把 id 传给接口
3.  接口会返回有哪些 tabs，如相册、推荐菜等，如果是人物管理还会下发人物列表
4.  下面的表单也是接口动态返回，包括是否必填、提示描述(表单标签右边的问号，如果有提示描述，用  Tooltip+QuestionCircleOut  来渲染)
5.  因此你需要封装一个动态组件，用来根据类型来渲染对应的组件，可能会包括下拉框、输入框、图片上传组件等，动态组件必须支持 value  和  onChange  属性
6.  下方还有一段温馨提示，待提供
7.  点击提交时校验必填项，把 id  以及表单数据提交给接口

## 新增   上传图片的通用组件  （工时： 0.5pd）

1.  基于 antd  的  upload  组件封装
2.  需要支持配置图片大小限制(默认  20M)、图片数量限制(默认不限制)、图片格式限制(  默认  png,jpg,jpeg)
3.  用自定义上传的方案，将图片上传到 oss  后返回 url  地址
4.  上传到 oss  的代码如下，注意做封装，不要全写在组件内部，拆分一个独立的页面

```javascript
import { uploadOSS } from '@alife/amap-mp-utils';

const getOssSign = async (contentType = 'image') => {
  const [err, data] = await gdRequest('mtop.amap.mp.merchant.oss.getPolicySignOuter', {
    contentType,
  });
  if (err || !data) {
    return false;
  }
  return data;
};
const getOssUrl = async (ossPath, style = '') => {
  const [err, data] = await gdRequest('content.media.oss.auth.generatePresignedUrl', {
    ossPath,
    style,
  });
  if (!err && data.url) {
    return data.url;
  }
};
export const uploadFile = async (path, contentType = 'image') => {
  const ossSign = await getOssSign(contentType);
  if (!ossSign) {
    return;
  }
  try {
    const id = uuid();
    const key = `${ossSign.dir}${id}${path.name}`;
    const data = await uploadOSS({
      host: ossSign.host,
      ossSign: {
        policy: ossSign.policy,
        accessKeyId: ossSign.accessid,
        signature: ossSign.signature,
      },
      fileKey: key,
      file: path,
    });
    return { id, key };
  } catch (e) {
    return false;
  }
};
export const uploadResource = async ({
  file,
  contentType = 'image',
  needDomainChange = true,
  isOriginalPic = false,
}) => {
  const res = await uploadFile(file, contentType);
  if (res) {
    const { key, id } = res;
    const ossUrl = await getOssUrl(key, file?.cropPropsData);
    if (!ossUrl) {
      return false;
    }
    if (!needDomainChange || contentType === 'video') {
      return ossUrl;
    }
    if (contentType === 'image') {
      const url = await domainChange(ossUrl, isOriginalPic);
      return url;
    }
  }
};
```

## 装修素材提报任务模块支持跳转运维推荐素材页面  (工时： 0.5pd)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/54Lq35Am1wjBMl7E/img/c62f15ed-07a3-4e7d-8ae3-b9aedf8b9745.png)

模块展开状态下，基建素材任务需求的每一个选项如果是“任务完成”，  需要在后面增加一个   去发布的按钮，点击后跳转运维推荐素材页面

# 接口文档系分  (接口定义和接入：1pd)

## 商户列表查询

**接口：**amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationMerchantList

**新增入参：**

isBasic  是否仅查询商户基础字段

## 门店列表查询

**接口：**amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationShopList

**新增入参：**

isBasic  是否仅查询门店基础字段

## tab  列表

**接口：**amap-sales-operation.AiSupplyDraftManageFacade.queryTabInfo

**入参：**

| **字段** | **类型** | **描述** |
| -------- | -------- | -------- |
| shopId   | String   | 门店 Id  |

**出参：**

| **字段**      | **类型**        | **描述**      |
| ------------- | --------------- | ------------- |
| shopId        | String          | 门店 Id       |
| TabInfoList   | List<TabInfoVO> | 模块 Tab 列表 |
| \* moudleType | String          | 模块类型      |
| \* cnt        | int             | 草稿数量      |

## 素材列表查询

**接口：**amap-sales-operation.AiSupplyDraftManageFacade.queryDraftList

**入参：**

| **字段**        | **类型**            | **描述**   |
| --------------- | ------------------- | ---------- |
| shopId          | String              | 门店 Id    |
| moudleType      | String              | 模块类型   |
| status          | String              | 草稿状态   |
| extInfo         | Map<String, Object> | 扩展字段   |
| \* subAlbumType | String              | 子相册类型 |
|                 |                     | 是否主图   |

**出参：**

| **字段**   | **类型** | **描述**                                                      |
| ---------- | -------- | ------------------------------------------------------------- |
| shopId     | String   | 门店 Id                                                       |
| moudleType | String   | 模块类型                                                      |
| summary    | JSON     | 推荐素材字段 展示字段参考：“**4.3.3.2  涉及修改点”第 4 小结** |
| createTime | Date     | 推荐时间                                                      |
| status     | String   | 状态                                                          |

## 提交/批量提交接口

**接口：**amap-sales-operation.AiSupplyDraftManageFacade.batchPublish

**入参：**

| **字段**    | **类型**     | **描述**                                                                                                                        |
| ----------- | ------------ | ------------------------------------------------------------------------------------------------------------------------------- |
| shopId      | String       | 门店 Id                                                                                                                         |
| moudleType  | String       | 模块类型                                                                                                                        |
| draftIdList | List<String> | 草稿 Id list **备注：相册推送需要保序，按照小二点击顺序推送** $\color{#0089FF}{@陈焕阳(予瀚)}$ $\color{#0089FF}{@宁家麟(家喻)}$ |

**出参：**

| **字段** | **类型** | **描述** |
| -------- | -------- | -------- |
| success  | Boolean  | 推送结果 |

## 素材收集配置查询，即表单收集页面配置查询

**接口：**amap-sales-operation.AiSupplyDraftManageFacade.queryCollectDraft

**入参：**

| **字段**   | **类型** | **描述**             |
| ---------- | -------- | -------------------- |
| bizOrderId | String   | 流水 id-页面投放流水 |

**出参：**

| **字段**     | **类型**             | **描述**                                                                                                                                                                                                                                                                                      |
| ------------ | -------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| shopId       | String               | 门店 Id                                                                                                                                                                                                                                                                                       |
| shopName     | String               | 门店名称                                                                                                                                                                                                                                                                                      |
| List<Object> |                      |                                                                                                                                                                                                                                                                                               |
| \-moudleType | String               | 模块类型-如手艺人                                                                                                                                                                                                                                                                             |
| \-draftList  | List<PublishDraftVO> | 发布草稿内容列表                                                                                                                                                                                                                                                                              |
| \* draftId   | String               | 草稿 Id                                                                                                                                                                                                                                                                                       |
| \* name      | String               | 名称                                                                                                                                                                                                                                                                                          |
| \* content   | JSON                 | 发布草稿 VO 内容组装格式参考：[《内容传参结构整理》](https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPPNL2rXInvEgeawJzN67Mw4?cid=597776739%3A4682317237&utm_source=im&utm_scene=person_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=dingd8e1123006514592) |

## 表单收集页面提交接口

**接口：**amap-sales-operation.AiSupplyDraftManageFacade.submitDraft

**入参：**

| **字段** | **类型** | **描述**                                                                                                                                                                                                                                                                                      |
| -------- | -------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| draftId  | String   | 草稿 id                                                                                                                                                                                                                                                                                       |
| content  | Object   | 发布草稿 VO 内容组装格式参考：[《内容传参结构整理》](https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPPNL2rXInvEgeawJzN67Mw4?cid=597776739%3A4682317237&utm_source=im&utm_scene=person_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_medium=im_card&corpId=dingd8e1123006514592) |

**出参：**

| **字段** | **类型** | **描述** |
| -------- | -------- | -------- |
| success  | Boolean  | 操作结果 |
| message  | String   | 错误信息 |

## 草稿状态批量更新接口（新增）

**接口：**amap-sales-operation.AiSupplyDraftManageFacade.batchUpdateStatus

**入参：**

| **字段**    | **类型**     | **描述**     |
| ----------- | ------------ | ------------ |
| shopId      | String       | 门店 Id      |
| moudleType  | String       | 模块类型     |
| draftIdList | List<String> | 草稿 Id list |

**出参：**

| **字段** | **类型** | **描述** |
| -------- | -------- | -------- |
| success  | Boolean  | 推送结果 |

#   灰度和埋点  (1pd)

灰度接口： amap-sales-operation.OptConfigQueryHsf.businessSceneSwitch
