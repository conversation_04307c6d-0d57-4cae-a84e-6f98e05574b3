# AI 提效工具-门店装修-接口文档

> 该文档基于《系分文档.md》与前端接口文档规则梳理，所有接口定义均以 src/services/index.ts 为准，类型定义严格结合业务场景。

---

## 1. 商户列表查询

### 1.1 说明

该接口用于获取商户列表，支持基础字段查询，主要用于运维推荐素材页面的商户选择组件。

### 1.2 新增/修改

[修改]

- 新增入参 isBasic，控制是否仅查询商户基础字段
- 新增可选入参 page，支持分页查询

### 1.3 入参/出参类型

```ts
export interface IQueryMerchantListParams {
  isBasic?: boolean; // 是否仅查询商户基础字段，默认false
  page?: {
    pageNo: number;
    pageSize: number;
  }; // 分页参数，可选
  // ...其他筛选字段（如分页、关键字等，具体以后端为准）
}
export interface IMerchantItem {
  pid: string; // 商户ID
  name: string; // 商户名称
  hasQwGroup?: boolean; // 是否有企微群
  // ...其他字段 //待确认：后端返回字段需补充
}
export const getMerchantList = (params: IQueryMerchantListParams): Promise<IMerchantItem[]> => {
  return gdRequest(
    'amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationMerchantList',
    params,
  );
};
```

---

## 2. 门店列表查询

### 2.1 说明

该接口用于获取门店列表，支持基础字段查询，主要用于运维推荐素材页面的门店选择组件。

### 2.2 新增/修改

[修改] 新增入参 isBasic，控制是否仅查询门店基础字段。

### 2.3 入参/出参类型

```ts
export interface IQueryShopListParams {
  isBasic?: boolean; // 是否仅查询门店基础字段，默认false
  pid?: string; // 商户ID，选填
  // ...其他筛选字段 //待确认：后端返回字段需补充
}
export interface IShopItem {
  shopId: string; // 门店ID
  shopName: string; // 门店名称
  // ...其他字段 //待确认：后端返回字段需补充
}
export const getShopList = (params: IQueryShopListParams): Promise<IShopItem[]> => {
  return gdRequest(
    'amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationShopList',
    params,
  );
};
```

---

## 3. tab 列表查询

### 3.1 说明

该接口用于获取指定门店下的模块 tab 信息，动态渲染页面 tab。

### 3.2 新增/修改

[新增]

### 3.3 入参/出参类型

```ts
export interface IQueryTabInfoParams {
  shopId: string; // 门店ID
}
export interface ITabInfoVO {
  moduleType: string; // 模块类型
  moduleName: string; // 模块名称
  draftCount: number; // 草稿数量
  extInfo?: {
    subAlbumTypeList?: string[]; // 子相册类型列表
    [key: string]: any; // 其他扩展字段
  };
}
export interface IQueryTabInfoResult {
  shopId: string;
  tabList: ITabInfoVO[];
}
export const queryTabInfo = (params: IQueryTabInfoParams): Promise<IQueryTabInfoResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryTabInfo', params);
};
```

---

## 4. 素材列表查询

### 4.1 说明

该接口用于获取指定门店、模块下的推荐素材列表，支持状态、子相册类型等筛选。

### 4.2 新增/修改

[新增]

### 4.3 入参/出参类型

```ts
export interface IQueryDraftListParams {
  shopId: string;
  moudleType: string;
  status?: string; // 草稿状态，可多选
  extInfo?: Record<string, any>; // 扩展字段
  subAlbumType?: string; // 子相册类型
  // isMainPic?: boolean; // 是否主图 //待确认：后端字段
}
export interface IDraftItem {
  shopId: string;
  moudleType: string;
  summary: any; // 推荐素材字段，结构见业务 //待确认：需补充详细结构
  createTime: string; // 推荐时间
  status: string; // 状态
  extInfo: Record<string, any>; // 扩展字段
}
export const queryDraftList = (params: IQueryDraftListParams): Promise<IDraftItem[]> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryDraftList', params);
};
```

---

## 5. 发布明细内容批量查询

### 5.1 说明

该接口用于批量查询发布明细内容。

### 5.2 新增/修改

[新增]

### 5.3 入参/出参类型

```ts
export interface IQueryPubContentListParams {
  shopId: string;
  moudleType: string;
  draftIdList: string[]; // 草稿Id list
}
export interface IPublishDraftVO {
  draftId: string;
  content: any; // 发布草稿VO内容
}
export interface IQueryPubContentListResult {
  shopId: string;
  moudleType: string;
  draftList: IPublishDraftVO[];
}
export const queryPubContentList = (
  params: IQueryPubContentListParams,
): Promise<IQueryPubContentListResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryPubContentList', params);
};
```

---

## 6. 素材批量提交接口

### 6.1 说明

该接口用于批量提交选中的草稿素材，支持相册顺序推送。

### 6.2 新增/修改

[新增]

### 6.3 入参/出参类型

```ts
export interface IBatchPublishParams {
  shopId: string;
  moudleType: string;
  draftIdList: string[]; // 草稿ID列表，顺序推送
}
export interface IBatchPublishResult {
  success: boolean;
}
export const batchPublish = (params: IBatchPublishParams): Promise<IBatchPublishResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.batchPublish', params);
};
```

---

## 7. 素材收集配置查询（表单收集页面）

### 7.1 说明

该接口用于获取表单收集页面的动态配置，包括 tab、表单项等。

### 7.2 新增/修改

[新增]

### 7.3 入参/出参类型

```ts
export interface IQueryCollectDraftParams {
  shopId: string; // 门店Id
  expireTimestamp: string; // 过期时间
}
export interface ICollectDraftDTO {
  draftNo: number; // 草稿number
  name: string; // 名称，用于素材收集页展示当前收集数据的名称，如手艺人的名字
  materialContent: any[]; // 发布草稿VO内容
}
export interface ICollectDraftModule {
  moduleType: string; // 模块类型
  moduleName: string; // 模块类型名称
  collectDraftList: ICollectDraftDTO[];
}
export interface IQueryCollectDraftResult {
  shopId: string;
  shopName: string;
  collectDraftModuleList: ICollectDraftModule[];
}
export const queryCollectDraft = (
  params: IQueryCollectDraftParams,
): Promise<IQueryCollectDraftResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryCollectDraft', params);
};
```

---

## 8. 表单收集页面提交接口

### 8.1 说明

该接口用于提交表单收集页面的草稿内容。

### 8.2 新增/修改

[新增]

### 8.3 入参/出参类型

```ts
export interface ISubmitDraftParams {
  requestId: string; // 请求id
  shopId: string; // 门店Id
  draftNo: number; // 草稿number
  moduleType: string; // 模块类型
  materialContent: Record<string, any>; // 发布草稿VO内容
}
export interface ISubmitDraftResult {
  success: boolean;
  message?: string;
}
export const submitDraft = (params: ISubmitDraftParams): Promise<ISubmitDraftResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.submitDraft', params);
};
```

---

## 9. 草稿状态批量更新接口

### 9.1 说明

该接口用于批量更新草稿状态。

### 9.2 新增/修改

[新增]

### 9.3 入参/出参类型

```ts
export interface IBatchUpdateStatusParams {
  shopId: string;
  moudleType: string;
  draftIdList: string[];
}
export interface IBatchUpdateStatusResult {
  success: boolean;
}
export const batchUpdateStatus = (
  params: IBatchUpdateStatusParams,
): Promise<IBatchUpdateStatusResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.batchUpdateStatus', params);
};
```

---

## 10. 灰度开关查询接口

### 10.1 说明

该接口用于查询业务场景下的灰度开关配置。

### 10.2 新增/修改

[修改] BusinessSceneEnum 新增场景：AI_SUPPLY("AI_SUPPLY","AI 供给提效")

### 10.3 入参/出参类型

```ts
export const configBusinessNewsGrey = (
  sceneList: Array<'TASK_PRIORITY' | 'AI_SUPPLY'>,
): Promise<
  Array<{
    switchStatus: boolean;
    scene: string;
  }>
> => {
  return request('amap-sales-operation.OptConfigQueryHsf.businessSceneSwitch', {
    sceneList,
  });
};
```

---

## 11. 查询个人素材接口

### 11.1 说明

该接口用于查询个人素材，新增素材收集场景。

### 11.2 新增/修改

[修改] 新增场景类型：DRAFT_CONTENT_COLLECT

### 11.3 入参/出参类型

```ts
export interface IGroupMaterialsQueryParams {
  scene?: string; // 场景类型，新增素材收集：DRAFT_CONTENT_COLLECT
}
export interface IMaterial {
  materialId: string; // 素材ID
  scene: string; // 场景类型
  content: string; // 素材内容
}
export interface IOptMaterialQueryResult {
  materials: IMaterial[]; // 素材列表
}
export interface IQueryGroupMaterialsResult {
  success: boolean; // 是否成功
  data: IOptMaterialQueryResult; // 返回数据
  errorCode?: string; // 错误码
  errorMsg?: string; // 错误信息
}
export const queryGroupMaterials = (
  params: IGroupMaterialsQueryParams,
): Promise<IQueryGroupMaterialsResult> => {
  return gdRequest('amap-sales-operation.OptMaterialQueryHsf.queryGroupMaterials', params);
};
```

---

## 12. 企微素材推送接口

### 12.1 说明

该接口用于企微素材推送，新增门店 Id 和草稿列表参数。

### 12.2 新增/修改

[修改] 新增入参：shopId、draftNoList

### 12.3 入参/出参类型

```ts
export interface IMerchantNewsReachModel {
  pid: string; // 商户ID
  shopId?: string; // 门店ID
  merchantNewsPageUrl?: string; // 喜报图片URL，当from=MERCHANT_NEWS时必填
  mainShopName?: string; // 主店名称
}
export interface IBatchMerchantNewReachOutParams {
  merchantNewsReachModelList: IMerchantNewsReachModel[]; // 商户触达列表
  materialId: string; // 素材ID
  from: string; // 来源场景，需符合FrontReachSceneEnum枚举
  sendMerchantNews?: boolean; // 是否发送喜报图片，默认false
  shopId?: string; // 门店Id
  draftNoList?: number[]; // 草稿列表
}
export interface ISendFailureDTO {
  pid: string; // 商户ID
  mainShopName: string; // 主店名称
  failReason: string; // 失败原因
}
export interface IBatchMerchantNewReachOutResult {
  success: boolean; // 是否成功
  data: {
    sendFailureList: ISendFailureDTO[]; // 发送失败列表
  };
  errorCode?: string; // 错误码
  errorMsg?: string; // 错误信息
}
export const batchMerchantNewReachOut = (
  params: IBatchMerchantNewReachOutParams,
): Promise<IBatchMerchantNewReachOutResult> => {
  return gdRequest(
    'com.amap.sales.operation.client.OperationAutoPushFacade.batchMerchantNewReachOut',
    params,
  );
};
```

---

## 13. 灰度开关查询接口

```ts
export const configBusinessNewsGrey = (
  sceneList: Array<'TASK_PRIORITY' | 'AI_SUPPLY'>,
): Promise<
  Array<{
    switchStatus: boolean;
    scene: string;
  }>
> => {
  return request('amap-sales-operation.OptConfigQueryHsf.businessSceneSwitch', {
    sceneList,
  });
};
```
