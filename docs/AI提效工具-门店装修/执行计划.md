# 执行计划

## 任务 1：新增 商户列表选择组件

1. 在 `src/components/merchant-select-v2/` 新建 `index.tsx`，实现商户单选下拉组件。
2. 支持传入 `value` 和 `onChange`，`onChange` 返回 `IMerchantItem` 对象。
3. 兼容 Select 的 value 逻辑（pid 为 value）。
4. 支持 loading、空状态、外部初始值。
5. 数据源调用 `getMerchantList` 接口，类型为 `IQueryMerchantListParams`、`IMerchantItem`。
6. 针对该需求，接口入参必须默认传入 isBasic [新增]
7. 商户选择器支持按商户名称过滤, 前端过滤 [新增]
8. 支持"是否有企微群"字段展示。

---

## 任务 2：新增 门店列表选择组件

1. 在 `src/components/shop-select-v2/` 新建 `index.tsx`，实现门店单选下拉组件。
2. 支持传入 `value` 和 `onChange`，`onChange` 返回 `IShopItem` 对象。
3. 兼容 Select 的 value 逻辑（shopId 为 value）。
4. 支持 loading、空状态、外部初始值。
5. 针对该需求，接口入参必须默认传入 isBasic [新增]
6. 门店选择器支持按门店名称过滤, 前端过滤 [新增]
7. 支持根据商户 pid 联动筛选门店。
8. 数据源调用 `getShopList` 接口，类型为 `IQueryShopListParams`、`IShopItem`。

---

## 任务 3：新增 上传图片的通用组件

1. 在 `src/components/upload-oss-noauth/` 新建 `index.tsx`，基于 antd Upload 封装，支持多图上传。
2. 支持图片大小（默认 20M）、数量、格式（默认 png/jpg/jpeg）限制，均可通过 props 配置。
3. 组件为受控组件，支持 value（string[]，图片 url 数组）、onChange（返回最新图片 url 数组）。
4. 使用自定义上传（customRequest），上传逻辑单独封装到 `src/utils/oss-upload.ts`，组件只负责 UI 和交互。
5. 上传工具 `oss-upload.ts` 需严格参考系分文档方案，分为三步：
   - 获取 OSS 签名（接口 url：`mtop.amap.mp.merchant.oss.getPolicySignOuter`）
   - 上传文件到 OSS（用 uploadOSS 工具）
   - 获取图片访问 url（接口 url：`content.media.oss.auth.generatePresignedUrl`）
   - （如需换域名，接口 url：`domainChange`，按需处理）
6. 上传成功后，将新图片 url 合并到 value，触发 onChange。支持图片删除，删除后触发 onChange。
7. beforeUpload 校验格式和大小，不通过则 message.error 并阻止上传。
8. UI 采用 antd Upload 的 picture-card 模式，上传按钮用 PlusOutlined，达到最大数量时隐藏上传按钮。
9. 组件类型、props、回调、工具函数均需类型安全，便于后续维护和复用。

---

## 任务 4：新增 运维推荐素材页面

1. 在 `src/pages/ai-material-recommend/` 新建 `index.tsx`。
2. 顶部表单区：集成商户选择组件、门店选择组件、状态筛选、企微群按钮。
   - 企微群弹窗 send-qw-msg-modal 组件应放在 src/pages/ai-material-recommend/components/send-qw-msg-modal/ 下
3. 表格区：动态 tab（`queryTabInfo`）、表格字段固定（推荐素材、推荐时间、状态、操作），支持多选、分页。
4. 数据获取：`queryDraftList`，类型为 `IQueryDraftListParams`、`IDraftItem`。
5. 多选提交/一键提交：`batchPublish`，二次确认弹窗，接口返回跳转地址自动跳转。
6. 状态批量更新：`batchUpdateStatus`。
7. 商户有企微群时，渲染"发送消息至企微群"按钮，弹出 `send-qw-msg-modal`。
8. 推荐素材列根据标识渲染红字"疑似线上已有，注意使用"。
9. 特殊 tab 逻辑：
   - 人物管理 tab 下多选不可用。
   - 相册 tab 下有二级 tab，支持标签筛选（标签从接口返回）。
10. 门店为必填，未选门店时下方数据置空。
11. 选择门店后自动查询商户并回填。
12. 选择商户后门店下拉只展示该商户下门店。

---

## 任务 5：新增 素材弹窗

1. 在 `src/components/material-modal/` 新建 `index.tsx`。
2. 接收 title 参数。
3. 内容与运维推荐素材页面表格一致，无筛选，仅复选框和确认。
4. 确认时调外部传入的 `onOk` 回调并关闭弹窗，下次打开自动勾选上次结果。
5. 数据源复用 `queryDraftList`。

---

## 任务 6：新增 发送消息至企微群弹窗

1. 在 `src/components/send-qw-msg-modal/` 新建 `index.tsx`。
2. 接收 pid、商户名称。
3. 商户名称、信息收集范围、信息内容必填。
4. 消息内容用 `bd-text-tpl` 组件。
5. 信息收集范围接口动态返回（**接口待补充**，需预留类型）。
6. 勾选信息收集范围时打开素材弹窗，复选结果与信息收集范围选项绑定。
7. 确认发送时收集所有数据并调接口（**接口待补充**，需预留类型）。

---

## 任务 7：新增 表单收集页面

1. 在 `src/pages/ai-material-collect/` 新建 `index.tsx`。
2. 适配移动端，样式单位用 rpx。
3. 页面携带 id 参数，传给接口。
4. tab、表单项接口动态返回：`queryCollectDraft`，类型为 `IQueryCollectDraftParams`、`IQueryCollectDraftResult`。
5. 封装动态表单组件，支持下拉、输入、图片上传等，支持 value/onChange。
6. 表单项支持必填、提示描述（Tooltip+QuestionCircleOut）。
7. 下方温馨提示（内容待补充）。
8. 点击提交时校验必填项，调 `submitDraft` 接口，类型为 `ISubmitDraftParams`、`ISubmitDraftResult`。

---

## 任务 8：装修素材提报任务模块支持跳转运维推荐素材页面

1. 在装修素材提报任务模块（如 `src/pages/task-board/` 或相关业务模块）增加"去发布"按钮，仅"任务完成"状态显示。
2. 点击后跳转运维推荐素材页面，带上 shopId、moudleType 等参数。

---

## 任务 9：灰度和埋点

1. 灰度开关：调用 `configBusinessNewsGrey` 接口，类型见接口文档。
2. 埋点：在各页面/模块关键操作点埋点，具体点位是前面所有 8 个任务中涉及点击按钮的操作
3. 埋点用 `src/utils/trace.tsx`的 trace 方法

---

## 模块交互图

```plain
@startuml
actor 用户
用户 --> 运维推荐素材页面 : 进入页面
运维推荐素材页面 --> 商户选择组件 : 选择商户
运维推荐素材页面 --> 门店选择组件 : 选择门店
运维推荐素材页面 --> 素材列表接口 : 获取素材
运维推荐素材页面 --> 上传图片组件 : 上传图片
运维推荐素材页面 --> 发送消息至企微群弹窗 : 发送消息
发送消息至企微群弹窗 --> 素材弹窗 : 勾选素材
发送消息至企微群弹窗 --> 上传图片组件 : 上传图片
运维推荐素材页面 --> 表单收集页面 : 跳转
表单收集页面 --> 上传图片组件 : 上传图片
装修素材提报任务模块 --> 运维推荐素材页面 : 跳转
所有页面/模块 --> 灰度和埋点 : 埋点/灰度开关
@enduml
```

---

## 类型定义与接口补充说明

- 所有类型定义统一放在 `src/types/ai-material/` 下，严格参照接口文档。
- 动态表单项、summary、标签等结构如后端未补充，暂用 any 类型，后续补全。
- 发送消息至企微群弹窗相关接口、信息收集范围接口，需后端补充，开发时预留类型与调用点。

---

## 拆分与复用原则

- 基础组件（商户选择、门店选择、上传图片）优先开发，供页面/弹窗复用。
- 页面与弹窗均按"主入口+components"模式拆分，便于维护和复用。
- 业务逻辑与 UI 分离，接口请求、类型定义、工具方法单独维护。

---

**执行计划已完整梳理，符合需求、可拆分、依赖清晰、数据流合理。  
如需导出为文档或进入具体编码阶段，请直接告知！**
