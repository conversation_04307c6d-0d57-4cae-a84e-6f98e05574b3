# 项目技术文档

## 1. 技术栈

### 应用框架

- **ice.js** - 基于 React 的前端应用框架，版本 3.x
- 支持微前端架构（qiankun 子应用）
- 使用 TypeScript 进行开发

### UI 组件库

- **antd** - 版本 5.24.3，企业级 UI 设计语言和 React 组件库
- **@ant-design/icons** - Ant Design 图标库
- **styled-components** - CSS-in-JS 样式解决方案，版本 6.1.11

### 时间库

- **dayjs** - 轻量级时间处理库，版本 1.11.11

### 埋点方案

- **@alife/amap-aes-trace** - 主要埋点方案，版本 2.1.0，使用 sendEvent 方法
- **@alife/amap-tracker** - 辅助埋点方案，版本 0.1.10，使用 recordCLK、recordEXP 方法

### 其他核心依赖

- **React** - 版本 18.3.1
- **ahooks** - React Hooks 工具库，版本 3.8.4
- **bizcharts** - 数据可视化图表库，版本 4.1.23
- **echarts** - 图表库，版本 5.6.0

## 2. 项目概要

这是一个基于 ice.js 框架的 Web 项目。这是一个任务管理系统，主要用于商户运营任务的管理和数据分析，包括门店管理、广告投放、业务数据分析等功能模块。项目支持微前端架构，可以作为子应用集成到其他系统中。

## 3. 类型定义规范

### 类型定义目录

类型定义主要在以下位置：

- `src/types/*.ts` - 全局类型定义
- 组件同目录下的类型定义
- 部分类型定义在组件内部

建议把类型都定义在 `src/types/xx.ts` 下，以业务领域名称划分文件。

### 类型定义取名规范

类型定义命名规范：以 I 开头的大驼峰形式，并用 export 导出

示例：

```typescript
interface IAction {
  jumpType: string;
  jumpTypeNew: string;
  buttonText: string;
  buttonType: ActionButtonType;
  greyButton: boolean;
  jumpUrlList: string;
  showButton: boolean;
  client: string;
  jumpUrl: string;
  desc?: string;
}
```

类型定义建议用 interface，而不是 type，复杂情况可以用泛型来兼容类型动态性。

## 4. 组件定义规范

### 创建文件的规则

1. `src/pages` 下的文件通常以"kebab-case"形式命名文件夹，因为使用约定式路由框架
2. 公共组件在 `src/components` 下，使用"kebab-case"命名文件夹
3. 业务组件可以在业务根目录下用/components，使用"kebab-case"命名
4. 工具类写在 `src/utils` 下
5. React hooks 写在 `src/hooks` 下，以 `useXXXHook` 的小驼峰形式命名

### React 组件定义规则

React 组件必须以大驼峰形式定义，使用函数组件和 TypeScript：

```typescript
interface IProps {
  title: string;
  showTips?: boolean;
}

export const Header: React.FC<IProps> = ({ title, showTips = true }) => {
  return (
    <>
      <div className="header-title">{title}</div>
      {showTips && <HeaderTips />}
    </>
  );
};
```

或者使用默认导出：

```typescript
interface IProps {
  value?: string;
  onChange?: (val: string) => void;
}

export default function Input(props: IProps) {
  return <Input />;
}
```

## 5. 项目埋点方案

### 埋点文件结构

项目埋点已重构为目录结构：

- `src/utils/trace/index.ts` - 主要的埋点函数（trace、traceExp、traceClick）
- `src/utils/trace/traceMap.ts` - 埋点映射表（TrackerKey、TraceEvent 枚举）

### 埋点函数

- `trace(...args)` - 通用事件埋点
- `traceExp(TrackerKey, TraceEvent, params)` - 曝光事件埋点
- `traceClick(TrackerKey, TraceEvent, params)` - 点击事件埋点

### 使用方式

```typescript
import { trace, traceExp, traceClick, TrackerKey, TraceEvent } from '@/utils/trace';

// 通用事件埋点
trace('event_name', 'param1', 'param2');

// 曝光埋点 - 使用枚举映射
traceExp(TrackerKey.首页, TraceEvent.待办任务曝光, {
  taskNo: task.taskNo,
});

// 点击埋点 - 使用枚举映射
traceClick(TrackerKey.首页, TraceEvent.待办任务点击, {
  taskNo: task.taskNo,
});
```

### 埋点映射表

为避免硬编码字符串，项目使用枚举映射埋点事件：

- **TrackerKey** - 页面标识枚举（如：`TrackerKey.首页`、`TrackerKey.商户列表`）
- **TraceEvent** - 埋点事件枚举，使用中文描述映射到英文事件名（如：`TraceEvent.待办任务曝光`）

### 参数说明

- 支持最多 7 个参数（c1-c7）
- EventKey 固定为'xy-task'
- 商户相关使用 `pid` 参数，任务相关使用 `taskNo` 参数
