# \[202505\]智能质检【代运营质检二期】

**aone:**

**prd:** [《运维质检 V1.0-PRD》](https://alidocs.dingtalk.com/i/nodes/qnYMoO1rWxrkmoj2IkOKB2K6J47Z3je9?utm_scene=person_space&utm_medium=dingdoc_doc_plugin_card&utm_source=dingdoc_doc)  $\color{#0089FF}{@王鹏阳(瑞得)}$

**接口文档：**[《接口文档》](https://alidocs.dingtalk.com/i/nodes/G53mjyd80KXOR2zkfj49rMEgJ6zbX04v)  $\color{#0089FF}{@徐文洋(亦竟)}$

ESP  提报工单需求

对应目录为 src/components/task-detail/shop-infrastruct-task-detail/MerchantAuditForm.tsx，  请在代码中补充注释，注明该模块是  ESP  工单提报模块

# 排期

5  月  27  日开发

6  月  6  号联调

6  月  10  号提测

预估工时： 1  天

实际工时：

# 需求系分

**分支  feat/1.97.0**

## 提报审核卡片基于角色，可提报次数进行限制

运维 1 已经提报后禁用提报，运维 2 第二次之后，每次提报时，弹窗提示：审核资源有限，请按需、合理提交审核

- 点击提报审核按钮时，若当前提报门店判断逻辑用的是 3.0LV 级别，则不提示任何文案；若当前提报门店判断逻辑用的是 1.0 分值，则弹窗提示：因指标计算存在延迟，请完成门店任务 1-3 天后，再提报审核（以免因指标计算延迟，导致门店质检不过审）

## 原质检结果不通过再次提交审核时的文案更改

更改为：  请确认，原审核工单不申诉后，再次提报审核

注释原来的文案，不要直接替换

## 提报完成后直接将卡片收起

## 在卡片内左侧最下方贴右对齐展示

上次质检结果/时间：通过/2025-01-01 15:30

查看详情(点击跳转详情页面，和原来的查看详情一样)

## 卡点待解决改成下拉框

支持多选

# 接口系分

## 获取工单信息的接口增加出参

```tsx
      "shopStaffRelationInfo": [ // 人店关系数据
            {
                "staffId": "3033000000006107", // 员工ID
                "relationType": "ADVERTISE_OPERATE",
                "firstOperator": false // 是否为运维1
            },
            {
                "staffId": "3033000000006105",
                "relationType": "BASIC_FACILITIES_BUILDER",
                "firstOperator": true
            }
        ],
        "merchantScoreInfo": { //商家质量分
            "score": 3,
            "scoreVersion": "1.0" // 商家分 1.0
        },
        "shopInspectionInfo": { // 门店商品质检信息
            "InspectResult": "PASS", // 质检结果
            "failReason": "商品价格有误" // 质检失败原因
        }
```

## 查询无法提报原因的接口增加出参

结构待提供，用于渲染卡点待解决下拉框的选项
