# 智能质检【代运营质检二期】接口文档

## 1. [修改] 查询本地 ESP 订单信息接口

该接口用于获取门店的 ESP 工单信息，包括人店关系、商家质量分、门店商品质检信息等，用于判断提报权限和显示质检结果。

修改内容：

- 新增 `shopStaffRelationInfo` 字段：人店关系数据，用于判断运维 1/运维 2 角色
- 新增 `merchantScoreInfo` 字段：商家质量分信息，用于判断是否显示延迟提示
- 新增 `shopInspectionInfo` 字段：门店商品质检信息，用于显示上次质检结果

```ts
interface ShopStaffRelation {
  staffId: string; // 员工ID
  relationType: string; // 关系类型
  firstOperator: boolean; // 是否为运维1
}

interface MerchantScoreInfo {
  score: number; // 商家质量分
  scoreVersion: string; // 商家分版本，如 "1.0" 或 "3.0"
}

interface ShopInspectionInfo {
  inspectResult: 'PASS' | 'FAIL'; // 质检结果：通过/不通过
  failReason?: string; // 质检失败原因
  inspectTime?: string; // 质检时间，格式：2025-01-01 15:30
}

interface AuditBizOrder {
  gmtTime: string;
  orderNodeDesc: string;
}

interface AuditRecord {
  auditId: string;
  auditType: string;
  bizId: string;
  submitterId: string;
  submitterType: string;
  auditStatus: string;
  auditStatusDesc: string;
  auditBizOrders: AuditBizOrder[];
  collectShopName: string;
  optEspOrderDetailUrl: string;
  // 新增字段
  shopStaffRelationInfo: ShopStaffRelation[]; // 人店关系数据
  merchantScoreInfo: MerchantScoreInfo; // 商家质量分
  shopInspectionInfo: ShopInspectionInfo; // 门店商品质检信息
}

export const queryLocalOptEspWoosOrder = (params: {
  shopId: string;
  auditId?: string;
}): Promise<AuditRecord> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.queryLocalOptEspOrder', params);
};
```

## 2. [修改] 查询无法提报原因接口

该接口用于获取卡点待解决的选项数据，从原来的文本框改为下拉框多选。

修改内容：

- 新增出参结构，提供下拉框选项数据

```ts
// 待确认：具体的选项数据结构
interface UnableSubmitOption {
  value: string; // 选项值
  label: string; // 选项显示文本
  category?: string; // 选项分类
}

interface UnableSubmitReason {
  lackMaterial?: string; // 缺失材料（保持原有字段）
  processBarrier?: string; // 有卡点需解决（保持原有字段）
  // 新增字段
  processBarrierOptions?: string[]; // 卡点待解决选中的选项值数组
}

interface AuditUnableCommitRecord {
  auditId: string;
  auditType: 'ESP_WOOS_ORDER_UNABLE_COMMIT';
  bizId: string;
  submitterId: string;
  submitterType: 'PROVIDER_STAFF';
  unableSubmitReason: UnableSubmitReason;
  // 新增字段
  processBarrierOptionList?: UnableSubmitOption[]; // 卡点待解决的下拉框选项
}

export const queryUnableCommitReason = (params: {
  shopId: string;
}): Promise<AuditUnableCommitRecord> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.queryUnableSubmitReason', {
    shopId: params.shopId,
  });
};
```

## 3. [修改] 保存无法提交原因接口

该接口用于保存卡点待解决的选择结果，支持多选。

修改内容：

- 新增 `processBarrierOptions` 字段：支持多选的卡点选项

```ts
interface UnableSubmitReason {
  lackMaterial?: string; // 缺失材料
  processBarrier?: string; // 有卡点需解决（保持原有字段用于兼容）
  // 新增字段
  processBarrierOptions?: string[]; // 卡点待解决选中的选项值数组
}

export const saveUnableCommitReason = (params: {
  shopId: string;
  unableSubmitReason: UnableSubmitReason;
}): Promise<void> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.saveUnableSubmitReason', params);
};
```

## 待确认事项

1. `processBarrierOptionList` 的具体选项数据结构需要后端提供
2. `shopStaffRelationInfo` 中的 `relationType` 字段的具体枚举值需要确认
3. `ShopInspectionInfo` 中的 `inspectTime` 字段格式需要确认
4. 是否需要新增接口来获取当前用户的提报次数信息，用于运维 2 的提示逻辑
