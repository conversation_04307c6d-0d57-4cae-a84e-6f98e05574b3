# 智能质检【代运营质检二期】执行计划

## 任务概述

基于需求文档，本次需求主要涉及对现有 ESP 工单提报模块的功能增强，包括：

1. 基于角色的提报次数限制
2. 提报审核时的延迟提示
3. 原质检结果不通过时的文案更改
4. 提报完成后自动收起卡片
5. 显示上次质检结果信息
6. 卡点待解决改为下拉框多选

## 任务执行计划

### 任务 1：更新 ESP 审核相关类型定义

**目标**：根据接口文档更新类型定义，支持新增的字段

**执行步骤**：

1. ✅ 已完成：创建 `src/types/esp-audit/index.ts` 文件
2. ✅ 已完成：定义 `ShopStaffRelation`、`MerchantScoreInfo`、`ShopInspectionInfo` 等新增类型
3. ✅ 已完成：更新 `AuditRecord` 类型，添加新增字段
4. ✅ 已完成：更新 `UnableSubmitReason` 和 `AuditUnableCommitRecord` 类型，支持下拉框选项

### 任务 2：更新接口定义

**目标**：根据接口文档更新 `src/services/index.ts` 中的接口定义

**执行步骤**：

1. ✅ 已完成：引入新的类型定义
2. ✅ 已完成：更新 `queryLocalOptEspWoosOrder` 接口返回类型
3. ✅ 已完成：更新 `saveUnableCommitReason` 和 `queryUnableCommitReason` 接口类型

### 任务 3：添加 ESP 工单提报模块注释

**目标**：在代码中补充注释，注明该模块是 ESP 工单提报模块

**执行步骤**：

1. 在 `MerchantAuditForm.tsx` 文件顶部添加模块说明注释
2. 在关键功能函数添加注释说明

### 任务 4：实现角色判断和提报次数限制逻辑

**目标**：基于人店关系数据判断用户角色，实现运维 1/运维 2 的不同提报限制

**执行步骤**：

1. 创建角色判断工具函数 `getUserRole`
2. 创建提报次数检查逻辑 `checkSubmitPermission`
3. 在 `MerchantAuditForm` 组件中集成角色判断逻辑
4. 实现运维 1 的一次提报限制（提报后禁用所有逻辑）
5. 实现运维 2 的多次提报提示（第二次之后弹窗提示）

### 任务 5：实现商家分版本判断和延迟提示

**目标**：根据商家质量分版本显示不同的提示文案

**执行步骤**：

1. 创建商家分版本判断函数 `getMerchantScoreVersion`
2. 在提报审核按钮点击时添加版本判断逻辑
3. 对于 1.0 分值显示延迟提示弹窗
4. 对于 3.0LV 级别不显示提示

### 任务 6：更新质检结果不通过时的文案

**目标**：更改再次提交审核时的确认文案

**执行步骤**：

1. 注释原有的确认文案
2. 添加新的确认文案："请确认，原审核工单不申诉后，再次提报审核"
3. 保持原有的确认逻辑不变

### 任务 7：实现提报完成后自动收起卡片

**目标**：提报成功后自动将卡片收起

**执行步骤**：

1. 在 `handleSubmit` 成功回调中添加 `setExpanded(false)` 逻辑
2. 确保收起逻辑在提报成功后立即执行

### 任务 8：显示上次质检结果信息

**目标**：在卡片内左侧最下方显示上次质检结果和时间

**执行步骤**：

1. 创建质检结果显示组件 `InspectionResultInfo`
2. 根据 `shopInspectionInfo` 数据渲染质检结果
3. 格式：`上次质检结果/时间：通过/2025-01-01 15:30`
4. 添加查看详情链接，复用原有的跳转逻辑
5. 将组件放置在卡片内左侧最下方，右对齐

### 任务 9：重构 ReasonModal 组件支持下拉框多选

**目标**：将"卡点待解决"从文本框改为下拉框多选

**执行步骤**：

1. 更新 `ReasonModal` 组件，添加下拉框选项数据获取
2. 将 `processBarrier` 字段改为 `Select` 组件，支持多选
3. 保留原有的 `lackMaterial` 文本框
4. 更新表单提交逻辑，支持新的数据结构
5. 添加选项数据的加载状态处理

### 任务 10：集成所有功能到主组件

**目标**：将所有新功能集成到 `MerchantAuditForm` 主组件中

**执行步骤**：

1. 集成角色判断和权限控制逻辑
2. 集成商家分版本判断和提示逻辑
3. 集成质检结果显示组件
4. 更新组件状态管理，支持新的数据流
5. 确保所有功能协调工作

### 任务 11：添加埋点

**目标**：为新增的交互行为添加埋点

**执行步骤**：

1. 为运维 2 的提示弹窗添加曝光和点击埋点
2. 为商家分延迟提示弹窗添加曝光和点击埋点
3. 为质检结果查看详情链接添加点击埋点
4. 为下拉框选择操作添加埋点

## 技术要点

### 数据流设计

1. 通过 `queryLocalOptEspWoosOrder` 接口获取完整的审核数据
2. 基于 `shopStaffRelationInfo` 判断用户角色
3. 基于 `merchantScoreInfo` 判断商家分版本
4. 基于 `shopInspectionInfo` 显示质检结果

### 组件拆分

1. `InspectionResultInfo` - 质检结果显示组件
2. `RolePermissionChecker` - 角色权限检查工具
3. `MerchantScoreChecker` - 商家分版本检查工具
4. 更新 `ReasonModal` - 支持下拉框多选

### 状态管理

1. 新增 `userRole` 状态管理用户角色
2. 新增 `submitCount` 状态管理提报次数
3. 新增 `merchantScoreVersion` 状态管理商家分版本
4. 保持现有状态结构，避免破坏性变更

## 注意事项

1. 所有新功能都要保持向后兼容
2. 接口数据可能为空，需要做好容错处理
3. 用户体验要流畅，避免频繁的弹窗提示
4. 代码要有充分的注释，便于后续维护
5. 需要考虑不同角色和场景下的边界情况
