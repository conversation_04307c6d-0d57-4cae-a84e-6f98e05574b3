# 埋点文档

## 概述

本文档记录了运维端到端优化需求[第二批]中新增的所有埋点，包括埋点的触发时机、埋点 key、参数说明等。

## 埋点实现方式

所有埋点均使用项目中统一的埋点工具函数：

- 曝光埋点：`traceExp(eventName, ...params)`
- 点击埋点：`traceClick(eventName, ...params)`

## 埋点清单

### 1. 今日必做任务和超时任务

#### 1.1 任务曝光埋点

**触发时机**：任务列表数据加载成功后

**埋点位置**：`src/pages/index/components/todo-list/index.tsx`

**埋点代码**：

```typescript
traceExp('todo_task_exposure', task.taskNo);
```

**参数说明**：

- `eventName`: `'todo_task_exposure'`
- `taskNo`: 任务编号，从接口返回的任务数据中获取

#### 1.2 任务点击埋点

**触发时机**：点击任务卡片时

**埋点位置**：`src/pages/index/components/todo-list/index.tsx`

**埋点代码**：

```typescript
traceClick('todo_task_click', task.taskNo);
```

**参数说明**：

- `eventName`: `'todo_task_click'`
- `taskNo`: 任务编号，从被点击的任务数据中获取

### 2. 商户任务列表

#### 2.1 商户任务曝光埋点

**触发时机**：商户列表数据加载成功后

**埋点位置**：`src/pages/index/components/merchant-task-table/index.tsx`

**埋点代码**：

```typescript
traceExp('merchant_task_exposure', item.pid, item.priorityTaskInfo.taskNo);
```

**参数说明**：

- `eventName`: `'merchant_task_exposure'`
- `pid`: 商户 ID，从商户数据中获取
- `taskNo`: 任务编号，从商户的优先任务信息中获取 `item.priorityTaskInfo.taskNo`

**注意**：只有当商户存在优先任务信息时才会触发埋点

#### 2.2 去完成点击埋点

**触发时机**：点击商户列表中的"去完成"按钮时

**埋点位置**：`src/pages/index/components/merchant-task-table/index.tsx`

**埋点代码**：

```typescript
traceClick('merchant_task_complete_click', record.pid, record.priorityTaskInfo.taskNo);
```

**参数说明**：

- `eventName`: `'merchant_task_complete_click'`
- `pid`: 商户 ID，从被点击行的商户数据中获取
- `taskNo`: 任务编号，从被点击行的优先任务信息中获取

**注意**：只有当商户存在优先任务信息时才会触发埋点

### 3. 商户列表抽屉页

#### 3.1 高优任务曝光埋点

**触发时机**：任务详情抽屉页数据加载成功后

**埋点位置**：`src/pages/index/components/task-detail-drawer/index.tsx`

**埋点代码**：

```typescript
traceExp('high_priority_task_exposure', pid, res?.maxPriorityTask?.taskNo);
```

**参数说明**：

- `eventName`: `'high_priority_task_exposure'`
- `pid`: 商户 ID，从抽屉页传入的参数中获取
- `taskNo`: 高优任务编号，从接口返回的最高优先级任务数据中获取

**注意**：只有当存在最高优先级任务时才会触发埋点

#### 3.2 任务卡片曝光埋点

**触发时机**：任务详情抽屉页数据加载成功后

**埋点位置**：`src/pages/index/components/task-detail-drawer/index.tsx`

**埋点代码**：

```typescript
traceExp('task_exposure', pid, task.taskNo);
```

**参数说明**：

- `eventName`: `'task_exposure'`
- `pid`: 商户 ID，从抽屉页传入的参数中获取
- `taskNo`: 任务编号，从任务列表中的每个任务数据中获取

**注意**：会为任务列表中的每个任务都触发一次曝光埋点

#### 3.3 去完成点击埋点

**触发时机**：点击高优任务的"去完成"按钮时

**埋点位置**：`src/pages/index/components/task-detail-drawer/index.tsx`

**埋点代码**：

```typescript
traceClick('high_priority_task_complete_click', pid, maxPeriorityTask.taskNo);
```

**参数说明**：

- `eventName`: `'high_priority_task_complete_click'`
- `pid`: 商户 ID，从抽屉页传入的参数中获取
- `taskNo`: 高优任务编号，从最高优先级任务数据中获取

#### 3.4 任务卡片点击埋点

**触发时机**：点击任务卡片右箭头时

**埋点位置**：`src/pages/index/components/task-detail-drawer/card.tsx`

**埋点代码**：

```typescript
traceClick('task_card_click', pid, task.taskNo);
```

**参数说明**：

- `eventName`: `'task_card_click'`
- `pid`: 商户 ID，从父组件传入的参数中获取
- `taskNo`: 任务编号，从被点击的任务数据中获取

### 4. 广告投放方案下载表格

#### 4.1 下载表格点击埋点

**触发时机**：点击"下载表格"按钮并成功下载后

**埋点位置**：`src/components/ad-plan/modal.tsx`

**埋点代码**：

```typescript
traceClick('adPlan_downloadExcel', merchantId);
```

**参数说明**：

- `eventName`: `'adPlan_downloadExcel'`
- `merchantId`: 商户 ID，从组件传入的参数中获取

### 5. 核心任务完成率卡片

#### 5.1 卡片曝光埋点

**触发时机**：核心任务完成率数据加载成功后

**埋点位置**：`src/components/completion-rate-card/core-task.tsx`

**埋点代码**：

```typescript
traceExp('core_task_completion', 'card_show');
```

**参数说明**：

- `eventName`: `'core_task_completion'`
- `module`: `'card_show'` - 固定值，表示卡片展示

#### 5.2 卡片点击埋点

**触发时机**：点击核心任务完成率卡片时

**埋点位置**：`src/components/completion-rate-card/core-task.tsx`

**埋点代码**：

```typescript
traceClick('core_task_completion', 'card_click', code, name);
```

**参数说明**：

- `eventName`: `'core_task_completion'`
- `action`: `'card_click'` - 固定值，表示卡片点击
- `code`: 卡片编码，从卡片数据中获取
- `name`: 卡片名称，从卡片数据中获取

## 数据流向

所有埋点数据通过 `src/utils/trace.tsx` 中的统一方法进行上报，最终会调用底层的埋点 SDK 进行数据收集和上报。

## 注意事项

1. 所有埋点都会进行参数有效性检查，只有在必要参数存在时才会触发
2. 曝光埋点通常在数据加载成功的回调中触发，确保数据的准确性
3. 点击埋点在用户操作的处理函数开始时触发，确保及时记录用户行为
4. 商户相关的埋点都会上报 `pid` 参数，任务相关的埋点都会上报 `taskNo` 参数

## 验证方法

可以通过浏览器开发者工具的网络面板或控制台查看埋点是否正常触发和上报。
