# 执行计划

## 任务优先级和依赖关系分析

1. 首先需要完成"完成率指标卡片"组件的开发，因为它是后续任务的基础组件
2. 然后进行商户列表相关的改动，包括筛选项调整和接口修改
3. 接着实现埋点需求
4. 最后处理其他功能性需求

## 具体任务拆解

### 任务 1：抽离和开发"完成率指标卡片"组件

1. 在 `src/components` 下创建 `completion-rate-card` 目录
2. 创建 `index.tsx` 文件，实现完成率指标卡片组件
   ```typescript
   interface ICompletionRateCardProps {
     title: string;
     rate: string;
     onClick?: () => void;
   }
   ```
3. 参考 `src/pages/index/components/business-target/index.tsx` 中的 `TargetCard` 相关代码
4. 使用 styled-components 实现样式
5. 实现点击事件处理

### 任务 2：修改 business-target 组件

1. 在 `src/pages/index/components/business-target/index.tsx` 中：
   - 引入新的完成率指标卡片组件
   - 调用 `getTaskCompletionRate` 接口获取数据
   - 使用 `useAction` 控制展示隐藏
2. 实现点击事件，参考应充预警的逻辑跳转到商户列表
3. 添加埋点：
   - 曝光埋点：上报小二信息
   - 点击埋点：上报小二信息

### 任务 3：商户列表筛选项调整

1. 在商户列表页面中：
   - 删除「商业化投放」筛选项
   - 在「商户标签」前增加「商户推荐分析」下拉筛选项
2. 修改商户列表接口调用，增加 `highPotentialValues` 参数
3. 更新商户列表类型定义，确保支持新的筛选项

### 任务 4：广告投放方案-下载表格功能

1. 调研并引入 `@syncfusion/ej2-excel-export` 包
2. 在广告投放方案页面：
   - 添加"下载表格"按钮
   - 实现表格数据导出功能
   - 处理超过 20 家门店的场景：
     - 分页展示数据
     - 超过 20 家时"下载图片"按钮置灰
     - "下载表格"按钮固定显示

### 任务 5：埋点实现

1. 今日必做任务和超时任务埋点：

   ```typescript
   // 曝光埋点
   trackExposure({
     taskNo: string;
   });

   // 点击埋点
   trackClick({
     taskNo: string;
   });
   ```

2. 商户任务列表埋点：

   ```typescript
   // 曝光埋点
   trackExposure({
     pid: string;
     taskNo: string;
   });

   // 点击埋点
   trackClick({
     pid: string;
     taskNo: string;
   });
   ```

3. 商户列表抽屉页埋点：

   ```typescript
   // 高优任务曝光
   trackExposure({
     pid: string;
     taskNo: string;
   });

   // 任务卡片曝光
   trackExposure({
     pid: string;
     taskNo: string;
   });

   // 去完成点击
   trackClick({
     pid: string;
     taskNo: string;
   });

   // 其他任务卡片点击
   trackClick({
     pid: string;
     taskNo: string;
   });
   ```

### 任务 6：删除"运维门店分配"菜单

1. 联系梦瑶删除对应配置（代码不需要改动）

## 待确认事项

1. 任务完成率接口的具体接口名称需要和后端确认
2. 商户列表页面的筛选项调整可能需要 UI 设计稿确认
3. 广告投放方案下载表格功能的具体字段和格式需要确认

## 开发顺序建议

1. 完成率指标卡片组件（任务 1）
2. 商户列表筛选项调整（任务 3）
3. business-target 组件改造（任务 2）
4. 广告投放方案下载功能（任务 4）
5. 埋点实现（任务 5）
6. 删除菜单配置（任务 6）

这个顺序考虑了以下因素：

- 组件依赖关系
- 功能复杂度
- 对用户体验的影响
- 与后端的协作需求
