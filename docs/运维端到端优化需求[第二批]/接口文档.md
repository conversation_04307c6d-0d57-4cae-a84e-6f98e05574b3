# 接口文档

## 1. [修改] 商户列表搜索接口

用于获取商户列表数据，支持潜力广告主筛选。

```typescript
interface ICommonOperatorInfo {
  amapUid: string;
  comId: string;
  operatorId: string;
  operatorName: string;
  operatorNickName: string;
  operatorType: string;
  source: string;
  workId: string;
}

interface IPageInfo {
  pageNo: number;
  pageSize: number;
}

interface IMerchantLevel {
  code: string;
  name: string;
}

interface IPriorityTaskInfo {
  gmtExpired: number;
  manageLimit: number;
  recommendAnalysis: string;
  taskDesc: string;
  taskName: string;
  taskNo: number;
  taskStatus: string;
  taskStatusText: string;
}

interface IServiceProgress {
  code: string;
  completedTaskCnt: string;
  name: string;
  showDetail: boolean;
  taskCnt: string;
}

interface IServiceSatisfaction {
  code: string;
  name: string;
  score: string;
}

interface IMerchantListItem {
  adCurrentBalance: string;
  adCurrentMonthCost: string;
  adRechargeCount: string;
  infrastructStaffName: string;
  mainShopName: string;
  merchantLevel: IMerchantLevel;
  highPotentialValues: string[]; // 新增字段：商户推荐分析标签
  merchantName: string;
  optGroupCanReach: boolean;
  optGroupName: string;
  pid: string;
  priorityTaskInfo: IPriorityTaskInfo;
  serviceProgress: IServiceProgress;
  serviceSatisfaction: IServiceSatisfaction;
  showCallButton: boolean;
  staffName: string;
}

export const queryAgentOperationMerchantList = (params: {
  commonOperatorInfo: ICommonOperatorInfo;
  highPotentialValues?: string[]; // 新增字段：商户推荐分析筛选
  filterOptRelation: boolean;
  gatewaySource: string;
  hasOptGroup: boolean;
  limit: number;
  offset: number;
  page: IPageInfo;
  requestId: string;
  source: string;
}): Promise<{
  dataList: IMerchantListItem[];
  pageInfo: {
    currentPageNo: number;
    hasMore: boolean;
    nextPageNo: number;
    pageSize: number;
    totalCount: number;
    totalPage: number;
  };
}> => {
  return gdRequest(
    'com.amap.sales.operation.client.AgentOperationQueryFacade.queryAgentOperationMerchantList',
    params,
  );
};
```

## 2. [新增] 获取任务完成率接口

用于获取首页核心任务完成率数据，包括广告续充、广告首续、年费续签三个指标的完成率。

```typescript
interface ITaskRate {
  name: string;
  code: string;
  finishRate: string;
}

// 待确认：接口名称未提供，需要和后端确认
export const getTaskCompletionRate = (): Promise<{
  rate: ITaskRate[];
}> => {
  return gdRequest('待确认接口名称', {});
};
```
