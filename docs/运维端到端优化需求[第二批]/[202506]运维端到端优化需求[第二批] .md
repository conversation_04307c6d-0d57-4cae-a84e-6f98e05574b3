# 需求系分

## 埋点需求

### 今日必做任务和超时任务

要求：

- 所有下发的任务需要进行曝光埋点，上报 taskNo
- 点击单任务需要点击埋点，上报 taskNo

### 商户任务列表

要求

- 当前页所有下发的商户进行曝光埋点，上报 pid  和  taskNo
- 点击商户列表的去完成，增加点击埋点，上报 pid  和  taskNo

### 商户列表去完成对应的抽屉页

- 上报高优任务的曝光埋点，上报  pid  和高优任务的 taskNo
- 上报其他所有任务卡片的曝光埋点
- 点击去完成的点击埋点，上报 pid  和 taskNo
- 点击其他任务卡片右箭头的点击埋点，上报 pid  和 taskNo

## 首页增加  "核心任务完成率"模块

### 前置准备工作

#### 抽离一个“完成率指标卡片”组件

参考  src/pages/index/components/business-target/index.tsx 中  TargetCard  相关代码，在  src/components  下新增一个完成率指标卡片的组件，注意 props 的定义和传递

#### 修改 business-target

复用完成率指标卡片修改原来的代码

### 核心任务完成率

### 展示条件

用 useAction  来控制展示隐藏

### 模块具体说明

包括以下指标：

- 广告续充任务完成率
- 广告首续任务完成率
- 年费续签任务完成率

交互和样式

模块展示在首页最上方

颜色：不区分颜色，都展示为蓝色

埋点：曝光+点击，上报小二信息

点击：带参数查商户列表,  参考点击应充预警的逻辑

## 商户列表筛选项调整

### 增加"商户推荐分析"下拉筛选项

- 筛选项位置，展示在「商户标签」这个筛选项前面
- 可选项：潜力广告主

###   删除「商业化投放」筛选项

## 广告投放方案

增加“下载表格”按钮，支持下载本次所选全部门店的投放数据

用前端工具下载，list  转 excel，可以调研一下这个包  @syncfusion/ej2-excel-export

1.  支持选择超过 20 家门店，页面默认最多展示 20 家门店的数据

    1.  超过 20 家时，页面分页展示数据，「下载图片」按钮置灰

2.  固定展示「下载表格」按钮，点击后下载所有本次已选门店的，页面上展示的内容，支持部分字段的非必填下载（原因：如一次选择了 200 家门店，在 excel 表格里操作修改更便捷）

## 删除“运维门店分配”菜单

找梦瑶删除一下对应配置，代码不需要改动

# 接口系分

### 商户列表搜索

接口名：com.amap.sales.operation.client.AgentOperationQueryFacade#queryAgentOperationMerchantList

入参：新增 highPotentialValues 字段，输入  list

```json-ld
 {
        "commonOperatorInfo": {
            "amapUid": "1644202647",
            "comId": "1788",
            "operatorId": "3022000000020324",
            "operatorName": "张家和",
            "operatorNickName": "",
            "operatorType": "INNER",
            "source": "XY",
            "workId": "WB01986032"
        },

        "highPotentialValues":[
          "1"
        ]
        "filterOptRelation": false,
        "gatewaySource": "AMAP_GATEWAY",
        "hasOptGroup": false,
        "limit": 10,
        "offset": 0,
        "page": {
            "pageNo": 1,
            "pageSize": 10
        },
        "requestId": "acde9870-bc25-3a0a-baaf-dee3027c5a5d",
        "source": "BASE_MERCHANT_LIST"
    }
```

出参：新增 highPotentialValues  字段

```json-ld
{
    "code": "1",
    "data": {
        "dataList": [
            {

                "adCurrentBalance": "100000.0",
                "adCurrentMonthCost": "0.0",
                "adRechargeCount": "3",
                "infrastructStaffName": "zb预发xy新链路 ",
                "mainShopName": "眉山预发1",
                "merchantLevel": {
                    "code": "NEW_SIGN",
                    "name": "新签"
                },
                "highPotentialValues":[
                    "1"
                ],
                "merchantName": "Abc",
                "optGroupCanReach": false,
                "optGroupName": "婚纱北京(仅供测试)&高德运维对接群&2088421251799361",
                "pid": "2088421251799361",
                "priorityTaskInfo": {
                    "gmtExpired": 4102415999000,
                    "manageLimit": 1745423999000,
                    "recommendAnalysis": "预估带来增量收入666元",
                    "taskDesc": "广告余额仅剩--元，预计可投放--天",
                    "taskName": "余额预警",
                    "taskNo": 2504160652200100000,
                    "taskStatus": "PROCESSING",
                    "taskStatusText": "执行中"
                },
                "serviceProgress": {
                    "code": "INCOMPLETE",
                    "completedTaskCnt": "5",
                    "name": "未完成",
                    "showDetail": true,
                    "taskCnt": "8"
                },
                "serviceSatisfaction": {
                    "code": "EXCELLENCE",
                    "name": "优秀",
                    "score": "5"
                },
                "showCallButton": true,
                "staffName": "张彪"
            }
        ],
        "pageInfo": {
            "currentPageNo": 1,
            "hasMore": false,
            "nextPageNo": 2,
            "pageSize": 10,
            "totalCount": 1,
            "totalPage": 1
        }
    },
    "msgCode": "SUCCESS",
    "msgInfo": "调用成功",
    "result": true,
    "success": true,
    "timestamp": "1748487947072",
    "traceId": "0bfb408b17484879466111813e178c",
    "version": "1.0"
}
```

### 任务完成率

接口名：

入参：

```json-ld
没有入参
```

出参：

```json-ld
{
    "code": "1",
    "data": {
        "rate": [
            {
                "name": "广告续充任务完成率",
                "code": "",
                "finishRate": "0.3232"
            },
            {
                "name": "广告首续任务完成率",
                "code": "",
                "finishRate": "0.3232"
            },
            {
                "name": "年费续签任务完成率",
                "code": "",
                "finishRate": "0.3232"
            }
        ]
    },
    "msgCode": "SUCCESS",
    "msgInfo": "调用成功",
    "result": true,
    "success": true,
    "timestamp": "1740488312871",
    "traceId": "21448cc817404883128146169e1262",
    "version": "1.0"
}
```
