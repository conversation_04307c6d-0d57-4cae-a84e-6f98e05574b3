# 代运营埋点需求 - 技术执行计划

## 需求概述

基于【PRD】代运营工作台埋点&数据报表需求，为各个模块添加点击和曝光埋点。

### 埋点原则

- 点击和曝光事件都需要上报
- 曝光事件针对任务级别，对所有的任务做曝光上报（不对模块组件做曝光）
- 上报信息中忽略"小二"（上报工具会自动上报）
- 任务参数使用 `taskNo`，商户参数使用 `pid`

## 任务 1：更新埋点映射表

### 1.1 页面标识说明

所有模块都使用现有的 `PageSPMKey.首页 = 'xy-task-pc-home'`，不需要新增页面标识。

### 1.2 新增埋点事件

在 `ModuleSPMKey` 枚举中添加新的埋点事件（采用模块.功能的层级结构）：

```typescript
export enum ModuleSPMKey {
  // ... 现有枚举

  // 任务管理页面
  '任务管理页面' = 'task_management_page',

  // 核心任务完成率模块
  '核心任务完成率' = 'core_task_completion',
  '核心任务完成率.任务' = 'core_task_completion.task',
  '核心任务完成率.卡片' = 'core_task_completion.card',

  // 柱状图模块
  '柱状图' = 'target_dashboard',
  '柱状图.广告任务' = 'target_dashboard.ad_task',
  '柱状图.年费任务' = 'target_dashboard.annual_task',
  '柱状图.广告Tab' = 'target_dashboard.ad_tab',
  '柱状图.年费Tab' = 'target_dashboard.annual_tab',
  '柱状图.广告柱子' = 'target_dashboard.ad_chart',
  '柱状图.年费柱子' = 'target_dashboard.annual_chart',

  // 待办任务模块
  '待办任务' = 'todo_list',
  '待办任务.任务' = 'todo_list.task',
  '待办任务.紧急待办Tab' = 'todo_list.urgent_tab',
  '待办任务.今日必做Tab' = 'todo_list.today_tab',
  '待办任务.全部待办Tab' = 'todo_list.all_tab',
  '待办任务.任务项' = 'todo_list.task_item',

  // 企微任务模块
  '企微任务' = 'qw_task',
  '企微任务.任务' = 'qw_task.task',
  '企微任务.自动发送' = 'qw_task.auto_send',

  // 商户列表模块
  '商户列表' = 'merchant_list',
  '商户列表.筛选' = 'merchant_list.filter',
  '商户列表.操作项' = 'merchant_list.operation',

  // 门店列表模块
  '门店列表' = 'shop_list',
  '门店列表.筛选' = 'shop_list.filter',
  '门店列表.操作项' = 'shop_list.operation',

  // 喜报模块
  '喜报' = 'business_news',
  '喜报.AI智能分析' = 'business_news.ai_analysis',
  '喜报.字段切换' = 'business_news.field_toggle',
  '喜报.明细' = 'business_news.detail',
  '喜报.下载明细表' = 'business_news.download_table',
  '喜报.下载图片' = 'business_news.download_image',
  '喜报.发到企微群' = 'business_news.send_to_qw',

  // 拜访记录模块
  '拜访记录' = 'visit_record',
  '拜访记录.商户详情Tab' = 'visit_record.merchant_detail_tab',
  '拜访记录.拜访记录Tab' = 'visit_record.history_tab',
  '拜访记录.复盘数据Tab' = 'visit_record.replay_tab',
  '拜访记录.记拜访' = 'visit_record.create',

  // 投放方案模块
  '投放方案' = 'ad_plan',
  '投放方案.操作' = 'ad_plan.operation',
  '投放方案.下载' = 'ad_plan.download',

  // 基建任务模块
  '基建任务' = 'infrastructure_task',
  '基建任务.任务' = 'infrastructure_task.task',
  '基建任务.素材提报' = 'infrastructure_task.material_submit',
  '基建任务.提报审核' = 'infrastructure_task.submit_audit',
  '基建任务.商家分任务' = 'infrastructure_task.merchant_assign',

  // 年费续签任务模块
  '年费续签任务' = 'annual_renewal_task',
  '年费续签任务.任务' = 'annual_renewal_task.task',

  // 其他操作
  '视角切换' = 'viewer_switch',
}
```

## 任务 2：首页埋点实现

### 2.1 任务管理页面曝光埋点

在 `src/pages/index/index.tsx` 中添加页面曝光埋点：

```typescript
import { traceExp, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

// 页面加载时触发曝光埋点
useEffect(() => {
  traceExp(PageSPMKey.首页, ModuleSPMKey.任务管理页面, {});
}, []);
```

### 2.2 核心任务完成率模块埋点

在 `src/components/completion-rate-card/core-task.tsx` 中：

```typescript
// 任务曝光埋点（当任务数据加载时触发）
useEffect(() => {
  if (data && data.length > 0) {
    data.forEach((task) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['核心任务完成率.任务'], {
        taskNo: task.code, // 任务编码作为taskNo
      });
    });
  }
}, [data]);

// 任务点击埋点（在现有的handleCardClick函数中添加）
const handleCardClick = (code: string, name: string) => {
  // 添加点击埋点
  traceClick(PageSPMKey.首页, ModuleSPMKey['核心任务完成率.卡片'], {
    taskNo: code, // 任务编码作为taskNo
  });

  // ... 现有逻辑
};
```

### 2.3 广告/年费柱子模块埋点

在 `src/pages/index/components/target-dashboard/index.tsx` 中：

```typescript
// 任务曝光埋点（当图表数据加载时触发）
useEffect(() => {
  if (data && data.length > 0) {
    data.forEach((item) => {
      const exposureEvent =
        queryType === tabOptions[0].value
          ? ModuleSPMKey['柱状图.广告任务']
          : ModuleSPMKey['柱状图.年费任务'];

      traceExp(PageSPMKey.首页, exposureEvent, {
        taskNo: item.taskNo,
        pid: item.pid,
        indicatorType: form.getFieldValue('indicatorType'),
        contrastPeriod: form.getFieldValue('contrastPeriod'),
        emergencyLevel: item.emergencyLevel,
      });
    });
  }
}, [data, queryType]);

// Tab切换点击埋点
const handleTabChange = (value: string) => {
  const event =
    value === tabOptions[0].value ? ModuleSPMKey['柱状图.广告Tab'] : ModuleSPMKey['柱状图.年费Tab'];

  traceClick(PageSPMKey.首页, event, {
    tabType: value,
    // 根据筛选条件添加其他参数
    indicatorType: form.getFieldValue('indicatorType'),
    contrastPeriod: form.getFieldValue('contrastPeriod'),
    emergencyLevel: form.getFieldValue('emergencyLevel'),
  });

  setQueryType(value);
};

// 柱子点击埋点
const handleChartClick = (data: any) => {
  const event =
    queryType === tabOptions[0].value
      ? ModuleSPMKey['柱状图.广告柱子']
      : ModuleSPMKey['柱状图.年费柱子'];

  traceClick(PageSPMKey.首页, event, {
    taskNo: data.taskNo,
    pid: data.pid,
    indicatorType: form.getFieldValue('indicatorType'),
    contrastPeriod: form.getFieldValue('contrastPeriod'),
    emergencyLevel: form.getFieldValue('emergencyLevel'),
  });
};
```

### 2.4 待办任务模块埋点

在 `src/pages/index/components/todo-list/index.tsx` 中：

```typescript
// 待办任务曝光埋点（当任务列表数据加载时触发）
useEffect(() => {
  if (taskList && taskList.length > 0) {
    taskList.forEach((task) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['待办任务.任务'], {
        taskNo: task.taskNo,
      });
    });
  }
}, [taskList]);

// Tab切换点击埋点（在现有的Radio.Group onChange中添加）
const handleTabChange = (e: any) => {
  const tabValue = e.target.value as TaskType;
  let event;

  switch (tabValue) {
    case TaskType.URGENT:
      event = ModuleSPMKey['待办任务.紧急待办Tab'];
      break;
    case TaskType.TODAY:
      event = ModuleSPMKey['待办任务.今日必做Tab'];
      break;
    case TaskType.ALL:
      event = ModuleSPMKey['待办任务.全部待办Tab'];
      break;
  }

  if (event) {
    traceClick(PageSPMKey.首页, event, {});
  }

  setCurTab(tabValue);
};

// 待办任务点击埋点（更新现有的handleTaskClick函数）
const handleTaskClick = async (task: ITodoTask) => {
  // 点击埋点
  traceClick(PageSPMKey.首页, ModuleSPMKey['待办任务.任务项'], {
    taskNo: task.taskNo,
  });

  // ... 现有逻辑
};
```

### 2.5 企微自动发送限时任务埋点

在企微相关组件中（需要确认具体组件位置）：

```typescript
// 企微任务曝光埋点（当任务数据加载时触发）
useEffect(() => {
  if (qwTasks && qwTasks.length > 0) {
    qwTasks.forEach((task) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['企微任务.任务'], {
        taskNo: task.taskNo,
      });
    });
  }
}, [qwTasks]);

// 任务点击埋点
const handleQwTaskClick = (task: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['企微任务.自动发送'], {
    taskNo: task.taskNo,
  });
};
```

### 2.6 切换视角埋点

在视角切换组件中：

```typescript
const handleViewerSwitch = (viewType: string) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey.视角切换, {
    viewType,
  });
};
```

## 任务 3：商户列表模块埋点

### 3.1 商户列表模块（无需模块曝光埋点）

在 `src/pages/index/components/double-table/merchant/index.tsx` 中：

```typescript
// 商户列表模块主要是展示和操作功能，无需模块曝光埋点
// 只需要在有具体任务时对任务进行曝光
```

### 3.2 筛选变更埋点

在筛选组件中：

```typescript
const handleFilterChange = (filterData: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['商户列表.筛选'], {
    filterType: Object.keys(filterData).join(','),
  });
};
```

### 3.3 操作项点击埋点

在操作按钮组件中：

```typescript
const handleOperationClick = (operation: string, merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['商户列表.操作项'], {
    operation,
    pid: merchant.pid,
  });
};
```

## 任务 4：门店列表模块埋点

### 4.1 门店列表模块（无需模块曝光埋点）

在 `src/pages/index/components/double-table/shop/index.tsx` 中：

```typescript
// 门店列表模块主要是展示和操作功能，无需模块曝光埋点
// 只需要在有具体任务时对任务进行曝光
```

### 4.2 筛选变更埋点

```typescript
const handleShopFilterChange = (filterData: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.筛选'], {
    filterType: Object.keys(filterData).join(','),
  });
};
```

### 4.3 操作项点击埋点

```typescript
const handleShopOperationClick = (operation: string, shop: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.操作项'], {
    operation,
    pid: shop.pid,
  });
};
```

## 任务 5：喜报模块埋点

### 5.1 喜报模块（无需模块曝光埋点）

在 `src/pages/business-news/index.tsx` 中：

```typescript
// 喜报模块主要是展示和操作功能，无需模块曝光埋点
// 只需要对具体的操作行为进行点击埋点
```

### 5.2 AI 智能分析点击埋点

```typescript
const handleAiAnalysisClick = (merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.AI智能分析'], {
    pid: merchant.pid,
  });
};
```

### 5.3 字段隐藏切换埋点

```typescript
const handleFieldToggle = (field: string, merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.字段切换'], {
    field,
    pid: merchant.pid,
  });
};
```

### 5.4 明细点击埋点

```typescript
const handleDetailClick = (merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.明细'], {
    pid: merchant.pid,
  });
};
```

### 5.5 下载明细表点击埋点

在 `src/pages/business-news/components/download-img/index.tsx` 中更新：

```typescript
const handleDownloadDetailTable = (merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.下载明细表'], {
    pid: merchant.pid,
  });
};
```

### 5.6 下载图片点击埋点

更新现有的下载图片埋点：

```typescript
const onClick = () => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.下载图片'], {
    pid: props.pid,
  });

  // ... 现有逻辑
};
```

### 5.7 发到企微群点击埋点

```typescript
const sendToQW = async () => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.发到企微群'], {
    pid: props.pid,
  });

  // ... 现有逻辑
};
```

## 任务 6：记拜访模块埋点

### 6.1 记拜访模块（无需模块曝光埋点）

在拜访相关组件中：

```typescript
// 记拜访模块主要是展示和操作功能，无需模块曝光埋点
// 只需要对具体的操作行为进行点击埋点
```

### 6.2 Tab 点击埋点

```typescript
const handleTabClick = (tabKey: string, merchant: any) => {
  let event;
  switch (tabKey) {
    case 'merchantDetail':
      event = ModuleSPMKey['拜访记录.商户详情Tab'];
      break;
    case 'visitRecord':
      event = ModuleSPMKey['拜访记录.拜访记录Tab'];
      break;
    case 'replayData':
      event = ModuleSPMKey['拜访记录.复盘数据Tab'];
      break;
  }

  if (event) {
    traceClick(PageSPMKey.首页, event, {
      pid: merchant.pid,
    });
  }
};
```

### 6.3 记拜访点击埋点

```typescript
const handleRecordVisitClick = (merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['拜访记录.记拜访'], {
    pid: merchant.pid,
  });
};
```

## 任务 7：投放方案模块埋点

### 7.1 投放方案模块（无需模块曝光埋点）

在 `src/components/ad-plan/modal.tsx` 中：

```typescript
// 投放方案模块主要是展示和操作功能，无需模块曝光埋点
// 只需要对具体的操作行为进行点击埋点
```

### 7.2 投放方案操作曝光埋点

```typescript
const handleAdPlanOperation = () => {
  traceExp(PageSPMKey.首页, ModuleSPMKey['投放方案.操作'], {
    pid: merchantId,
  });
};
```

### 7.3 投放方案下载点击埋点

```typescript
const { loading: downloading, run: download } = useRequest(
  async () => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.下载'], {
      pid: merchantId,
    });
    await ref.current?.saveImg();
  },
  {
    manual: true,
  },
);
```

## 任务 8：基建任务模块埋点

### 8.1 基建任务曝光埋点

在基建任务相关组件中：

```typescript
// 基建任务曝光埋点（当任务数据加载时触发）
useEffect(() => {
  if (infraTasks && infraTasks.length > 0) {
    infraTasks.forEach((task) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['基建任务.任务'], {
        taskNo: task.taskNo,
        pid: task.pid,
      });
    });
  }
}, [infraTasks]);
```

### 8.2 装修素材提报任务点击埋点

```typescript
const handleMaterialSubmitClick = (task: any, merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['基建任务.素材提报'], {
    taskNo: task.taskNo,
    pid: merchant.pid,
  });
};
```

### 8.3 提报审核点击埋点

```typescript
const handleSubmitAuditClick = (task: any, merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['基建任务.提报审核'], {
    taskNo: task.taskNo,
    pid: merchant.pid,
  });
};
```

### 8.4 商家分任务点击埋点

```typescript
const handleMerchantTaskAssignClick = (task: any, merchant: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['基建任务.商家分任务'], {
    taskNo: task.taskNo,
    pid: merchant.pid,
  });
};
```

## 任务 9：年费续签任务埋点

### 9.1 年费续签任务曝光埋点

```typescript
// 年费续签任务曝光埋点（当任务数据加载时触发）
useEffect(() => {
  if (renewalTasks && renewalTasks.length > 0) {
    renewalTasks.forEach((task) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['年费续签任务.任务'], {
        taskNo: task.taskNo,
      });
    });
  }
}, [renewalTasks]);
```

### 9.2 年费续签任务点击埋点

```typescript
const handleAnnualRenewalClick = (task: any) => {
  traceClick(PageSPMKey.首页, ModuleSPMKey['年费续签任务.任务'], {
    taskNo: task.taskNo,
  });
};
```

## 任务执行顺序

1. **第一步**: 更新埋点映射表（任务 1）
2. **第二步**: 实现首页相关模块埋点（任务 2）
3. **第三步**: 实现商户列表模块埋点（任务 3）
4. **第四步**: 实现门店列表模块埋点（任务 4）
5. **第五步**: 实现喜报模块埋点（任务 5）
6. **第六步**: 实现记拜访模块埋点（任务 6）
7. **第七步**: 实现投放方案模块埋点（任务 7）
8. **第八步**: 实现基建任务模块埋点（任务 8）
9. **第九步**: 实现年费续签任务埋点（任务 9）

## 注意事项

1. **曝光埋点时机**:

   - 页面级曝光在页面加载时触发
   - 任务级曝光在任务数据加载时触发（通过 useEffect 监听数据变化）
   - 不对模块组件做曝光埋点，只对具体任务做曝光埋点

2. **参数规范**:

   - 商户参数统一使用 `pid`
   - 任务参数统一使用 `taskNo`
   - 筛选相关参数使用具体的筛选条件字段

3. **埋点工具选择**:

   - 使用 `traceExp()` 进行曝光埋点
   - 使用 `traceClick()` 进行点击埋点
   - 保持与现有埋点方案的一致性

4. **错误处理**:

   - 埋点不应影响业务逻辑
   - 建议在埋点代码周围添加 try-catch

5. **测试验证**:
   - 在开发环境验证埋点数据格式
   - 确认埋点事件名称与枚举定义一致
