---
title: 代运营埋点需求 - 埋点文档
---

**埋点统计**：

- 共计 **60** 个埋点事件
- 涵盖 **11** 个主要业务模块

## 埋点清单

### 1. 首页核心模块

#### 1.1 页面级埋点

**[曝光] 任务管理页面曝光**

- **埋点 key**: `amap.xy-task-pc-home.task_management_page`
- **触发时机**: 首页加载时
- **参数**: 无

---

#### 1.2 核心任务完成率模块

**[曝光] 核心任务完成率任务曝光**

- **埋点 key**: `amap.xy-task-pc-home.core_task_completion.task`
- **触发时机**: 核心任务数据加载成功后，为每个任务触发
- **参数**: `{name}`

**[点击] 核心任务完成率卡片点击**

- **埋点 key**: `amap.xy-task-pc-home.core_task_completion.card`
- **触发时机**: 点击核心任务完成率卡片时
- **参数**: `{name}`

---

#### 1.3 柱状图模块

**[曝光] 广告任务曝光**

- **埋点 key**: `amap.xy-task-pc-home.target_dashboard.ad_task`
- **触发时机**: 广告 tab 下数据展示时
- **参数**: `{pid, indicatorType, contrastPeriod, emergencyLevel}`

**[曝光] 年费任务曝光**

- **埋点 key**: `amap.xy-task-pc-home.target_dashboard.annual_task`
- **触发时机**: 年费 tab 下数据展示时
- **参数**: `{pid, indicatorType, contrastPeriod, emergencyLevel}`

**[点击] 广告 Tab 切换**

- **埋点 key**: `amap.xy-task-pc-home.target_dashboard.ad_tab`
- **触发时机**: 切换到广告 tab 时
- **参数**: `{tabType, indicatorType, contrastPeriod, emergencyLevel}`

**[点击] 年费 Tab 切换**

- **埋点 key**: `amap.xy-task-pc-home.target_dashboard.annual_tab`
- **触发时机**: 切换到年费 tab 时
- **参数**: `{tabType, indicatorType, contrastPeriod, emergencyLevel}`

**[点击] 广告柱状图点击**

- **埋点 key**: `amap.xy-task-pc-home.target_dashboard.ad_chart`
- **触发时机**: 点击广告 tab 下的柱状图时
- **参数**: `{pid, indicatorType, contrastPeriod, emergencyLevel}`

**[点击] 年费柱状图点击**

- **埋点 key**: `amap.xy-task-pc-home.target_dashboard.annual_chart`
- **触发时机**: 点击年费 tab 下的柱状图时
- **参数**: `{pid, indicatorType, contrastPeriod, emergencyLevel}`

---

#### 1.4 待办任务模块

**[曝光] 待办任务曝光**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.task`
- **触发时机**: 待办任务列表数据加载成功后，为每个任务触发
- **参数**: `{taskNo}`

**[曝光] 广告任务数据曝光**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.ad_task`
- **触发时机**: 广告任务数据加载成功后，为每个任务触发
- **参数**: `{taskName}`

**[曝光] 续签任务数据曝光**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.renewal_task`
- **触发时机**: 续签任务数据加载成功后，为每个任务触发
- **参数**: `{taskName}`

**[曝光] 预警任务数据曝光**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.warning_task`
- **触发时机**: 预警任务数据加载成功后，为每个任务触发
- **参数**: `{value, taskName}`

**[曝光] 基建任务数据曝光**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.infrastructure_task`
- **触发时机**: 基建任务数据加载成功后，为每个任务触发
- **参数**: `{value, taskName}`

**[点击] 紧急待办 Tab**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.urgent_tab`
- **触发时机**: 切换到紧急待办 tab 时
- **参数**: 无

**[曝光] 紧急待办任务数据**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.urgent_data`
- **触发时机**: 紧急待办 tab 数据加载成功后，为每个任务触发
- **参数**: `{taskNo, taskType, priority, deadline}`

**[点击] 今日必做 Tab**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.today_tab`
- **触发时机**: 切换到今日必做 tab 时
- **参数**: 无

**[曝光] 今日必做任务数据**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.today_data`
- **触发时机**: 今日必做 tab 数据加载成功后，为每个任务触发
- **参数**: `{taskNo, taskType, priority, deadline}`

**[点击] 全部待办 Tab**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.all_tab`
- **触发时机**: 切换到全部待办 tab 时
- **参数**: 无

**[曝光] 全部待办任务数据**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.all_data`
- **触发时机**: 全部待办 tab 数据加载成功后，为每个任务触发
- **参数**: `{taskNo, taskType, priority, deadline}`

**[点击] 待办任务项点击**

- **埋点 key**: `amap.xy-task-pc-home.todo_list.task_item`
- **触发时机**: 点击待办任务列表中的任务项时
- **参数**: `{taskNo}`

---

#### 1.5 企微任务模块

**[曝光] 企微任务曝光**

- **埋点 key**: `amap.xy-task-pc-home.qw_task.task`
- **触发时机**: 企微任务数据加载成功后，为每个任务触发
- **参数**: `{scene}`

**[点击] 企微自动发送点击**

- **埋点 key**: `amap.xy-task-pc-home.qw_task.auto_send`
- **触发时机**: 点击企微自动发送任务时
- **参数**: `{scene}`

---

#### 1.6 商户列表模块

**[点击] 商户筛选变更**

- **埋点 key**: `amap.xy-task-pc-home.merchant_list.filter`
- **触发时机**: 商户筛选条件变更时
- **参数**: `{filterType}`

**[点击] 商户操作项点击**

- **埋点 key**: `amap.xy-task-pc-home.merchant_list.operation`
- **触发时机**: 点击商户列表中的操作按钮时
- **参数**: `{operation, pid}`

---

#### 1.7 门店列表模块

**[点击] 门店筛选变更**

- **埋点 key**: `amap.xy-task-pc-home.shop_list.filter`
- **触发时机**: 门店筛选条件变更时
- **参数**: `{filterType}`

**[点击] 门店操作项点击**

- **埋点 key**: `amap.xy-task-pc-home.shop_list.operation`
- **触发时机**: 点击门店列表中的操作按钮时
- **参数**: `{operation, pid}`

---

#### 1.8 视角切换

**[点击] 视角切换**

- **埋点 key**: `amap.xy-task-pc-home.viewer_switch`
- **触发时机**: 切换视角时
- **参数**: `{viewType}`

---

### 2. 喜报模块

**[点击] AI 智能分析**

- **埋点 key**: `amap.xy-task-pc-home.business_news.ai_analysis`
- **触发时机**: 点击 AI 智能分析按钮时
- **参数**: `{pid}`

**[点击] 字段切换**

- **埋点 key**: `amap.xy-task-pc-home.business_news.field_toggle`
- **触发时机**: 切换字段显示/隐藏状态时
- **参数**: `{field, action, pid}`

**[点击] 明细查看**

- **埋点 key**: `amap.xy-task-pc-home.business_news.detail`
- **触发时机**: 点击查看明细时
- **参数**: `{pid}`

**[点击] 下载明细表**

- **埋点 key**: `amap.xy-task-pc-home.business_news.download_table`
- **触发时机**: 点击下载明细表时
- **参数**: `{pid}`

**[点击] 下载图片**

- **埋点 key**: `amap.xy-task-pc-home.business_news.download_image`
- **触发时机**: 点击下载图片按钮时
- **参数**: `{pid}`

**[点击] 发到企微群**

- **埋点 key**: `amap.xy-task-pc-home.business_news.send_to_qw`
- **触发时机**: 点击发送到企微群按钮时
- **参数**: `{pid}`

---

### 3. 拜访记录模块

**[点击] Tab 切换**

- **埋点 key**: `amap.xy-task-pc-home.visit_record.tab_switch`
- **触发时机**: 切换拜访记录相关 Tab 时
- **参数**: `{tabKey, pid}`

**[点击] 继续填写拜访记录**

- **埋点 key**: `amap.xy-task-pc-home.visit_record.continue_fill`
- **触发时机**: 点击继续填写拜访记录按钮时
- **参数**: `{planId}`

---

### 4. 投放方案模块

**[曝光] 投放方案模块曝光**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.module_exposure`
- **触发时机**: 打开投放方案弹窗时
- **参数**: `{pid}`

**[点击] 投放方案查询**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.query`
- **触发时机**: 点击查询按钮时
- **参数**: `{pid}`

**[点击] 投放方案新增**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.create`
- **触发时机**: 点击新增投放方案时
- **参数**: `{pid, productType}`

**[点击] 投放方案删除**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.delete`
- **触发时机**: 点击删除投放方案时
- **参数**: `{pid, shopId, productType}`

**[点击] 关闭修改**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.close_edit`
- **触发时机**: 点击关闭修改按钮时
- **参数**: `{pid}`

**[点击] 移除门店**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.remove_shop`
- **触发时机**: 移除门店时
- **参数**: `{pid, shopId}`

**[点击] 下载 Excel**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.download_excel`
- **触发时机**: 点击下载 Excel 按钮时
- **参数**: `{pid}`

**[点击] 下载图片**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.download_image`
- **触发时机**: 点击下载图片按钮时
- **参数**: `{pid}`

**[点击] 发送至企微群**

- **埋点 key**: `amap.xy-task-pc-home.ad_plan.send_to_qw`
- **触发时机**: 点击发送至企微群按钮时
- **参数**: `{pid}`

---

### 5. 基建任务模块

**[曝光] 基建任务模块曝光**

- **埋点 key**: `amap.xy-task-pc-home.infrastructure_task.module_exposure`
- **触发时机**: 打开基建任务详情页面时
- **参数**: `{shopId, taskNo}`

**[点击] 基建任务卡片点击**

- **埋点 key**: `amap.xy-task-pc-home.infrastructure_task.task_card_click`
- **触发时机**: 点击基建任务卡片时
- **参数**: `{shopId, taskName, taskNo}`

**[曝光] 装修素材提报曝光**

- **埋点 key**: `amap.xy-task-pc-home.infrastructure_task.decoration_material_submit`
- **触发时机**: 装修素材提报组件展示时
- **参数**: `{shopId}`

**[曝光] 装修素材提报曝光**

- **埋点 key**: `amap.xy-task-pc-home.infrastructure_task.decoration_material_submit`
- **触发时机**: 装修素材提报组件展示时
- **参数**: `{shopId, availableMaterials}`

**[点击] 装修素材提报点击**

- **埋点 key**: `amap.xy-task-pc-home.infrastructure_task.decoration_material_submit`
- **触发时机**: 点击装修素材提报按钮时
- **参数**: `{shopId, submittedMaterials, shopName}`

**[曝光] 提报审核曝光**

- **埋点 key**: `amap.xy-task-pc-home.infrastructure_task.submit_audit`
- **触发时机**: 提报审核组件展示时
- **参数**: `{shopId}`

**[点击] 提报审核点击**

- **埋点 key**: `amap.xy-task-pc-home.infrastructure_task.submit_audit`
- **触发时机**: 点击提报审核按钮时
- **参数**: `{shopId}`

---

### 6. 年费续签任务模块

**[曝光] 年费续签任务模块曝光**

- **埋点 key**: `amap.xy-task-pc-home.annual_renewal_task.module_exposure`
- **触发时机**: 打开年费续签任务详情页面时
- **参数**: `{shopId}`

**[点击] 年费续签任务卡片点击**

- **埋点 key**: `amap.xy-task-pc-home.annual_renewal_task.task_card_click`
- **触发时机**: 点击年费续签任务卡片时
- **参数**: `{shopId, taskName}`

**[曝光] 年费续签任务详情曝光**

- **埋点 key**: `amap.xy-task-pc-home.annual_renewal_task.task_detail_exposure`
- **触发时机**: 年费续签任务详情展示时
- **参数**: `{taskName, taskStatus, completed}`

**[点击] 年费续签任务操作**

- **埋点 key**: `amap.xy-task-pc-home.annual_renewal_task.task_operation`
- **触发时机**: 点击年费续签任务操作按钮时
- **参数**: `{taskName, buttonText}`
