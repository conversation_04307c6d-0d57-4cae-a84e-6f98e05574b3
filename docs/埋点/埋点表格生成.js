const fs = require('fs');
const path = require('path');

// ==================== 第一步：导出 traceMap 到埋点事件清单 ====================

// 读取 traceMap.ts 文件
const traceMapPath = path.join(__dirname, '../../src/utils/trace/traceMap.ts');
const traceMapContent = fs.readFileSync(traceMapPath, 'utf8');

// 解析 ModuleSPMKey 枚举
const moduleSPMKeyMatch = traceMapContent.match(/export enum ModuleSPMKey \{([\s\S]*?)\}/);
if (!moduleSPMKeyMatch) {
  console.error('无法找到 ModuleSPMKey 枚举');
  process.exit(1);
}

const enumContent = moduleSPMKeyMatch[1];

// 解析枚举项
const enumItems = [];
const lines = enumContent.split('\n');
let currentComment = '';

for (const line of lines) {
  const trimmedLine = line.trim();

  // 跳过空行和结束括号
  if (!trimmedLine || trimmedLine === '}') continue;

  // 检查是否是注释
  if (trimmedLine.startsWith('//')) {
    currentComment = trimmedLine.replace('//', '').trim();
    continue;
  }

  // 解析枚举项
  const itemMatch = trimmedLine.match(/^'([^']+)'\s*=\s*'([^']+)',?$/);
  if (itemMatch) {
    const [, eventName, eventKey] = itemMatch;
    enumItems.push({
      eventName,
      eventKey,
      comment: currentComment,
    });
    currentComment = '';
  }
}

// 根据事件类型和参数推断埋点信息

// 生成埋点事件清单
const traceEvents = enumItems.map((item) => {
  return {
    eventName: item.eventName,
    eventKey: `amap.xy-task-pc-home.${item.eventKey}`,
    eventType: '点击+曝光',
    filepath: '',
  };
});

// 确保 tempdocs 目录存在
const tempdocsDir = path.join(__dirname, '../../tempdocs');
if (!fs.existsSync(tempdocsDir)) {
  fs.mkdirSync(tempdocsDir, { recursive: true });
}

// 写入埋点事件清单文件
const outputPath = path.join(tempdocsDir, '埋点事件清单.json');
fs.writeFileSync(outputPath, JSON.stringify(traceEvents, null, 2), 'utf8');

console.log(`成功导出 ${traceEvents.length} 个埋点事件到 ${outputPath}`);
console.log('埋点事件清单已清空原有内容并重新生成');

// ==================== 第二步：生成埋点表格 ====================

const inputPath = path.resolve(tempdocsDir, '埋点事件清单.json');
const tsvPath = path.resolve(tempdocsDir, '埋点表格.tsv');

// 固定内容
const SCENE = 'xy-task-pc//C33代运营pc';
const REF_SPEC = 'B端通用公参//233204593';

const IMG = '';

// 读取json
const events = JSON.parse(fs.readFileSync(inputPath, 'utf-8'));

// 表头
const header = [
  '场景【必填】',
  '事件名称【必填】',
  '事件类型【必填】',
  '事件编码【必填】',
  '事件参数【选填】',
  'SPM编码【选填】',
  '图片【选填】',
  '页面编码【选填】',
  '引用埋点规范【选填】',
];

// 按eventKey去重，只保留第一个
const seen = new Set();
const uniqueEvents = events.filter((ev) => {
  if (seen.has(ev.eventKey)) return false;
  seen.add(ev.eventKey);
  return true;
});

// 生成表格内容
const rows = uniqueEvents.map((ev) => {
  // 页面编码取eventKey前两段
  const keyParts = ev.eventKey.split('.');
  const pageCode = keyParts.length >= 2 ? keyParts.slice(0, 2).join('.') : ev.eventKey;
  return [
    SCENE,
    ev.eventName,
    ev.eventType,
    `/${ev.eventKey}`,
    '',
    ev.eventKey,
    IMG,
    `${pageCode}//代运营任务管理首页`,
    REF_SPEC,
  ];
});

// 组装TSV表格
let tsv = `${header.join('\t')}\n`;
rows.forEach((row) => {
  tsv += `${row.join('\t')}\n`;
});
fs.writeFileSync(tsvPath, tsv, 'utf-8');

console.log('埋点TSV表格已生成:', tsvPath);
