# 运维端到端优化需求[第一批] - 执行计划

## 任务概览

根据系分文档，本次需求包含以下 5 个主要任务：

1. 广告计划开启文案调整
2. 门店列表基建任务进度展示优化
3. 删除门店列表上方的任务状态筛选器
4. 基建任务卡片调整（新增）
5. 门店任务详情右上角展示必做标签

## 任务执行计划

### 任务 1：广告计划开启文案调整

**目标**：将广告计划开启的文案调整为："新签广告主签约产品中包含广告推广金，门店基建任务完成状态为「已完成」,可以开启广告新手加速计划"

**实施步骤**：

1. 修改 `src/components/order-modal/ad-switch.tsx` 文件
   - 找到第 65 行的文案描述部分
   - 将现有文案替换为新的文案内容
   - 确保文案显示逻辑保持不变

**涉及文件**：

- `src/components/order-modal/ad-switch.tsx`

### 任务 2：门店列表基建任务进度展示优化

**目标**：在门店列表的基建任务进度列中，展示格式调整为：

- 必做：xx/xx
- 非必做：xx/xx

**实施步骤**：

1. 分析当前基建任务进度的数据结构

   - 查看 `AgentOperationShopTaskDTO` 类型定义中的相关字段
   - 确认是否有必做/非必做的区分字段，如果没有需要后端提供

2. 修改 `src/pages/index/components/shop-list/components/merchant-task-table/index.tsx` 文件

   - 找到基建任务进度列的 render 函数（约第 360-370 行）
   - 修改显示逻辑，按照必做/非必做分别展示进度
   - 如果后端暂未提供区分字段，先预留接口调用位置

**涉及文件**：

- `src/pages/index/components/shop-list/components/merchant-task-table/index.tsx`

**注意事项**：此任务可能需要后端接口支持，如果接口暂未提供相关字段，需要与后端协调

### 任务 3：删除门店列表上方的任务状态筛选器

**目标**：注释掉门店列表上方的任务状态筛选器，不要直接删除代码

**实施步骤**：

1. 修改 `src/pages/index/components/shop-list/components/search-form/index.tsx` 文件

   - 找到任务状态筛选器的 FormItem 组件（约第 54-60 行）
   - 使用注释将该 FormItem 包裹起来，保留代码但不显示
   - 添加注释说明为什么注释掉此功能

2. 确保搜索逻辑中移除对该字段的处理
   - 检查 `src/pages/index/components/shop-list/components/merchant-task-table/index.tsx` 中的 fetchList 函数
   - 确认 shopTaskStatus 字段的处理逻辑是否需要调整

**涉及文件**：

- `src/pages/index/components/shop-list/components/search-form/index.tsx`
- `src/pages/index/components/shop-list/components/merchant-task-table/index.tsx`（可能需要）

### 任务 4：基建任务卡片调整

**目标**：根据 rewardType 字段判断奖励类型，调整任务卡片中的文案显示：

- "任务总分"改为"任务奖励"
- "实际得分"改为"实际奖励"
- 根据奖励类型显示不同的单位和格式

**实施步骤**：

1. 确认 TaskDetailDTO 类型定义中是否包含 rewardType 字段

   - 查看 `src/_docplus/target/types/alsc-kbt-intergration-toolkit-client.d.ts` 中的 TaskDetailDTO 定义
   - 如果没有 rewardType 字段，需要与后端协调添加该字段

2. 修改 `src/components/task-detail/task-card/index.tsx` 文件

   - 在第 190-200 行左右找到任务总分和实际得分的显示逻辑
   - 根据 rewardType 字段值调整显示文案：
     - 当 rewardType 为 'merchant_score' 时：显示 "+xx 分"
     - 当 rewardType 为 'exposure' 时：显示 "+xx 曝光"
     - 当 rewardType 为空或其他值时：保持原来的逻辑

3. 具体修改逻辑：

   ```typescript
   // 根据奖励类型确定显示文案
   const getRewardText = (score: string, type: 'task' | 'actual') => {
     const { rewardType } = detailInfo || {};

     if (!rewardType) {
       // 保持原来的逻辑
       return type === 'task' ? `任务总分：${score}分` : `实际得分：${score}分`;
     }

     if (rewardType === 'merchant_score') {
       return type === 'task' ? `任务奖励：+${score} 分` : `实际奖励：+${score} 分`;
     }

     if (rewardType === 'exposure') {
       return type === 'task' ? `任务奖励：+${score} 曝光` : `实际奖励：+${score} 曝光`;
     }

     // 兜底逻辑
     return type === 'task' ? `任务总分：${score}分` : `实际得分：${score}分`;
   };
   ```

4. 添加埋点（如果需要）

   - 如果需要对新的奖励类型显示进行埋点统计，可以在任务卡片渲染时添加曝光埋点
   - 使用项目中的 `traceExp` 方法进行埋点

**涉及文件**：

- `src/components/task-detail/task-card/index.tsx`
- `src/_docplus/target/types/alsc-kbt-intergration-toolkit-client.d.ts`（可能需要类型更新）

**注意事项**：此任务需要确认后端是否提供了 rewardType 字段，如果没有需要与后端协调

### 任务 5：门店任务详情右上角展示必做标签

**目标**：在门店任务详情的任务卡片右上角展示红框的「必做」标签，根据字段条件渲染

**实施步骤**：

1. 分析当前任务卡片的标签实现

   - 查看 `src/components/task-detail/task-card/index.tsx` 中的 taskDetailLabelList 实现
   - 了解当前标签的渲染逻辑和样式

2. 确认必做标签的判断条件

   - 查看 TaskDetailDTO 类型定义，确认是否有必做标识字段
   - 如果没有相关字段，需要与后端协调添加

3. 修改任务卡片组件

   - 在 `src/components/task-detail/task-card/index.tsx` 中
   - 在现有标签逻辑基础上，添加必做标签的判断和渲染
   - 确保必做标签使用红色边框样式

**涉及文件**：

- `src/components/task-detail/task-card/index.tsx`

**注意事项**：需要确认后端是否提供了必做任务的标识字段

## 执行顺序建议

- 任务 1 和任务 3 可以并行开发（独立任务）
- 任务 2、任务 4 和任务 5 需要确认后端接口支持情况后再开发
- **风险点**：任务 2、任务 4 和任务 5 可能需要后端提供额外的接口字段支持

## 验收标准

1. **任务 1**：广告计划开启弹窗中的文案已更新为新内容
2. **任务 2**：门店列表中基建任务进度显示为"必做：x/x，非必做：x/x"格式
3. **任务 3**：门店列表搜索表单中不再显示任务状态筛选器，但代码被注释保留
4. **任务 4**：基建任务卡片中的文案根据 rewardType 正确显示：
   - 商家分奖励：显示"任务奖励：+xx 分"和"实际奖励：+xx 分"
   - 流量曝光奖励：显示"任务奖励：+xx 曝光"
   - 无 rewardType 或其他值：保持原有显示逻辑
5. **任务 5**：门店任务详情中的必做任务卡片右上角显示红色"必做"标签

## 注意事项

1. 所有修改需要遵循项目的编码规范
2. 涉及接口字段的任务需要与后端确认数据结构
3. 删除功能时使用注释而非直接删除，便于后续恢复
4. 测试时需要确保现有功能不受影响
5. 不要修改 `src/_docplus/` 目录下的文件，这些是自动生成的
