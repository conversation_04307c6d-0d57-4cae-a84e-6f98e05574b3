# [202507]代运营批量质检需求 - 执行计划

## 需求概述

本次需求主要实现代运营批量质检功能，包括：

1. 在门店列表表格上方右侧新增"批量提报"按钮
2. 新增批量提报弹窗，包含门店选择和提报审核两个步骤

## 任务分解

### 任务 1：门店列表表格上方右侧新增"批量提报"按钮

1. 首先需要在 `src/pages/index/components/double-table/shop/table.tsx` 中的表格上方添加"批量提报"按钮
2. 按钮需要根据是否有未完结的批量提报任务来控制禁用状态
3. 点击按钮时触发批量提报弹窗
4. 需要添加按钮的埋点事件

### 任务 2：新增批量提报弹窗组件

1. 在 `src/pages/index/components/double-table/shop/components/` 目录下创建 `batch-submit-modal` 文件夹
2. 创建主弹窗组件 `index.tsx`，负责整体弹窗逻辑和步骤控制
3. 创建门店选择组件 `shop-selector.tsx`，实现第一步的门店选择功能
4. 创建提报审核组件 `submit-review.tsx`，实现第二步的提报审核功能

### 任务 3：完善接口和类型定义

1. 更新 `src/types/batch-submit.ts` 中的类型定义，补充缺失的字段
2. 更新 `src/services/batch-submit.ts` 中的接口定义，确保与接口文档一致
3. 添加装修素材提报记录查询接口，用于获取目标门店名称的默认值

### 任务 4：添加埋点事件

1. 在 `src/utils/trace/traceMap.ts` 中添加批量提报相关的埋点事件
2. 在相应组件中添加埋点调用

## 接口文档分析

### 接口 1：运维工单门店信息查询接口

**接口名称：** `amap-sales-operation.AgentOperationQueryFacade.queryOperationOrderShopList`

**用途：** 获取门店列表，支持多种筛选条件

**新增字段：**

- `hasHistoricalReport`：历史是否提报（枚举值：是/否）
- `isHistoricalOrderApproved`：历史工单是否过审（枚举值：是/否）
- `isMerchantScoreQualified`：商家分是否达标（枚举值：是/否）
- `lastSubmitStartTime`：最近一次提报开始时间
- `lastSubmitEndTime`：最近一次提报结束时间

**响应新增字段：**

- `lastSubmitTime`：最近一次提报时间
- `collectShopName`：装修素材提报记录中的目标门店名称

### 接口 2：批量提报未完结流水查询接口

**接口名称：** `amap-sales-operation.ShopInfraManageFacade.queryUnfinishedOptWoosBizOrder`

**用途：** 查询是否有未完结的批量提报任务，用于控制按钮禁用状态

**响应说明：**

- `result: true` 表示有未完结工单，按钮需要禁用
- `result: false` 表示没有未完结工单，按钮可以正常使用

### 接口 3：批量提报任务创建接口

**接口名称：** `amap-sales-operation.ShopInfraManageFacade.batchSubmitAndCreateEspOrder`

**用途：** 创建批量提报任务

**入参结构：**

- `createReqs`：批量创建请求列表，每个请求包含 `shopId` 和 `collectShopName`

### 接口 4：装修素材提报记录查询接口（复用现有接口）

**接口名称：** `amap-sales-operation.ShopInfraManageFacade.queryShopCollectInfo`

**用途：** 查询门店的装修素材提报记录，获取目标门店名称的默认值

**说明：** 该接口已存在，可以直接复用，返回的 `collectShopName` 字段可以作为目标门店名称的默认值

## 详细实现步骤

### 任务 1：门店列表表格上方右侧新增"批量提报"按钮

#### 1.1 在表格上方添加按钮区域

在 `src/pages/index/components/double-table/shop/table.tsx` 中：

1. 在表格上方添加按钮区域，包含"批量提报"按钮
2. 按钮样式使用 antd 的 Button 组件，类型为 primary
3. 按钮位置在表格的右上角，与现有的搜索表单区域相邻

#### 1.2 添加按钮状态控制逻辑

1. 使用 `queryUnfinishedOptWoosBizOrder` 接口查询是否有未完结的批量提报任务
2. 根据接口返回结果控制按钮的禁用状态
3. 使用 `useRequest` hook 管理接口调用状态

#### 1.3 添加按钮点击事件

1. 点击按钮时打开批量提报弹窗
2. 传递必要的参数给弹窗组件

#### 1.4 添加埋点事件

1. 按钮曝光埋点：`traceExp(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {})`
2. 按钮点击埋点：`traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {})`

### 任务 2：新增批量提报弹窗组件

#### 2.1 创建主弹窗组件

在 `src/pages/index/components/double-table/shop/components/batch-submit-modal/index.tsx` 中：

1. 创建 `BatchSubmitModal` 组件，使用 antd 的 Modal 组件
2. 实现步骤控制逻辑，使用 `BatchSubmitStep` 枚举管理当前步骤
3. 管理弹窗的显示/隐藏状态
4. 管理已选门店列表和目标门店设置数据
5. 实现弹窗关闭时清空所有数据的逻辑

#### 2.2 创建门店选择组件

在 `src/pages/index/components/double-table/shop/components/batch-submit-modal/shop-selector.tsx` 中：

1. 创建 `ShopSelector` 组件，实现第一步的门店选择功能
2. 使用 `getBatchSubmitShopList` 接口获取门店列表
3. 实现筛选功能，支持以下字段筛选：
   - 历史是否提报（hasHistoricalReport）
   - 历史工单是否过审（isHistoricalOrderApproved）
   - 商家分是否达标（isMerchantScoreQualified）
   - pid 查询
   - 门店 id 查询
   - 最近提报时间（lastSubmitStartTime、lastSubmitEndTime）
4. 实现跨页选择功能，保留已选门店状态
5. 实现全选/取消全选功能
6. 添加"取消"和"下一步"按钮
7. 通过回调函数通知父组件已选门店

#### 2.3 创建提报审核组件

在 `src/pages/index/components/double-table/shop/components/batch-submit-modal/submit-review.tsx` 中：

1. 创建 `SubmitReview` 组件，实现第二步的提报审核功能
2. 渲染已选门店列表，显示门店基础信息（门店名称、门店 ID、PID、最近一次提报时间）
3. 新增"操作"列，支持设置无目标门店或目标门店名称
4. 查询装修素材提报记录，使用 `queryShopCollectInfo` 接口，如果有记录则默认填充目标门店名称
5. 添加"上一步"和"提报审核"按钮
6. 实现表单验证逻辑：勾选的门店必须填写目标门店信息，若无则勾选无目标门店
7. 通过回调函数通知父组件提交数据
8. 提交时调用 `batchSubmitAndCreateEspOrder` 接口创建批量提报任务

### 任务 3：完善接口和类型定义

#### 3.1 更新类型定义

在 `src/types/batch-submit.ts` 中：

1. 补充 `IShopInfo` 接口的缺失字段，确保与接口文档一致
2. 添加装修素材提报记录查询相关的类型定义
3. 完善批量提报创建请求的类型定义
4. 添加门店采买信息查询的类型定义

#### 3.2 更新接口定义

在 `src/services/batch-submit.ts` 中：

1. 确保 `getBatchSubmitShopList` 接口的参数和响应类型正确，使用 `queryOperationOrderShopList` 接口
2. 确保 `queryUnfinishedOptWoosBizOrder` 接口的参数和响应类型正确，使用 `queryUnfinishedOptWoosBizOrder` 接口
3. 确保 `batchSubmitAndCreateEspOrder` 接口的参数和响应类型正确，使用 `batchSubmitAndCreateEspOrder` 接口
4. 添加装修素材提报记录查询接口，复用现有的 `queryShopCollectInfo` 接口

### 任务 4：添加埋点事件

#### 4.1 更新埋点映射表

在 `src/utils/trace/traceMap.ts` 中：

1. 在 `TrackerKey` 枚举中添加批量提报相关页面标识
2. 在 `TraceEvent` 枚举中添加批量提报相关事件

#### 4.2 在组件中添加埋点

1. 在批量提报弹窗组件中添加曝光埋点
2. 在门店选择组件中添加相关操作埋点
3. 在提报审核组件中添加相关操作埋点

## 技术实现要点

### 1. 组件拆分原则

- 主弹窗组件负责整体逻辑和状态管理
- 门店选择组件和提报审核组件分别负责各自的业务逻辑
- 组件间通过回调函数进行数据传递

### 2. 数据流设计

- 主弹窗组件管理全局状态（当前步骤、已选门店、目标门店设置等）
- 子组件通过 props 接收数据，通过回调函数通知父组件状态变更
- 弹窗关闭时清空所有状态数据

### 3. 接口调用优化

- 使用 `useRequest` hook 管理接口调用状态
- 实现接口调用的错误处理和重试机制
- 合理使用缓存减少重复请求

### 4. 用户体验优化

- 添加加载状态提示
- 实现表单验证和错误提示
- 支持键盘快捷键操作
- 添加操作确认提示

## 文件结构

```
src/
├── pages/
│   └── index/
│       └── components/
│           └── double-table/
│               └── shop/
│                   ├── table.tsx (修改：添加批量提报按钮)
│                   └── components/
│                       └── batch-submit-modal/
│                           ├── index.tsx (新增：主弹窗组件)
│                           ├── shop-selector.tsx (新增：门店选择组件)
│                           └── submit-review.tsx (新增：提报审核组件)
├── types/
│   └── batch-submit.ts (修改：完善类型定义)
├── services/
│   └── batch-submit.ts (修改：完善接口定义)
└── utils/
    └── trace/
        └── traceMap.ts (修改：添加埋点事件)
```

## 任务依赖关系分析

### 任务依赖关系

1. **任务 3（完善接口和类型定义）** → **任务 2（新增批量提报弹窗组件）**

   - 必须先完善类型定义和接口定义，才能进行组件开发

2. **任务 2（新增批量提报弹窗组件）** → **任务 1（新增批量提报按钮）**

   - 必须先完成弹窗组件，才能在按钮中引用

3. **任务 2 内部依赖关系：**

   - 主弹窗组件 → 门店选择组件 → 提报审核组件
   - 需要先创建主弹窗组件框架，再实现子组件

4. **任务 4（添加埋点事件）** → **任务 1 和任务 2**
   - 埋点事件需要在组件开发完成后添加

### 重排后的开发顺序

1. **第一阶段：基础准备**

   - 完善类型定义和接口定义（任务 3）

2. **第二阶段：核心组件开发**

   - 创建批量提报弹窗的主组件框架
   - 实现门店选择组件
   - 实现提报审核组件
   - （任务 2 的各个子任务）

3. **第三阶段：集成和优化**

   - 在门店列表表格中添加批量提报按钮（任务 1）
   - 添加埋点事件（任务 4）

4. **第四阶段：测试和联调**
   - 进行联调和测试
   - 修复问题和优化体验

## Step2: 检查任务数是否正确

根据需求系分文档，我识别出以下主要任务：

1. **门店列表表格上方右侧新增"批量提报"按钮** ✅
2. **新增批量提报弹窗**（包含两个步骤）✅
   - 第一步：门店选择模块 ✅
   - 第二步：提报审核模块 ✅

**任务数检查结果：** ✅ 所有任务都已包含在执行计划中，没有遗漏。

## Step4: REVIEW 执行计划

### 检查每个任务是否合理

#### 任务 1：门店列表表格上方右侧新增"批量提报"按钮 ✅

**符合需求：** ✅ 在门店列表表格上方右侧添加按钮，符合需求描述

**可以拆分复用：** ✅ 按钮组件可以复用，逻辑相对简单

**模块依赖关系：** ✅ 依赖批量提报弹窗组件，依赖关系正确

**数据流合理：** ✅ 按钮状态由接口控制，点击事件触发弹窗，数据流合理

#### 任务 2：新增批量提报弹窗组件 ✅

**符合需求：** ✅ 包含两个步骤的门店选择和提报审核，符合需求描述

**可以拆分复用：** ✅ 已拆分为主弹窗、门店选择、提报审核三个子组件，拆分合理

**模块依赖关系：** ✅ 主弹窗管理全局状态，子组件通过回调函数通信，依赖关系正确

**数据流合理：** ✅ 主弹窗管理状态，子组件通过 props 接收数据，通过回调通知变更，数据流合理

#### 任务 3：完善接口和类型定义 ✅

**符合需求：** ✅ 确保接口和类型定义与接口文档一致

**可以拆分复用：** ✅ 类型定义可以复用，接口定义清晰

**模块依赖关系：** ✅ 为其他任务提供基础，依赖关系正确

**数据流合理：** ✅ 类型定义支持数据流，接口定义支持业务逻辑

#### 任务 4：添加埋点事件 ✅

**符合需求：** ✅ 为新增的按钮和页面添加埋点事件

**可以拆分复用：** ✅ 埋点事件可以复用，映射表统一管理

**模块依赖关系：** ✅ 在组件开发完成后添加，依赖关系正确

**数据流合理：** ✅ 埋点事件不影响业务数据流，合理

### 补充埋点任务

根据需求，需要为以下新增功能添加埋点：

1. **批量提报按钮**

   - 曝光埋点：按钮显示时触发
   - 点击埋点：按钮点击时触发

2. **批量提报弹窗**

   - 曝光埋点：弹窗打开时触发
   - 步骤切换埋点：从门店选择切换到提报审核时触发

3. **门店选择组件**

   - 筛选埋点：使用筛选条件时触发
   - 选择埋点：选择门店时触发
   - 全选埋点：全选/取消全选时触发

4. **提报审核组件**
   - 提交埋点：提交审核时触发
   - 目标门店设置埋点：设置目标门店时触发

## 注意事项

1. 确保组件遵循项目的编码规范
2. 使用 TypeScript 确保类型安全
3. 合理使用 React Hooks 管理状态
4. 注意组件的性能优化
5. 确保埋点事件的准确性
6. 做好错误处理和边界情况处理
7. 遵循项目的目录结构约定
8. 确保接口调用的错误处理和重试机制
9. 注意用户体验优化，如加载状态、表单验证等

## 总结

本执行计划严格按照 solution 规则制定，包含了代运营批量质检需求的所有任务：

1. **任务完整性**：涵盖了需求系分中的所有功能点，包括批量提报按钮、两步式弹窗、接口集成等
2. **技术方案合理**：遵循项目技术栈和编码规范，组件拆分合理，数据流设计清晰
3. **接口分析完整**：详细分析了所有相关接口，包括新增接口和复用接口
4. **任务依赖关系清晰**：明确了任务间的依赖关系，制定了合理的开发顺序
5. **埋点方案完善**：为所有新增功能制定了完整的埋点方案

该执行计划可以直接用于指导开发工作，确保项目按时高质量交付。
