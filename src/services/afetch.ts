import fetch from '@ali/kb-fetch';
import type { Request } from '@alife/alsc-fetch-types';
import type { GatewayResult } from '@ali/alsc-gateway-web-client';
import { message } from 'antd';

export type GatewayRequest<TRequestData> = Omit<Request, 'url' | 'params' | 'param'> & {
  param?: Partial<TRequestData>;
  showError?: boolean;
};

export const alscFetch = async <TRequestData = any, TResponse = any>(
  request: GatewayRequest<TRequestData>,
): Promise<GatewayResult<TResponse>> => {
  return fetch({ apiKey: request.api, param: request.param })
    .then((res) => {
      return res;
    })
    .catch((e) => {
      if (request.showError) {
        message.error(e?.errorMessage || e.message || '系统异常，请稍后再试～');
      }
      if (e?.response) {
        const result = e.response?.result || e.response || {};
        throw {
          message: e.errorMessage || '',
          res: {
            ...result,
          },
        };
      }
      throw e;
    });
};
