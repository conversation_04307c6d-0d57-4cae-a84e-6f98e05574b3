import { OssUploader } from '@alife/mp-oss-upload/esm/uploader';

// uploadInstance.upload(file);
export const uploadToOSS = (file: File, isReport = true) => {
  const reportImg = {
    bucketName: 'amap-sales-automation-oss-prod',
    ossConfigId: '63d4638fd1f04cc4',
    dir: 'reportImg',
    generateUrlType: 'gd',
  };
  const other = {
    bucketName: 'amap-sales-operation-prod',
    ossConfigId: '4071ecfb4ada4cd7',
    dir: 'task',
    generateUrlType: 'gd',
  };
  const uploadInstance = new OssUploader(isReport ? reportImg : other);
  return uploadInstance.upload(file);
};
