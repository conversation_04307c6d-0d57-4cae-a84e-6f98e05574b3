import request, { gdRequest } from './request';

// 获取bd话术模板列表
export const getBdTemplateList = ({
  scene,
}: {
  scene?: string;
} = {}): Promise<{
  materials: Array<{ scene: string; content: string; materialId: any }>;
}> => {
  return request('amap-sales-operation.OptMaterialQueryHsf.queryGroupMaterials', { scene });
};

/**
 * 新增BD话术模板
 */
export const saveBdTemplate = (data: {
  scene: string;
  content: string;
  materialId?: any;
}): Promise<any> => {
  return request('amap-sales-operation.OptMaterialCmdHsf.saveGroupMaterial', data);
};

/**
 * 编辑BD话术模板
 */
export const editBdTemplate = (data: {
  scene: string;
  content: string;
  materialId?: any;
}): Promise<any> => {
  return request('amap-sales-operation.OptMaterialCmdHsf.editGroupMaterial', data);
};
interface sendGroupMsgParams {
  materialId: any;
  merchantNewsReachModelList: Array<{
    pid: string;
    merchantNewsPageUrl?: string;
    shopDetailListPageUrl?: string;
  }>;
  from: 'MERCHANT_NEWS' | 'MERCHANT_LIST'; // MERCHANT_NEWS 喜报页 ｜ MERCHANT_LIST 商户列表
  sendMerchantNews?: boolean;
  startDate?: string;
  endDate?: string;
  shopDetailListPageUrl?: string;
}

/**
 * 批量发送企微群
 */
export const sendGroupMsg = (
  data: sendGroupMsgParams,
): Promise<{
  sendFailureList?: Array<{ pid: string; mainShopName: string; failureReason: string }>;
}> => {
  return gdRequest('amap-sales-operation.OperationAutoPushFacade.batchMerchantNewReachOut', data);
};
