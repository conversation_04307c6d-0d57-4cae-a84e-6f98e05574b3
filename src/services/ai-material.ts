import request, { gdRequest } from './request';
import * as MaterialCollectionTypes from '@/types/ai-material/merchant';
import {
  IInfraContentCollectReachOutParams,
  IInfraContentCollectReachOutResult,
} from '@/types/ai-material/merchant';

// ========== AI提效工具-门店装修相关接口 ========== //

// 1. 商户列表查询
export const getMerchantList = (
  params: MaterialCollectionTypes.IQueryMerchantListParams,
): Promise<MaterialCollectionTypes.IMerchantListResponse> => {
  return request(
    'amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationMerchantList',
    params,
  );
};

// 2. 门店列表查询
export const getShopList = (
  params: MaterialCollectionTypes.IQueryShopListParams,
): Promise<MaterialCollectionTypes.IShopListResponse> => {
  return request(
    'amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationShopList',
    params,
  );
};

// 3. tab 列表查询
export const queryTabInfo = (
  params: MaterialCollectionTypes.IQueryTabInfoParams,
): Promise<MaterialCollectionTypes.IQueryTabInfoResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryTabInfo', params);
};

// 4. 素材列表查询
export const queryDraftList = (
  params: MaterialCollectionTypes.IQueryDraftListParams,
): Promise<MaterialCollectionTypes.IDraftListResponse> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryDraftList', params);
};

// 5. 发布明细内容批量查询
export const queryPubContentList = (
  params: MaterialCollectionTypes.IQueryPubContentListParams,
): Promise<MaterialCollectionTypes.IQueryPubContentListResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryPubContentList', params);
};

// 6. 素材批量提交接口
export const batchPublish = (
  params: MaterialCollectionTypes.IBatchPublishParams,
): Promise<MaterialCollectionTypes.IBatchPublishResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.batchPublish', params);
};

// 7. 素材收集配置查询（表单收集页面）
export const queryCollectDraft = (
  params: MaterialCollectionTypes.IQueryCollectDraftParams,
): Promise<MaterialCollectionTypes.IQueryCollectDraftResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.queryCollectDraft', params);
};

// 8. 表单收集页面提交接口
export const submitDraft = (
  params: MaterialCollectionTypes.ISubmitDraftParams,
): Promise<MaterialCollectionTypes.ISubmitDraftResult> => {
  return gdRequest('amap-sales-operation.AiSupplyDraftManageFacade.submitDraft', params);
};

// 9. 草稿状态批量更新接口
export const batchUpdateStatus = (
  params: MaterialCollectionTypes.IBatchUpdateStatusParams,
): Promise<MaterialCollectionTypes.IBatchUpdateStatusResult> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.batchUpdateStatus', params);
};

// 10. 查询个人素材接口
export const queryGroupMaterials = (
  params: MaterialCollectionTypes.IGroupMaterialsQueryParams,
): Promise<MaterialCollectionTypes.IQueryGroupMaterialsResult> => {
  return gdRequest('amap-sales-operation.OptMaterialQueryHsf.queryGroupMaterials', params);
};

// 11. 企微素材推送接口
export const batchMerchantNewReachOut = (
  params: MaterialCollectionTypes.IBatchMerchantNewReachOutParams,
): Promise<MaterialCollectionTypes.IBatchMerchantNewReachOutResult> => {
  return gdRequest(
    'com.amap.sales.operation.client.OperationAutoPushFacade.batchMerchantNewReachOut',
    params,
  );
};

// 12. 素材内容收集推送（企微群）接口
export const infraContentCollectReachOut = (
  params: IInfraContentCollectReachOutParams,
): Promise<IInfraContentCollectReachOutResult> => {
  return gdRequest(
    'amap-sales-operation.OperationAutoPushFacade.infraContentCollectReachOut',
    params,
  );
};
