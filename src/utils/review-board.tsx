import React from 'react';
import numeral from 'numeral';

const formatNum = (
  value: string | number | undefined,
  format: string,
  handleValue?: (v: number) => number,
) => {
  return typeof value !== 'undefined'
    ? numeral(handleValue ? handleValue(+value) : value).format(format)
    : '--';
};

/**
 * 整数
 * @param num
 * @returns
 */
const formatCount = (num: string | number | undefined) => {
  return formatNum(num, '0,0');
};

/**
 * 保留一位小数
 * @param num
 * @returns
 */
const formatDecimals = (num: string | number | undefined) => {
  return formatNum(num, '0.0');
};

export const reviewDataFormatter = (value: string | number) => {
  if (typeof value === 'undefined' || value === null) {
    return value;
  }
  const num = +value;
  if (num > 100000000) {
    return `${formatDecimals(num / 100000000)}亿`;
  } else if (num > 10000) {
    return `${formatDecimals(num / 10000)}万`;
  } else if (num === 0) {
    return formatCount(num);
  } else {
    return value;
  }
};

export const handleFormatData = (data: any) => {
  const {
    ad_cost_1d,
    ad_cost_1d_hb_diff,
    cash_cost_1d,
    cash_cost_1d_hb_diff,
    cash_cost_1d_div_sum_day,
    cash_cost_1d_div_sum_day_hb_diff,
    redp_cost_1d,
    redp_cost_1d_hb_diff,
    cpc_cost_1d,
    cpc_cost_1d_hb_diff,
    cpc_cost_1d_div_ad_cost_1d,
    cpc_cost_1d_div_ad_cost_1d_hb_diff,
    cpc_cash_cost_1d,
    cpc_cash_cost_1d_hb_diff,
    cpc_cash_cost_1d_div_cpc_cost_1d,
    cpc_cash_cost_1d_div_cpc_cost_1d_hb_diff,
    ocpc_cost_1d,
    ocpc_cost_1d_hb_diff,
    ocpc_cost_1d_div_ad_cost_1d,
    ocpc_cost_1d_div_ad_cost_1d_hb_diff,
    ocpc_cash_cost_1d,
    ocpc_cash_cost_1d_hb_diff,
    ocpc_cash_cost_1d_div_ocpc_cost_1d,
    ocpc_cash_cost_1d_div_ocpc_cost_1d_hb_diff,
    all_kz_cnt_1d,
    all_kz_cnt_1d_hb_diff,
    all_kz_cnt_1d_div_sum_day,
    all_kz_cnt_1d_div_sum_day_hb_diff,
    cash_cost_1d_div_all_kz_cnt_1d,
    cash_cost_1d_div_all_kz_cnt_1d_hb_diff,
    ad_kz_cnt_1d,
    ad_kz_cnt_1d_hb_diff,
    cash_cost_1d_div_ad_kz_cnt_1d,
    cash_cost_1d_div_ad_kz_cnt_1d_hb_diff,
    phone_kz_cnt_1d,
    phone_kz_cnt_1d_hb_diff,
    phone_kz_cnt_1d_div_all_kz_cnt_1d,
    phone_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    plat_kz_cnt_1d,
    plat_kz_cnt_1d_hb_diff,
    plat_kz_cnt_1d_div_all_kz_cnt_1d,
    plat_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    order_kz_cnt_1d,
    order_kz_cnt_1d_hb_diff,
    order_kz_cnt_1d_div_all_kz_cnt_1d,
    order_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    consult_kz_cnt_1d,
    consult_kz_cnt_1d_hb_diff,
    consult_kz_cnt_1d_div_all_kz_cnt_1d,
    consult_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    booking_kz_cnt_1d,
    booking_kz_cnt_1d_hb_diff,
    booking_kz_cnt_1d_div_all_kz_cnt_1d,
    booking_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    arrive_kz_cnt_1d,
    arrive_kz_cnt_1d_hb_diff,
    arrive_kz_cnt_1d_div_all_kz_cnt_1d,
    arrive_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    gd_car_kz_cnt_1d,
    gd_car_kz_cnt_1d_hb_diff,
    gd_car_kz_cnt_1d_div_all_kz_cnt_1d,
    gd_car_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    visible_kz_cnt_1d,
    visible_kz_cnt_1d_hb_diff,
    invisible_kz_cnt_1d,
    invisible_kz_cnt_1d_hb_diff,
    not_ad_kz_cnt_1d,
    not_ad_kz_cnt_1d_hb_diff,
    not_ad_kz_cnt_1d_div_all_kz_cnt_1d,
    not_ad_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    ad_cost_1d_div_visible_kz_cnt_1d,
    ad_cost_1d_div_visible_kz_cnt_1d_hb_diff,
    invisible_kz_cnt_1d_div_all_kz_cnt_1d,
    invisible_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
    ad_expo_cnt_1d,
    ad_expo_cnt_1d_hb_diff,
    ad_expo_cnt_1d_div_sum_day,
    ad_expo_cnt_1d_div_sum_day_hb_diff,
    ad_click_cnt_1d,
    ad_click_cnt_1d_hb_diff,
    ad_click_cnt_1d_div_ad_expo_cnt_1d,
    ad_click_cnt_1d_div_ad_expo_cnt_1d_hb_diff,
    ad_cost_1d_div_ad_expo_cnt_1d,
    ad_cost_1d_div_ad_expo_cnt_1d_hb_diff,
  } = data || {};

  // 消耗字段
  const CONSUMPTIONDATA = () => {
    return [
      reviewDataFactory(
        'ad_cost_1d',
        '广告消耗',
        ad_cost_1d,
        ad_cost_1d_hb_diff,
        '门店和商品广告投放的总消耗（含现金、红包消耗），包括用户到店、客资线索',
      ),
      reviewDataFactory(
        'cash_cost_1d',
        '现金消耗',
        cash_cost_1d,
        cash_cost_1d_hb_diff,
        '门店和商品广告投放的现金消耗，包括用户到店、客资线索',
      ),
      reviewDataFactory(
        'cash_cost_1d_div_sum_day',
        '日均现金消耗',
        cash_cost_1d_div_sum_day,
        cash_cost_1d_div_sum_day_hb_diff,
        '现金消耗/投放时间',
      ),
      reviewDataFactory(
        'redp_cost_1d',
        '红包消耗',
        redp_cost_1d,
        redp_cost_1d_hb_diff,
        '门店和商品广告投放的红包消耗（含现金红包、折扣红包），包括用户到店、客资线索',
      ),
      reviewDataFactory(
        'cpc_cost_1d',
        'CPC消耗',
        cpc_cost_1d,
        cpc_cost_1d_hb_diff,
        '通过广告投放，用户点击所产生的消耗金额（含现金、红包消耗）',
      ),
      reviewDataFactory(
        'cpc_cost_1d_div_ad_cost_1d',
        'CPC消耗占比',
        cpc_cost_1d_div_ad_cost_1d,
        cpc_cost_1d_div_ad_cost_1d_hb_diff,
        '通过广告投放，用户点击所产生的消耗金额占比（含现金、红包消耗），CPC消耗/广告消耗',
      ),
      reviewDataFactory(
        'cpc_cash_cost_1d',
        'CPC现金消耗',
        cpc_cash_cost_1d,
        cpc_cash_cost_1d_hb_diff,
        '通过广告投放，用户点击所产生的现金消耗金额',
      ),
      reviewDataFactory(
        'cpc_cash_cost_1d_div_cpc_cost_1d',
        'CPC现金消耗占比',
        cpc_cash_cost_1d_div_cpc_cost_1d,
        cpc_cash_cost_1d_div_cpc_cost_1d_hb_diff,
        '通过广告投放，用户点击所产生的现金消耗占比，CPC现金消耗/CPC消耗',
      ),
      reviewDataFactory(
        'ocpc_cost_1d',
        'OCPC消耗',
        ocpc_cost_1d,
        ocpc_cost_1d_hb_diff,
        '通过广告投放，广告客资所产生的消耗金额（含现金、红包消耗）',
      ),
      reviewDataFactory(
        'ocpc_cost_1d_div_ad_cost_1d',
        'OCPC消耗占比',
        ocpc_cost_1d_div_ad_cost_1d,
        ocpc_cost_1d_div_ad_cost_1d_hb_diff,
        '通过广告投放，广告客资所产生的消耗金额占比（含现金、红包消耗），OCPC消耗/广告消耗',
      ),
      reviewDataFactory(
        'ocpc_cash_cost_1d',
        'OCPC现金消耗',
        ocpc_cash_cost_1d,
        ocpc_cash_cost_1d_hb_diff,
        '通过广告投放，广告客资所产生的现金消耗金额',
      ),
      reviewDataFactory(
        'ocpc_cash_cost_1d_div_ocpc_cost_1d',
        'OCPC现金消耗占比',
        ocpc_cash_cost_1d_div_ocpc_cost_1d,
        ocpc_cash_cost_1d_div_ocpc_cost_1d_hb_diff,
        '通过广告投放，广告客资所产生的现金消耗金额占比，OCPC现金消耗/OCPC消耗',
      ),
    ];
  };

  // 客资字段
  const CUSTOMERDATA = () => {
    return [
      reviewDataFactory(
        'all_kz_cnt_1d',
        '总客资量',
        all_kz_cnt_1d,
        all_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的总客资数量（含广告客资+非广告客资）',
      ),
      reviewDataFactory(
        'cash_cost_1d_div_all_kz_cnt_1d',
        '客资成本',
        cash_cost_1d_div_all_kz_cnt_1d,
        cash_cost_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的广告客资成本（广告消耗/总客资量）',
      ),
      reviewDataFactory(
        'all_kz_cnt_1d_div_sum_day',
        '日均客资量',
        all_kz_cnt_1d_div_sum_day,
        all_kz_cnt_1d_div_sum_day_hb_diff,
        '通过广告投放，每天获取的客资数量（含广告客资+非广告客资），总客资量/投放时间段',
      ),
      reviewDataFactory(
        'phone_kz_cnt_1d',
        '电话客资',
        phone_kz_cnt_1d,
        phone_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的电话咨询客资量，包括高德、口碑、支付宝',
      ),
      reviewDataFactory(
        'phone_kz_cnt_1d_div_all_kz_cnt_1d',
        '电话客资占比',
        phone_kz_cnt_1d_div_all_kz_cnt_1d,
        phone_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的电话咨询客资量占总客资量的比例（包括高德、口碑、支付宝），电话客资/总客资量',
      ),
      reviewDataFactory(
        'plat_kz_cnt_1d',
        '平台客资',
        plat_kz_cnt_1d,
        plat_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的平台客资量，包括高德、口碑、支付宝',
      ),
      reviewDataFactory(
        'plat_kz_cnt_1d_div_all_kz_cnt_1d',
        '平台客资占比',
        plat_kz_cnt_1d_div_all_kz_cnt_1d,
        plat_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的平台客资量占总客资量的比例（包括高德、口碑、支付宝），平台客资/总客资量',
      ),
      reviewDataFactory(
        'order_kz_cnt_1d',
        '订单客资',
        order_kz_cnt_1d,
        order_kz_cnt_1d_hb_diff,
        '通过广告投放，门店下单的客资量，包括高德、口碑、支付宝',
      ),
      reviewDataFactory(
        'order_kz_cnt_1d_div_all_kz_cnt_1d',
        '订单客资占比',
        order_kz_cnt_1d_div_all_kz_cnt_1d,
        order_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，门店下单的客资量占总客资量的比例（包括高德、口碑、支付宝），订单客资/总客资量',
      ),
      reviewDataFactory(
        'consult_kz_cnt_1d',
        '在线咨询客资',
        consult_kz_cnt_1d,
        consult_kz_cnt_1d_hb_diff,
        '通过广告投放，在高德地图与您发起聊天的顾客数量',
      ),
      reviewDataFactory(
        'consult_kz_cnt_1d_div_all_kz_cnt_1d',
        '在线咨询客资占比',
        consult_kz_cnt_1d_div_all_kz_cnt_1d,
        consult_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，在高德地图与您发起聊天的顾客数量占总客资量的比例，在线咨询客资/总客资量',
      ),
      reviewDataFactory(
        'booking_kz_cnt_1d',
        '预约礼客资',
        booking_kz_cnt_1d,
        booking_kz_cnt_1d_hb_diff,
        '通过广告投放，领取到店礼的客资量，包括高德、口碑、支付宝',
      ),
      reviewDataFactory(
        'booking_kz_cnt_1d_div_all_kz_cnt_1d',
        '预约礼客资占比',
        booking_kz_cnt_1d_div_all_kz_cnt_1d,
        booking_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，领取到店礼的客资量占总客资量的比例（包括高德、口碑、支付宝），预约礼客资/总客资量',
      ),
      reviewDataFactory(
        'arrive_kz_cnt_1d',
        '到店预约客资',
        arrive_kz_cnt_1d,
        arrive_kz_cnt_1d_hb_diff,
        '通过广告投放，快速预约留资的客资量，包括高德、口碑、支付宝',
      ),
      reviewDataFactory(
        'arrive_kz_cnt_1d_div_all_kz_cnt_1d',
        '到店预约客资占比',
        arrive_kz_cnt_1d_div_all_kz_cnt_1d,
        arrive_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，快速预约留资的客资量占总客资量的比例（包括高德、口碑、支付宝），到店预约礼客资/总客资量',
      ),
      reviewDataFactory(
        'gd_car_kz_cnt_1d',
        '高德打车客资',
        gd_car_kz_cnt_1d,
        gd_car_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的高德打车留资用户量',
      ),
      reviewDataFactory(
        'gd_car_kz_cnt_1d_div_all_kz_cnt_1d',
        '高德打车客资占比',
        gd_car_kz_cnt_1d_div_all_kz_cnt_1d,
        gd_car_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '通过广告投放，获取的高德打车留资用户量占总客资量的比例，高德打车客资/总客资量',
      ),
      reviewDataFactory(
        'not_ad_kz_cnt_1d',
        '非广告客资',
        not_ad_kz_cnt_1d,
        not_ad_kz_cnt_1d_hb_diff,
        '广告投放期间，投放计划预算撞线后产生的客资量',
      ),
      reviewDataFactory(
        'not_ad_kz_cnt_1d_div_all_kz_cnt_1d',
        '非广告客资占比',
        not_ad_kz_cnt_1d_div_all_kz_cnt_1d,
        not_ad_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '广告投放期间，投放计划预算撞线后产生的客资量占总客资量的比例，非广告客资/总客资量',
      ),
      reviewDataFactory(
        'ad_kz_cnt_1d',
        '广告客资',
        ad_kz_cnt_1d,
        ad_kz_cnt_1d_hb_diff,
        '电话客资、平台客资、订单客资、在线咨询客资、预约礼客资、到店预约客资、高德打车客资加总',
      ),
      reviewDataFactory(
        'cash_cost_1d_div_ad_kz_cnt_1d',
        '广告客资成本',
        cash_cost_1d_div_ad_kz_cnt_1d,
        cash_cost_1d_div_ad_kz_cnt_1d_hb_diff,
        '单个广告客资成本（现金消耗/广告客资）',
      ),
      reviewDataFactory(
        'visible_kz_cnt_1d',
        '可见客资量',
        visible_kz_cnt_1d,
        visible_kz_cnt_1d_hb_diff,
        '通过广告投放获取的实际客资数量',
      ),
      reviewDataFactory(
        'ad_cost_1d_div_visible_kz_cnt_1d',
        '可见客资成本',
        ad_cost_1d_div_visible_kz_cnt_1d,
        ad_cost_1d_div_visible_kz_cnt_1d_hb_diff,
        '单个可见客资成本（广告消耗/可见客资量）',
      ),
      reviewDataFactory(
        'invisible_kz_cnt_1d',
        '不可见客资量',
        invisible_kz_cnt_1d,
        invisible_kz_cnt_1d_hb_diff,
        '广告未投放期间错过的广告客资数量',
      ),
      reviewDataFactory(
        'invisible_kz_cnt_1d_div_all_kz_cnt_1d',
        '不可见客资占比',
        invisible_kz_cnt_1d_div_all_kz_cnt_1d,
        invisible_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff,
        '广告未投放期间错过的广告客资数量占总客资量的比例，不可见客资量/总客资量',
      ),
    ];
  };

  // 流量及转化字段
  const EXPODATA = () => {
    return [
      reviewDataFactory(
        'ad_expo_cnt_1d',
        '广告曝光量',
        ad_expo_cnt_1d,
        ad_expo_cnt_1d_hb_diff,
        '通过广告投放，门店被用户看到的次数',
      ),
      reviewDataFactory(
        'ad_click_cnt_1d',
        '广告点击量',
        ad_click_cnt_1d,
        ad_click_cnt_1d_hb_diff,
        '通过广告投放，门店被用户点击的次数',
      ),
      reviewDataFactory(
        'ad_click_cnt_1d_div_ad_expo_cnt_1d',
        '广告点击率',
        ad_click_cnt_1d_div_ad_expo_cnt_1d,
        ad_click_cnt_1d_div_ad_expo_cnt_1d_hb_diff,
        '广告点击量/广告曝光量',
      ),
      reviewDataFactory(
        'ad_expo_cnt_1d_div_sum_day',
        '广告日均曝光量',
        ad_expo_cnt_1d_div_sum_day,
        ad_expo_cnt_1d_div_sum_day_hb_diff,
        '广告投放期间您门店每天被看见的次数（广告曝光量/投放时间段）',
      ),
      reviewDataFactory(
        'ad_cost_1d_div_ad_expo_cnt_1d',
        '千次曝光成本',
        ad_cost_1d_div_ad_expo_cnt_1d,
        ad_cost_1d_div_ad_expo_cnt_1d_hb_diff,
        '每千次展现产生的花费金额',
      ),
    ];
  };
  return {
    CONSUMPTIONDATA: CONSUMPTIONDATA(),
    CUSTOMERDATA: CUSTOMERDATA(),
    EXPODATA: EXPODATA(),
  };
};

export const reviewDataFactory = (
  key: string,
  title: string,
  value: number,
  compareValue: number,
  tips?: string,
) => {
  return {
    key,
    title,
    tips,
    value: reviewDataFormatter(value),
    compareValue,
  };
};

// 精细化复盘是否可请求
export const canRequest = (formData: any) => {
  const { pid, shopIds, dateRange } = formData;
  return pid && shopIds?.length && dateRange?.length;
};

// 获取指标数据
export const getFormatData = (data: any) => {
  const { CONSUMPTIONDATA, CUSTOMERDATA, EXPODATA } = handleFormatData(data) || {};
  return [
    {
      title: '消耗字段',
      data: CONSUMPTIONDATA,
    },
    {
      title: '客资字段',
      data: CUSTOMERDATA,
    },
    {
      title: '流量及转化字段',
      data: EXPODATA,
    },
  ];
};

// 整合删减字段和指标，保留选中（未删减）数据
export const getFinalSelectData = (data: any, fields: string[]) => {
  const mergedData = Object.values(handleFormatData(data)).flat();
  return mergedData.filter((item) => fields.includes(item.key));
};

// 获取全部指标字段key
export const getAllFields = (data: any) => {
  return Object.values(handleFormatData(data)).flatMap((group) => group.map((item) => item.key));
};

// 环比数据
export const getFormatCompareValue = (value: any) => {
  if (value === '--') {
    return ['', '--'];
  }

  const val = parseFloat(value as any);
  if (val > 0) {
    return ['#f00', `+${value}`];
  }
  if (val < 0) {
    return ['#69bf47', value];
  }
  return ['', value];
};

// 环比
export const renderFormatCompareValue = (compareValue: any, isEmptyValue: boolean) => {
  if (!isEmptyValue && typeof compareValue !== 'undefined' && compareValue !== null) {
    const [compareColor, parsedCompareValue] = getFormatCompareValue(compareValue);
    return (
      <span style={{ color: compareColor }} className="comparative-number">
        {parsedCompareValue}
      </span>
    );
  }
  return '';
};
