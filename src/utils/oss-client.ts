import OSS from 'ali-oss';
import { message } from 'antd';

export class ClientSts {
  private ossClient: OSS;
  private policy: any;
  private bucketName: string;
  private getPolicySign: () => Promise<any>;
  constructor(params: { policy: any; bucketName: string; getPolicySign: () => Promise<any> }) {
    this.policy = params.policy;
    this.bucketName = params.bucketName;
    this.getPolicySign = params.getPolicySign;
  }

  getOssClient() {
    if (this.ossClient) {
      return this.ossClient;
    }
    const client = new OSS({
      timeout: 300000,
      ...this.policy,
      // 填写Bucket名称。
      bucket: this.bucketName,
      refreshSTSToken: async () => {
        const p = await this.getPolicySign();
        if (p) {
          this.policy = p;
          return {
            accessKeyId: p.accessKeyId,
            accessKeySecret: p.accessKeySecret,
            stsToken: p.stsToken,
          };
        } else {
          message.error('token更新失败，请刷新页面');
        }
      },
    });
    this.ossClient = client;
    return this.ossClient;
  }

  async uploadOSS(file: File, fileKey: string) {
    const client = this.getOssClient();
    await client.put(fileKey, file).catch((e: any) => {
      console.error('uploadOSS Error', e);
      message.error(e?.message || '上传失败');
      throw e;
    });
  }
}
