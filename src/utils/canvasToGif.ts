import GIF from '@dhdbstjr98/gif.js';

export const canvasToGif = async (params: {
  domId: string;
  fileName: string;
  fps?: number; // 每帧延迟时间（毫秒）
  duration?: number;
  quality?: number; // 质量 1-30，1最好
}) => {
  // 把fps转换为delay
  const { domId, fileName, duration = 4000, quality = 30, fps = 50 } = params;
  const delay = fps ? Math.floor(1000 / fps) : 60;

  // canvas是 domId下的一个节点
  const canvas = document.getElementById(domId).querySelector('canvas');
  if (canvas?.captureStream) {
    // 开始录制
    const gifBlob = await recordCanvasToGif(canvas, { duration, delay, quality });

    // 创建下载链接
    const url = URL.createObjectURL(gifBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.gif`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
};

function recordCanvasToGif(
  canvas: HTMLCanvasElement,
  options: {
    duration: number;
    delay: number;
    quality: number;
  },
): Promise<Blob> {
  const { duration, delay, quality } = options;
  return new Promise((resolve, reject) => {
    const gif = new GIF({
      workers: 2, // 工作线程数
      quality, // 质量 1-30，1最好
      width: canvas.width,
      height: canvas.height,
    });
    const startTime = Date.now();
    let isRecording = true;
    function captureFrame() {
      const nowDate = Date.now();
      if (!isRecording) return;
      gif.addFrame(canvas, {
        copy: true,
        delay,
        scale: 0.5,
      });
      if (nowDate - startTime < duration) {
        const progress = Math.ceil(((nowDate - startTime) / duration) * 100);
        console.log(`进度：${progress}%`);
        requestAnimationFrame(captureFrame);
      } else {
        isRecording = false;
        gif.render();
      }
    }

    gif.on('finished', function (blob) {
      resolve(blob);
    });

    // GIF 渲染出错的回调
    gif.on('error', function (error) {
      reject(error);
    });
    captureFrame();
  });
}
