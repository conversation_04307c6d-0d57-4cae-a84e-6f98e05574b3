import AESTrace from '@alife/amap-aes-trace';
import { recordCLK, recordEXP } from '@alife/amap-tracker';
import { PageSPMKey, ModuleSPMKey } from './traceMap';

const aes = new AESTrace();

const getTraceArgs = (...args: any) => {
  return {
    type: 'OTHER',
    c1: args?.[0],
    c2: args?.[1],
    c3: args?.[2],
    c4: args?.[3],
    c5: args?.[4],
    c6: args?.[5],
    c7: args?.[6],
  };
};
const EventKey = 'xy-task';

export function trace(...args: any[]) {
  aes?.sendEvent?.(EventKey, getTraceArgs(...args));
}

export function traceExp(
  page: PageSPMKey,
  event: ModuleSPMKey | string,
  params: Record<string, string | number | boolean | undefined> = {},
) {
  recordEXP(`amap.${page}.${event}`, {
    params,
  });
}

export function traceClick(
  page: PageSPMKey,
  event: ModuleSPMKey | string,
  params: Record<string, string | number | boolean | undefined> = {},
) {
  recordCLK(`amap.${page}.${event}`, {
    params,
  });
}

// 重新导出枚举，方便外部使用
export { PageSPMKey, ModuleSPMKey } from './traceMap';
