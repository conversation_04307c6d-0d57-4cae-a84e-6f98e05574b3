export enum PageSPMKey {
  首页 = 'xy-task-pc-home',
}

/**
 * 埋点事件映射表 - 统一管理所有埋点事件名称
 * 使用中文描述，映射到具体的英文事件名
 * 通过 traceExp() 和 traceClick() 方法来区分曝光和点击事件
 * 采用模块.功能的层级结构
 */
export enum ModuleSPMKey {
  // 核心任务完成率模块
  '核心任务完成率' = 'core_task_completion',
  '核心任务完成率.任务' = 'core_task_completion.task',
  '核心任务完成率.卡片' = 'core_task_completion.card',

  // 柱状图模块
  '柱状图' = 'target_dashboard',
  '柱状图.广告任务' = 'target_dashboard.ad_task',
  '柱状图.年费任务' = 'target_dashboard.annual_task',
  '柱状图.广告Tab' = 'target_dashboard.ad_tab',
  '柱状图.年费Tab' = 'target_dashboard.annual_tab',
  '柱状图.广告柱子' = 'target_dashboard.ad_chart',
  '柱状图.年费柱子' = 'target_dashboard.annual_chart',

  // 待办任务模块
  '待办任务' = 'todo_list',
  '待办任务.任务' = 'todo_list.task',
  '待办任务.广告任务' = 'todo_list.ad_task',
  '待办任务.续签任务' = 'todo_list.renewal_task',
  '待办任务.预警任务' = 'todo_list.warning_task',
  '待办任务.基建任务' = 'todo_list.infrastructure_task',
  '待办任务.紧急待办Tab' = 'todo_list.urgent_tab',
  '待办任务.紧急待办数据' = 'todo_list.urgent_data',
  '待办任务.今日必做Tab' = 'todo_list.today_tab',
  '待办任务.今日必做数据' = 'todo_list.today_data',
  '待办任务.全部待办Tab' = 'todo_list.all_tab',
  '待办任务.全部待办数据' = 'todo_list.all_data',
  '待办任务.任务项' = 'todo_list.task_item',

  // 企微任务模块
  '企微任务' = 'qw_task',
  '企微任务.任务' = 'qw_task.task',
  '企微任务.自动发送' = 'qw_task.auto_send',
  '企微任务.发送至企微群' = 'qw_task.send_to_qw',

  // 商户列表模块
  '商户列表' = 'merchant_list',
  '商户列表.商户曝光' = 'merchant_list.merchant_exposure',
  '商户列表.筛选' = 'merchant_list.filter',
  '商户列表.操作项' = 'merchant_list.operation',

  // 门店列表模块
  '门店列表' = 'shop_list',
  '门店列表.筛选' = 'shop_list.filter',
  '门店列表.操作项' = 'shop_list.operation',
  '门店列表.批量提报按钮' = 'shop_list.batch_submit_button',

  // 喜报模块
  '喜报' = 'business_news',
  '喜报.AI智能分析' = 'business_news.ai_analysis',
  '喜报.字段切换' = 'business_news.field_toggle',
  '喜报.明细' = 'business_news.detail',
  '喜报.下载明细表' = 'business_news.download_table',
  '喜报.下载图片' = 'business_news.download_image',
  '喜报.发到企微群' = 'business_news.send_to_qw',

  // 拜访记录模块
  '拜访记录' = 'visit_record',
  '拜访记录.商户详情Tab' = 'visit_record.merchant_detail_tab',
  '拜访记录.拜访记录Tab' = 'visit_record.history_tab',
  '拜访记录.复盘数据Tab' = 'visit_record.replay_tab',
  '拜访记录.记拜访' = 'visit_record.create',
  '拜访记录.tab切换' = 'visit_record.tab_switch',
  '拜访记录.继续填写' = 'visit_record.continue_fill',

  // 投放方案模块
  '投放方案' = 'ad_plan',
  '投放方案.模块曝光' = 'ad_plan.module_exposure',
  '投放方案.操作' = 'ad_plan.operation',
  '投放方案.查询' = 'ad_plan.query',
  '投放方案.新增' = 'ad_plan.create',
  '投放方案.删除' = 'ad_plan.delete',
  '投放方案.关闭修改' = 'ad_plan.close_edit',
  '投放方案.移除门店' = 'ad_plan.remove_shop',
  '投放方案.下载' = 'ad_plan.download_excel',
  '投放方案.下载图片' = 'ad_plan.download_image',
  '投放方案.发送至企微群' = 'ad_plan.send_to_qw',
  '投放方案.切换预览模式' = 'ad_plan.switch_preview_mode',
  // 基建任务模块
  '基建任务' = 'infrastructure_task',
  '基建任务.模块曝光' = 'infrastructure_task.module_exposure',
  '基建任务.任务' = 'infrastructure_task.task',
  '基建任务.任务卡片点击' = 'infrastructure_task.task_card_click',
  '基建任务.素材提报' = 'infrastructure_task.material_submit',
  '基建任务.装修素材提报' = 'infrastructure_task.decoration_material_submit',
  '基建任务.提报审核' = 'infrastructure_task.submit_audit',
  '基建任务.商家分任务' = 'infrastructure_task.merchant_assign',

  // 年费续签任务模块
  '年费续签任务' = 'annual_renewal_task',
  '年费续签任务.模块曝光' = 'annual_renewal_task.module_exposure',
  '年费续签任务.任务' = 'annual_renewal_task.task',
  '年费续签任务.任务卡片点击' = 'annual_renewal_task.task_card_click',
  '年费续签任务.任务详情曝光' = 'annual_renewal_task.task_detail_exposure',
  '年费续签任务.任务操作' = 'annual_renewal_task.task_operation',

  // 商户详情模块
  '商户详情' = 'merchant_detail',
  '商户详情.任务曝光' = 'merchant_detail.task_exposure',
  '商户详情.高优任务曝光' = 'merchant_detail.high_priority_task_exposure',
  '商户详情.去完成任务按钮点击' = 'merchant_detail.to_do_task_btn',

  '商家分任务' = 'merchant_score_task',
  '视角切换' = 'viewer_switch',
  门店详情 = 'shop_detail',
  机器人 = 'robotBlock_Robot.arouseRobot',

  // 批量提报模块
  '批量提报' = 'batch_submit',
  '批量提报.按钮点击' = 'batch_submit.button_click',
  '批量提报.弹窗曝光' = 'batch_submit.modal_exposure',
  '批量提报.门店选择步骤' = 'batch_submit.shop_select_step',
  '批量提报.门店选择.筛选' = 'batch_submit.shop_select.filter',
  '批量提报.门店选择.选择门店' = 'batch_submit.shop_select.select_shop',
  '批量提报.门店选择.下一步' = 'batch_submit.shop_select.next_step',
  '批量提报.门店选择.取消' = 'batch_submit.shop_select.cancel',
  '批量提报.提报审核步骤' = 'batch_submit.submit_review_step',
  '批量提报.提报审核.设置目标门店' = 'batch_submit.submit_review.set_target',
  '批量提报.提报审核.提交' = 'batch_submit.submit_review.submit',
  '批量提报.提报审核.取消' = 'batch_submit.submit_review.cancel',
  '批量提报.提交成功' = 'batch_submit.submit_success',
}

export type ModuleSPMKeyType = keyof typeof ModuleSPMKey;
