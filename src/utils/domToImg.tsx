import { uploadToOSS } from '@/services/oss';
import { message } from 'antd';
import html2canvas, { Options } from 'html2canvas';

interface IDomToImg {
  (params: { fileName: string; isDownload: true; dom: Element }): Promise<void>;
}
interface IDomToImg {
  (params: { isUpload: true; dom: Element }): Promise<string>;
}
interface IDomToImg {
  (params: { domId: string; fileName: string; isDownload: true }): Promise<void>;
}

interface IDomToImg {
  (params: { domId: string; isUpload: true }): Promise<string>;
}

// @ts-ignore 动态参数
export const domToImg: IDomToImg = async (params) => {
  const { domId, isUpload, isDownload, fileName = '', dom } = params;
  const opts: Partial<Options> = {
    logging: false,
    scale: 2,
    useCORS: true,
    allowTaint: true,
    ignoreElements: (element) => {
      // 截图时，过滤掉空样式
      if (element.id === 'empty') {
        return true;
      }
      return false;
    },
  };

  const res = await html2canvas(dom || (document?.getElementById(domId) as any), opts as any);
  if (isDownload) {
    const base64 = res.toDataURL('image/png', 1);
    const link = document.createElement('a');
    link.download = `${fileName}.png`;
    link.href = base64;
    link.click();
    return Promise.resolve();
  }
  if (isUpload) {
    return new Promise((resolve, reject) => {
      res.toBlob(
        async (blob) => {
          if (!blob) {
            message.error('图片导出失败');
          } else {
            const file = new File([blob], fileName, {
              type: 'image/png',
            });
            try {
              const ossRes = await uploadToOSS(file);
              resolve(ossRes.url);
            } catch (err) {
              reject(err);
            }
          }
        },
        'image/png',
        1,
      );
    });
  }
};
