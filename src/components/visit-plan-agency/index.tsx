import React, { useEffect } from 'react';
import { Al<PERSON>, Button } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import './index.less';
import { showVisitRecordFormPanel } from './visit-record-form';
import { PID_VISIT } from '@/common/const';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

interface IProps {
  missVisitRecordList: string[];
  loadMissVisitRecord: () => void;
  scene?: string;
  pid?: string;
}

export const VisitPlanAgency: React.FC<IProps> = ({
  missVisitRecordList,
  loadMissVisitRecord,
  scene,
  pid,
}) => {
  useEffect(() => {
    loadMissVisitRecord();
  }, [loadMissVisitRecord]);

  const handleClick = () => {
    // 添加继续填写拜访记录点击埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['拜访记录.继续填写'], {
      pid,
    });

    showVisitRecordFormPanel({
      callRecordId: missVisitRecordList[0],
      contactScene: scene || PID_VISIT,
    });
  };

  return (
    <>
      {missVisitRecordList.length > 0 && (
        <Alert
          type="warning"
          className="visit-plan-alert"
          message={
            <div className="visit-plan-alert-content">
              <span>请先提交拜访记录后再继续拨打电话</span>
              <Button type="link" className="visit-plan-alert-btn" onClick={handleClick}>
                继续填写
                <RightOutlined />
              </Button>
            </div>
          }
        />
      )}
    </>
  );
};
