.visit-plan-alert {
  margin-bottom: 10px !important;
  font-size: 12px !important;
  &-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &-btn {
    padding: 0;
    font-size: 12px;
    .anticon-right {
      margin-left: 4px;
    }
  }
}

.visit-record-container {
  background: #fff;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  
  .visit-record-header {
    padding: 12px 0;
    border-bottom: solid 1px #eee;
  }

  .create-visit-record {
    padding: 16px 10px;
  }
}

.ant-btn-link {
  color: #1677ff !important;
}
.ant-btn-link:hover {
  opacity: 0.7;
}