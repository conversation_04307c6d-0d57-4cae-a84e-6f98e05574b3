import { useRequest } from 'ahooks';
import { message } from 'antd';
import { C33_CALL_ENUM } from '@/common/const';
import service from '@/_docplus/target/service/alsc-kbt-intergration-toolkit-client/CallCenterQueryGatewayService';

export const useGetCallRecordMissingVisit = () => {
  const {
    loading,
    data: missVisitRecordList,
    run: loadMissVisitRecord,
  } = useRequest(
    async () => {
      const params = {
        bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
      };
      try {
        const res = await service.getCallRecordMissingVisit(params);
        const resData: string[] = res?.data || [];
        return resData;
      } catch (err) {
        message.error(err?.message || '系统异常，请稍后再试～');
        return [];
      }
    },
    {
      manual: true,
    },
  );

  return {
    loading,
    missVisitRecordList,
    loadMissVisitRecord,
  };
};
