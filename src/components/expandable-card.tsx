import { PropsWithChildren, useEffect, useState } from 'react';
import Card, { ICardProps } from './card';
import { Button } from 'antd';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

interface IProps extends ICardProps {
  defaultExpand?: boolean;
  onExpandChange?: (expand: boolean) => void;
  header?: React.ReactNode;
  contentIncludesHeader?: boolean;
  expanded?: boolean;
  disabled?: boolean;
}
export default function ExpandableCard(props: PropsWithChildren<IProps>) {
  const {
    defaultExpand,
    onExpandChange,
    actions = [],
    header = null,
    contentIncludesHeader = false,
    disabled = false,
    ...rest
  } = props;
  const [expanded, setExpanded] = useState(defaultExpand || false);
  const handleExpandChange = () => {
    const newVal = !expanded;
    setExpanded(newVal);
    onExpandChange?.(newVal);
  };
  useEffect(() => {
    if (props.expanded !== undefined) {
      setExpanded(props.expanded);
    }
  }, [props.expanded]);
  return (
    <Card
      {...rest}
      actions={[
        ...actions,
        disabled ? null : (
          <Button
            type="text"
            iconPosition="end"
            onClick={handleExpandChange}
            disabled={false}
            icon={expanded ? <CaretUpOutlined /> : <CaretDownOutlined />}
          >
            {expanded ? '收起' : '展开'}
          </Button>
        ),
      ]}
    >
      {!expanded && !disabled ? (
        header
      ) : (
        <div>
          {contentIncludesHeader && !disabled ? (
            <div style={{ marginBottom: 7 }}>{header}</div>
          ) : null}
          <div>{props.children}</div>
        </div>
      )}
    </Card>
  );
}
