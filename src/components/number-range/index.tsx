import { CloseOutlined } from '@ant-design/icons';
import { useClickAway } from 'ahooks';
import { InputNumber } from 'antd';
import { useMemo, useRef, useState } from 'react';
import styled from 'styled-components';

interface IProps {
  value?: number[];
  onChange?: (value: number[]) => void;
  min?: number;
  max: number;
  step?: number;
  precision?: number;
  allowEqual?: boolean;
}
const isExist = (val) => {
  return typeof val === 'number';
};
const Container = styled.div`
  width: 100%;
  height: 32px;
  border: 1px solid #ccc;
  border-radius: 4px;
  line-height: 32px;
  display: flex;
  align-items: center;
  gap: 4px;
  .ant-input-number {
    height: 26px;
    line-height: 1;
    background: unset;
    text-align: center;
    border: none;
    text-align-last: center;
    flex: 1;
    &:focus,
    &:focus-within {
      box-shadow: unset;
    }
  }
  .clear-icon {
    cursor: pointer;
    visibility: hidden;
  }
  &:hover {
    .clear-icon {
      visibility: visible;
    }
  }
`;
export default function NumberRanger(props: IProps) {
  const { value, onChange, min = 0, max, step = 1, precision, allowEqual = false } = props;
  const [range, setRange] = useState(value || []);
  const ref = useRef<any>(null);

  const handleInputChange = (index: number, v: number) => {
    const newRange = [range?.[0], range?.[1]];
    newRange[index] = v;
    setRange(newRange);
    if (newRange.every((item) => typeof item === 'number')) {
      onChange?.(newRange);
    }
  };
  useClickAway(() => {
    if (!range.every((item, index) => item === value?.[index])) {
      setRange(value);
    }
  }, ref);
  const leftLimit = useMemo(() => {
    let limit = max;
    if (isExist(range?.[1])) {
      if (allowEqual) {
        limit = range[1];
      } else {
        limit = range[1] - step;
      }
    }
    return limit;
  }, [range, allowEqual, max, step]);
  const rightLimit = useMemo(() => {
    let limit = min;
    if (isExist(range?.[0])) {
      if (allowEqual) {
        limit = range[0];
      } else {
        limit = range[0] + step;
      }
    }
    return limit;
  }, [range, allowEqual, min, step]);

  return (
    <Container ref={ref}>
      <InputNumber
        min={min}
        onChange={(v) => handleInputChange(0, v)}
        step={step}
        value={range?.[0]}
        precision={precision}
        max={leftLimit}
      />
      -
      <InputNumber
        max={max}
        min={rightLimit}
        onChange={(v) => handleInputChange(1, v)}
        value={range?.[1]}
        precision={precision}
        step={step}
      />
      <CloseOutlined
        className="clear-icon"
        style={{ marginLeft: 5, opacity: 0.4, fontSize: 12 }}
        onClick={() => {
          onChange([]);
          setRange([]);
        }}
      />
    </Container>
  );
}
