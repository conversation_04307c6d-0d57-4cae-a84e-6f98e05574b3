import React, { useEffect, useState } from 'react';
import './index.less';
import { Select, message } from 'antd';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import {
  AgentOperationMerchantRelationListRequest,
  AgentOperationMerchantRelationDTO,
} from '@/_docplus/target/types/amap-sales-operation-client';
import { getBaseRequestParam, getNewBaseListRequestParam } from '@/common/utils';
import { debounce } from 'lodash';
import { TASK_STATUS_KEY } from '@/common/const';

interface IProps {
  defaultPid?: string;
  onSelectChange: (value: AgentOperationMerchantRelationDTO) => void;
  allowClear?: boolean;
}
export const MerchantSelect: React.FC<IProps> = (props: IProps) => {
  const { defaultPid, onSelectChange, allowClear = true } = props;
  const [merchantList, setMerchantList] = useState<AgentOperationMerchantRelationDTO[]>([]);
  const queryMerchantList = (keyword = '') => {
    // 判断是否纯数字，数字用pid精确查询，否则商品名称模糊查询
    const searchKey = /^\d+$/.test(keyword) ? { pid: keyword } : { merchantName: keyword };
    const listParam: AgentOperationMerchantRelationListRequest = {
      source: 'MERCHANT_BOX_LIST',
      ...searchKey,
      ...getNewBaseListRequestParam(100, 1, TASK_STATUS_KEY.SHOP),
      ...getBaseRequestParam(),
    };
    service
      .queryAgentOperationMerchantList(listParam)
      .then((res) => {
        let { dataList: list } = res?.data || {};
        if (list?.length > 0) {
          const copyList = list;
          list = [];
          copyList.forEach((item) => {
            if (!list.find((newItem) => newItem.pid === item.pid)) {
              list.push(item);
            }
          });
        }
        setMerchantList(list || []);
        if (!res.success) {
          message.error(res?.msgInfo || '系统异常，请稍后再试～');
        }
      })
      .catch((e) => {
        console.error('queryAgentOperationMerchantList error: ', e);
      });
  };

  useEffect(() => {
    queryMerchantList();
  }, []);

  const handleSearch = debounce((keyword: string) => {
    queryMerchantList(keyword);
  }, 500);

  const handleMerchantChange = (value) => {
    if (!value) {
      onSelectChange({} as any);
      return;
    }
    const selectItem = merchantList.find((item) => item.pid === value);
    if (selectItem) {
      onSelectChange(selectItem);
    }
  };

  return (
    <Select
      className="merchant-select"
      allowClear={allowClear}
      showSearch
      filterOption={false}
      defaultValue={defaultPid}
      onClear={() => {
        onSelectChange(null as any);
      }}
      onSelect={handleMerchantChange}
      onSearch={handleSearch}
      placeholder="请选择商户"
      options={merchantList.map((item) => ({
        ...item,
        value: item.pid,
        label: item.merchantName,
      }))}
      optionRender={(item) => (
        <div>
          <div>{item.data.merchantName}</div>
          <div style={{ color: '#999' }}>ID: {item.data.pid}</div>
        </div>
      )}
      {...props}
    />
  );
};
