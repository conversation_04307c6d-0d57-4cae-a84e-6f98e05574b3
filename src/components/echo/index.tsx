import { WorkSpacePlugin, useEcho, WorkspaceEcho, IWorkspacePlugin } from '@alife/echo-client';
import { useUnmount } from 'ahooks';
import { useEffect } from 'react';
import { echoWorkbench } from '@/common/echo';
import styled from 'styled-components';

const StyledEcho = styled(WorkspaceEcho)`
  span {
    font-size: 14px;
    font-weight: 400;
  }
  .ant-input {
    background: unset;
  }
`;
export const Echo = () => {
  const EchoData = useEcho<[IWorkspacePlugin]>(
    {
      bizInfo: {
        bizScene: 'C33_AGENT_OUT_CALL',
        // @ts-ignore targetType force null
        targetType: null,
        // @ts-ignore targetId force null
        targetId: null,
      },
    },
    {
      plugins: [WorkSpacePlugin],
    },
  );
  const { echo, isBusy } = EchoData;
  useEffect(() => {
    echoWorkbench.echo = echo;
    if (echo) {
      echoWorkbench.init();
    }
  }, [echo]);

  useUnmount(() => {
    if (isBusy) {
      echo?.hangUp?.();
    }
    echo?.destroy?.();
  });
  return <StyledEcho Echo={EchoData} disableInput />;
};
