import { Flex, Space } from 'antd';
import React, { PropsWithChildren } from 'react';
import styled from 'styled-components';

const Container = styled.div`
  background: #fff;
  border-radius: 6px;
`;
export interface ICardProps {
  style?: React.CSSProperties;
  title?: string | React.ReactNode;
  actions?: React.ReactNode[];
  cardRef?: React.RefObject<HTMLDivElement>;
}
export default function Card(props: PropsWithChildren<ICardProps>) {
  const actions = props.actions || [];
  return (
    <Container style={{ padding: 15, ...(props.style || {}) }} ref={props.cardRef}>
      {props.title && (
        <Flex justify="space-between" align="center" style={{ marginBottom: 10 }}>
          <div style={{ fontSize: 16, fontWeight: 500 }}>{props.title}</div>
          {actions.length ? <Space>{actions}</Space> : null}
        </Flex>
      )}
      {props.children}
    </Container>
  );
}
