import React, { useRef, useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import type { SelectProps } from 'antd';
import { useAntdTable } from 'ahooks';
import { getMerchantList } from '@/services/ai-material';
import type { IMerchantItem, IQueryMerchantListParams } from '@/types/ai-material/merchant';
import { trace } from '@/utils/trace';

interface MerchantSelectV2Props {
  value?: IMerchantItem;
  onChange?: (merchant: IMerchantItem | undefined) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  autoSelectByPid?: string; // 根据pid自动选择商户
  onAutoSelect?: (merchant: IMerchantItem) => void; // 自动选择时的回调
  defaultValue?: string; // 默认商户 pid
}

const PAGE_SIZE = 20;

const MerchantSelectV2: React.FC<MerchantSelectV2Props> = ({
  value,
  onChange,
  placeholder = '请选择商户或输入pid搜索',
  style,
  autoSelectByPid,
  onAutoSelect,
  defaultValue,
}) => {
  const searchRef = useRef('');
  const [list, setList] = useState<IMerchantItem[]>([]);
  const { loading, pagination } = useAntdTable(
    async ({ current, pageSize }) => {
      const params: IQueryMerchantListParams = {
        source: 'AI_SUPPLY_TOOL',
        page: {
          pageNo: current,
          pageSize,
        },
      };
      if (searchRef.current) {
        params.pid = searchRef.current;
      } else if (defaultValue) {
        params.pid = defaultValue;
      }
      const res = await getMerchantList(params);
      const dataList = res.dataList || [];
      if (current === 1) {
        setList(dataList);
      } else {
        setList((prev) => [...prev, ...dataList]);
      }
      // 默认值自动选中
      if (defaultValue && !value?.pid && current === 1) {
        const matchedMerchant = dataList.find((m) => m.pid === defaultValue);
        if (matchedMerchant) {
          onChange?.(matchedMerchant);
        }
      }
      return {
        list: dataList,
        total: res.pageInfo?.totalCount || 0,
      };
    },
    {
      defaultPageSize: PAGE_SIZE,
      refreshDeps: [defaultValue],
      debounceWait: 300,
      debounceLeading: false,
      debounceTrailing: true,
    },
  );

  // 根据pid自动选择商户
  useEffect(() => {
    if (!autoSelectByPid || !list.length || loading) return;
    if (value?.pid === autoSelectByPid) return;
    const matchedMerchant = list.find((m) => m.pid === autoSelectByPid);
    if (matchedMerchant && onAutoSelect) {
      onAutoSelect(matchedMerchant);
    }
  }, [autoSelectByPid, list, loading, value?.pid, onAutoSelect]);

  // 搜索时重置分页
  const handleSearch = (val: string) => {
    searchRef.current = val;
    pagination.onChange(1, PAGE_SIZE);
  };

  // 下拉滚动加载更多
  const handlePopupScroll: React.UIEventHandler<HTMLDivElement> = (e) => {
    const target = e.target as HTMLDivElement;
    if (
      target.scrollTop + target.offsetHeight >= target.scrollHeight - 20 &&
      !loading &&
      list.length < pagination.total
    ) {
      pagination.onChange(pagination.current + 1, PAGE_SIZE);
    }
  };

  const handleChange: SelectProps<string>['onChange'] = (pid) => {
    const merchant = list.find((item) => item.pid === pid);
    onChange?.(merchant);
    trace('merchant-select-v2-change', { pid: merchant?.pid, name: merchant?.merchantName });
  };

  return (
    <Select
      showSearch
      allowClear
      value={value?.pid}
      placeholder={placeholder}
      style={style}
      loading={loading}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      optionLabelProp="label"
      onPopupScroll={handlePopupScroll}
    >
      {list.map((item) => (
        <Select.Option key={item.pid} value={item.pid} label={item.merchantName}>
          <span>{item.merchantName}</span>
          {item.hasQwGroup && (
            <span style={{ color: '#52c41a', marginLeft: 8, fontSize: 12 }}>(有企微群)</span>
          )}
        </Select.Option>
      ))}
    </Select>
  );
};

export default MerchantSelectV2;
