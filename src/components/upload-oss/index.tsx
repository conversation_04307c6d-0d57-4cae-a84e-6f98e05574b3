import { uploadToOSS } from '@/services/oss';
import { getFileSize } from '@/utils';
import { DeleteOutlined } from '@ant-design/icons';
import { Button, Image, message, Space, Spin, Upload, UploadProps } from 'antd';
import React, { PropsWithChildren } from 'react';

interface IProps {
  onlyImg?: boolean;
  imgSize?: number;
  fileSizeLimit?: number;
  onChange?: (fileList: any[]) => void;
  uploading?: boolean;
  setUploading?: (uploading: boolean) => void;
}

export default function UploadOss(props: PropsWithChildren<UploadProps> & IProps) {
  const {
    onlyImg = true,
    imgSize = 120,
    fileSizeLimit = 20,
    maxCount,
    setUploading,
    uploading,
  } = props;
  const fileList = props.fileList || [];
  const maxFileSizeLimit = getFileSize(fileSizeLimit);
  const beforeUpload: UploadProps['beforeUpload'] = async (file) => {
    if (maxCount && fileList.length >= maxCount) {
      message.error(`最多支持上传${maxCount}个文件`);
      return false;
    }
    if (file.size > maxFileSizeLimit) {
      message.error(`文件: ${file.name}, 超过 ${fileSizeLimit}MB限制`);
      return false;
    }
    setUploading?.(true);
    return uploadToOSS(file, false)
      .then((res) => {
        // @ts-ignore fileUrl
        file.url = res.url;
      })
      .catch(() => {
        message.error(`${file.name} 上传失败`);
      })
      .finally(() => {
        setUploading?.(false);
      });
  };
  const onRemove = (url: string) => {
    props.onChange?.(fileList.filter((file) => url !== file.url));
  };
  return (
    <Spin spinning={!!uploading}>
      {onlyImg && (
        <Space style={{ display: 'flex' }} wrap>
          {fileList.map((file) => (
            <div>
              <Image src={file.url} alt={file.name} width={imgSize} height={imgSize} />
              <Button
                danger
                type="text"
                onClick={() => {
                  onRemove(file.url);
                }}
                size="small"
                style={{ margin: '2px auto', display: 'block' }}
              >
                <DeleteOutlined />
              </Button>
            </div>
          ))}
        </Space>
      )}

      <Upload
        showUploadList={!onlyImg}
        beforeUpload={beforeUpload}
        accept={onlyImg ? 'image/png, image/jpg, image/jpeg' : ''}
        {...props}
        onChange={(info) => {
          props.onChange?.(info.fileList.filter((item) => !!item.url));
        }}
      >
        {!uploading ? props.children || <Button type="primary">上传附件</Button> : null}
      </Upload>
    </Spin>
  );
}
