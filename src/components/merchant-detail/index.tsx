import React from 'react';
import { Descriptions, Tag, Spin } from 'antd';
import { Block } from '@/components/block';
import './index.less';
import { getMerchantLabels } from '@/common/utils';
import { AgentOperationMerchantDetailDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import { isAmapXy } from '@/utils';

const { Item } = Descriptions;

interface IProps {
  detail: AgentOperationMerchantDetailDTO;
  loading: boolean;
}

export const MerchantDetail: React.FC<IProps> = ({ detail, loading }) => {
  return (
    <Spin spinning={loading}>
      <div className="merchant-detail-wrapper">
        <div className="merchant-detail-content">
          <h3 className="merchant-title">商户名称：{detail?.merchantName || '-'}</h3>
          <div>PID：{detail?.pid || '-'}</div>
          <div>主店名：{detail?.mainShopName || '-'}</div>
          <div className="merchant-labels">
            商户标签：
            {(detail?.merchantLabels?.length > 0 &&
              detail?.merchantLabels?.map((item: string) => (
                <Tag
                  color={getMerchantLabels(item).color}
                  key={item}
                  className="merchant-labels-tag"
                >
                  {getMerchantLabels(item).value}
                </Tag>
              ))) ||
              '-'}
          </div>
        </div>
        <Block title="广告数据">
          <Descriptions bordered column={1} size="small">
            <Item label={<div style={{ width: 130 }}>当月广告总消耗</div>}>
              <div className="item-content">{detail?.adCurrentMonthCost || '-'}</div>
            </Item>
            <Item label={<div style={{ width: 130 }}>广告现金余额</div>}>
              <div className="item-content">{detail?.adCurrentBalance || '-'}</div>
            </Item>
          </Descriptions>
        </Block>
        <Block title="门店数据">
          <Descriptions bordered column={1} size="small">
            <Item label={<div style={{ width: 130 }}>营业门店总数</div>}>
              <div className="item-content">{detail?.shopOpenNum || '-'}</div>
            </Item>
            <Item
              label={<div style={{ width: 130 }}>{isAmapXy() ? '年费' : '商户通'}在约门店总数</div>}
            >
              <div className="item-content">{detail?.shangHuTongShopNum || '-'}</div>
            </Item>
          </Descriptions>
        </Block>
      </div>
    </Spin>
  );
};
