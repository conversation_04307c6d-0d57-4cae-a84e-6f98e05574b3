import { Progress, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const TargetCard = styled.div`
  padding: 25px;
  flex: 1;
  position: relative;
  &:not(:last-child) {
    &::after {
      position: absolute;
      content: '';
      right: 0;
      top: 10%;
      width: 1px;
      height: 80%;
      background: #e5e5e5;
    }
  }
`;

const TitleSub = styled.div`
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
`;

const CardValue = styled.div<{ $color?: string }>`
  font-size: 24px;
  color: ${(props) => props.$color || '#1677ff'};
  margin: 8px 0;
`;

const CardDesc = styled.div`
  position: relative;
  margin-bottom: 8px;
`;

export interface ICompletionRateCardProps {
  title: string;
  rate: string;
  tips?: string;
  targetValue?: string;
  color?: string;
  onClick?: () => void;
  completedNum?: number;
  assignmentsNum?: number;
}

export const CompletionRateCard = (props: ICompletionRateCardProps) => {
  const { title, rate, tips, targetValue, color, onClick, completedNum, assignmentsNum } = props;
  const getProgressColor = () => {
    // 如果传入了自定义颜色，使用自定义颜色
    if (color) {
      return color;
    }

    if (!targetValue || !Number(targetValue) || targetValue === '-') {
      return 'grey';
    }
    if (Number(rate) > 50) {
      return '#1677ff';
    }
    return 'red';
  };

  return (
    <TargetCard onClick={onClick}>
      <div>
        <TitleSub>
          <span>{title}</span>
          {tips && (
            <Tooltip title={tips}>
              <QuestionCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
            </Tooltip>
          )}
        </TitleSub>
        <CardValue $color={color}>
          {rate || '-'}
          {rate !== '-' && rate ? '%' : ''}
        </CardValue>
        <CardDesc>
          <Progress
            strokeLinecap="butt"
            percent={rate !== '-' ? Number(rate) : 0}
            showInfo={false}
            strokeColor={getProgressColor()}
          />
          {targetValue && (
            <div
              style={{
                width: '2px',
                height: '20px',
                position: 'absolute',
                left: `${
                  targetValue ? Math.min(parseInt(targetValue.replace('%', ''), 10), 100) : 0
                }%`,
                top: '3px',
                zIndex: '1',
                background: 'rgba(0, 0, 0, 1)',
                opacity: '0.15',
              }}
            />
          )}
        </CardDesc>
        {/* 展示达标门店和应作业门店 */}
        {(completedNum !== undefined || assignmentsNum !== undefined) && (
          <CardDesc style={{ color: 'rgba(0, 0, 0, 0.65)', marginTop: 8 }}>
            达标门店：{completedNum ?? '-'} 应作业门店：
            {assignmentsNum === 0 || assignmentsNum === undefined ? '-' : assignmentsNum}
          </CardDesc>
        )}
      </div>
    </TargetCard>
  );
};

export default CompletionRateCard;
