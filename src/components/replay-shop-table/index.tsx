import React, { useEffect, useState } from 'react';
import { Table, TableProps } from 'antd';
import './index.less';
import { renderFormatCompareValue, reviewDataFormatter } from '@/utils/review-board';

interface IProps {
  tableProps: TableProps;
  fields: string[];
  type?: string;
}

export const ReplayShopTable: React.FC<IProps> = ({ tableProps, fields, type }) => {
  const renderIndicatorData = (value: any, compareValue: any) => {
    const isEmptyValue = typeof value === 'undefined' || value === null;
    return (
      <div className="indicator-number">
        <span> {isEmptyValue ? '--' : reviewDataFormatter(value)}</span>
        {renderFormatCompareValue(compareValue, isEmptyValue)}
      </div>
    );
  };

  const basicColumns = [
    {
      title: '门店名称',
      dataIndex: 'shop_name',
      width: 160,
    },
    {
      title: '城市',
      dataIndex: 'city_name',
      width: 80,
    },
    {
      title: '区域',
      dataIndex: 'district_name',
      width: 80,
    },
  ];

  const initialColumns = [
    {
      title: '广告消耗',
      dataIndex: 'ad_cost_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.ad_cost_1d_hb_diff),
    },
    {
      title: '现金消耗',
      dataIndex: 'cash_cost_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.cash_cost_1d_hb_diff),
    },
    {
      title: '日均现金消耗',
      dataIndex: 'cash_cost_1d_div_sum_day',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.cash_cost_1d_div_sum_day_hb_diff),
    },
    {
      title: '红包消耗',
      dataIndex: 'redp_cost_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.redp_cost_1d_hb_diff),
    },
    {
      title: 'CPC消耗',
      dataIndex: 'cpc_cost_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.cpc_cost_1d_hb_diff),
    },
    {
      title: 'CPC消耗占比',
      dataIndex: 'cpc_cost_1d_div_ad_cost_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.cpc_cost_1d_div_ad_cost_1d_hb_diff),
    },
    {
      title: 'CPC现金消耗',
      dataIndex: 'cpc_cash_cost_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.cpc_cash_cost_1d_hb_diff),
    },
    {
      title: 'CPC现金消耗占比',
      dataIndex: 'cpc_cash_cost_1d_div_cpc_cost_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.cpc_cash_cost_1d_div_cpc_cost_1d_hb_diff),
    },
    {
      title: 'OCPC消耗',
      dataIndex: 'ocpc_cost_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.ocpc_cost_1d_hb_diff),
    },
    {
      title: 'OCPC消耗占比',
      dataIndex: 'ocpc_cost_1d_div_ad_cost_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.ocpc_cost_1d_div_ad_cost_1d_hb_diff),
    },
    {
      title: 'OCPC现金消耗',
      dataIndex: 'ocpc_cash_cost_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.ocpc_cash_cost_1d_hb_diff),
    },
    {
      title: 'OCPC现金消耗占比',
      dataIndex: 'ocpc_cash_cost_1d_div_ocpc_cost_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.ocpc_cash_cost_1d_div_ocpc_cost_1d_hb_diff),
    },
    {
      title: '总客资量',
      dataIndex: 'all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.all_kz_cnt_1d_hb_diff),
    },
    {
      title: '客资成本',
      dataIndex: 'cash_cost_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.cash_cost_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '日均客资量',
      dataIndex: 'all_kz_cnt_1d_div_sum_day',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.all_kz_cnt_1d_div_sum_day_hb_diff),
    },
    {
      title: '电话客资',
      dataIndex: 'phone_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.phone_kz_cnt_1d_hb_diff),
    },
    {
      title: '电话客资占比',
      dataIndex: 'phone_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.phone_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '平台客资',
      dataIndex: 'plat_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.plat_kz_cnt_1d_hb_diff),
    },
    {
      title: '平台客资占比',
      dataIndex: 'plat_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.plat_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '订单客资',
      dataIndex: 'order_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.order_kz_cnt_1d_hb_diff),
    },
    {
      title: '订单客资占比',
      dataIndex: 'order_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.order_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '在线咨询客资',
      dataIndex: 'consult_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.consult_kz_cnt_1d_hb_diff),
    },
    {
      title: '在线咨询客资占比',
      dataIndex: 'consult_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.consult_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '预约礼客资',
      dataIndex: 'booking_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.booking_kz_cnt_1d_hb_diff),
    },
    {
      title: '预约礼客资占比',
      dataIndex: 'booking_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.booking_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '到店预约客资',
      dataIndex: 'arrive_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.arrive_kz_cnt_1d_hb_diff),
    },
    {
      title: '到店预约客资占比',
      dataIndex: 'arrive_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.arrive_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '高德打车客资',
      dataIndex: 'gd_car_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.gd_car_kz_cnt_1d_hb_diff),
    },
    {
      title: '高德打车客资占比',
      dataIndex: 'gd_car_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.gd_car_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '非广告客资',
      dataIndex: 'not_ad_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.not_ad_kz_cnt_1d_hb_diff),
    },
    {
      title: '非广告客资占比',
      dataIndex: 'not_ad_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.not_ad_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '广告客资',
      dataIndex: 'ad_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.ad_kz_cnt_1d_hb_diff),
    },
    {
      title: '广告客资成本',
      dataIndex: 'cash_cost_1d_div_ad_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.cash_cost_1d_div_ad_kz_cnt_1d_hb_diff),
    },
    {
      title: '可见客资量',
      dataIndex: 'visible_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.visible_kz_cnt_1d_hb_diff),
    },
    {
      title: '可见客资成本',
      dataIndex: 'ad_cost_1d_div_visible_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.ad_cost_1d_div_visible_kz_cnt_1d_hb_diff),
    },
    {
      title: '不可见客资量',
      dataIndex: 'invisible_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.invisible_kz_cnt_1d_hb_diff),
    },
    {
      title: '不可见客资占比',
      dataIndex: 'invisible_kz_cnt_1d_div_all_kz_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.invisible_kz_cnt_1d_div_all_kz_cnt_1d_hb_diff),
    },
    {
      title: '广告曝光量',
      dataIndex: 'ad_expo_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.ad_expo_cnt_1d_hb_diff),
    },
    {
      title: '广告点击量',
      dataIndex: 'ad_click_cnt_1d',
      width: 160,
      render: (_: any, record: any) => renderIndicatorData(_, record?.ad_click_cnt_1d_hb_diff),
    },
    {
      title: '广告点击率',
      dataIndex: 'ad_click_cnt_1d_div_ad_expo_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.ad_click_cnt_1d_div_ad_expo_cnt_1d_hb_diff),
    },
    {
      title: '广告日均曝光量',
      dataIndex: 'ad_expo_cnt_1d_div_sum_day',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.ad_expo_cnt_1d_div_sum_day_hb_diff),
    },
    {
      title: '千次曝光成本',
      dataIndex: 'ad_cost_1d_div_ad_expo_cnt_1d',
      width: 160,
      render: (_: any, record: any) =>
        renderIndicatorData(_, record?.ad_cost_1d_div_ad_expo_cnt_1d_hb_diff),
    },
  ];
  const [columns, setColumns] = useState([...basicColumns, ...initialColumns]);
  useEffect(() => {
    const filteredColumns = initialColumns.filter((column) => fields?.includes(column.dataIndex));
    setColumns([...basicColumns, ...filteredColumns]);
  }, [JSON.stringify(fields)]);

  return (
    <>
      {type !== 'store' && <h3 style={{ marginTop: '50px' }}>门店明细:</h3>}
      <Table
        rowKey="shopId"
        columns={columns}
        scroll={{ x: 'max-content' }}
        {...tableProps}
        className="replay-shop-table"
        id="shop-detail-table"
      />
    </>
  );
};
