.merchant-info-box {
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  padding: 8px;

  .row-info {
    display: flex;

    .info-box {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .info_item {
        display: inline-block;
        line-height: 24px;

        .info_item_data {
          max-width: 140px;
          white-space: wrap;
        }
      }

      .info-btn {
        padding: 0;
      }
    }

    .divider {
      width: 1px;
      height: 100%;
      background-color: #d9d9d9;
      margin: 5px;
    }
  }

  .visit-record-box {
    display: flex;
    flex-direction: column;

    .visit-up {
      display: flex;
      flex-direction: row-reverse;
      padding: 8px 0;
    }
  }
}

.task-board {
  padding: 24px;
  background-color: #f5f5f5;
  &-content {
    margin-top: 10px;
    padding: 20px;
    background-color: #fff;
    &-card {
      margin-bottom: 20px;
    }
  }
  &-item {
    color: rgba(0, 0, 0, 0.85);
    margin-left: 80px;
    font-weight: 500;
    line-height: 1.8;
    font-size: 16px;
  }
}

.merchant-info-selected {
  background-color: rgb(254, 247, 242);
}
