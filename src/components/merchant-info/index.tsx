import React, { useRef, useState } from 'react';
import { debounce } from 'lodash';
import { UpCircleTwoTone, DownCircleTwoTone, PlusCircleFilled } from '@ant-design/icons';
import { Row, Col, Button, Modal, Divider, message as throwMsg } from 'antd';
import { AgentOperationMerchantDetailDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import { uuid } from '@alife/kb-biz-util';
import classNames from 'classnames';
import service from '@/_docplus/target/service/alsc-kbt-intergration-toolkit-client/CallCenterQueryGatewayService';
import { ComponentLoader } from '@/components/component-loader';
import './index.less';
import { callStatusChangeKey, echoWorkbench, TeleStatusEnum } from '@/common/echo';
import { useEventHelper } from '@/common/event';
import { showVisitRecordFormPanel } from '../visit-plan-agency/visit-record-form';
import { C33_CALL_ENUM, PID_VISIT } from '@/common/const';
import { isAmapXy, getKpFlag } from '@/utils';

interface MerchantInfoProps {
  /**
   * echo外呼
   */
  callOut: (...args: any) => void;
  isMerchantSelected: boolean;
  onListPanelClick: () => void;
  merchantDetail: AgentOperationMerchantDetailDTO;
  loadMerchantDetail: (params: any) => void;
}

/**
 * 商户信息卡片（拨打在这里面哦）
 */
export const MerchantInfo: React.FC<MerchantInfoProps> = ({
  callOut,
  isMerchantSelected,
  onListPanelClick,
  merchantDetail,
  loadMerchantDetail,
}) => {
  const contactsRef = useRef(null);
  const [expanded, setExpanded] = useState(false);

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  useEventHelper(
    callStatusChangeKey,
    (status) => {
      if (status === TeleStatusEnum.RINGING) {
        showVisitRecordFormPanel({
          callRecordId: echoWorkbench.echo?.recordId,
          contactScene: PID_VISIT,
          needFefresh: { fefresh: loadMerchantDetail, params: { pid: merchantDetail?.pid } },
        });
      }
    },
    echoWorkbench,
  );

  /**
   * 外呼电话列表的展开/收起icon
   */
  let switchPhoneListIcon: React.ReactNode;
  if (expanded) {
    switchPhoneListIcon = (
      <>
        <UpCircleTwoTone />
        <span>收起</span>
      </>
    );
  } else {
    switchPhoneListIcon = (
      <>
        <DownCircleTwoTone />
        <span>展开</span>
      </>
    );
  }

  /**
   * 拨打
   * 1.先记拜访校验
   * 2.才是走外呼
   */
  const showConfirm = debounce(async (data) => {
    await service
      .getCallRecordMissingVisit({
        bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
        requestId: uuid(),
      })
      .then((res) => {
        if (res?.success) {
          if (res?.data?.length > 0) {
            // 表示有未记拜访的record，需要拦截
            Modal.warning({
              title: '你有未填写的记拜访，请填写完成后再继续拨打',
              width: 438,
              okText: '记拜访',
              onOk() {
                showVisitRecordFormPanel({
                  callRecordId: res.data[0],
                  contactScene: PID_VISIT,
                });
                setExpanded(!expanded);
              },
            });
          } else {
            const phoneNumber = isAmapXy() ? data?.contactNo : data?.mainContactWay?.contactValue;
            const cpName = isAmapXy() ? data.contactName : data.contactPersonName;
            callOut({
              phoneNumber,
              bizInfo: {
                targetId: merchantDetail?.pid,
                bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
                kpContactId: data?.contactId,
                contactPerson: {
                  cpId: data?.contactId,
                  cpName,
                  supportCorrect: data.supportCorrect,
                  cpPosition: data.roleDesc,
                  keyPersonFlag: getKpFlag(data),
                  contactNo: phoneNumber,
                },
                targetType: 'MERCHANT',
              },
            });
          }
        } else {
          // 报错
          throwMsg.error(res?.resultMessage);
        }
      })
      .catch((error) => {
        throwMsg.error(error?.errorMessage);
      });
  }, 600);

  const InfoItem = ({ label, value }: { label: React.ReactNode; value: React.ReactNode }) => (
    <div className="info_item">
      <span className="info_item_label">{label}：</span>
      <span className="info_item_data">{value}</span>
    </div>
  );

  return (
    <div>
      <div
        className={classNames({
          'merchant-info-selected': isMerchantSelected,
          'merchant-info-box': true,
        })}
        onClick={onListPanelClick}
      >
        <Row className="row-info">
          <Col span={16} className="info-box">
            <InfoItem label="商户名称" value={merchantDetail?.merchantName} />
            <InfoItem label="PID" value={merchantDetail?.pid} />
            <InfoItem label="主店名" value={merchantDetail?.mainShopName} />
          </Col>
          <Col span={1}>
            <Divider type="vertical" className="divider" />
          </Col>
          <Col span={7} className="info-box">
            <div>最近通话</div>
            <div>{merchantDetail?.lastCallTime || '暂无'}</div>
            <ComponentLoader appName="kb-visit" componentName="contact-modal">
              {(Comp) => (
                <Comp
                  entityId={merchantDetail?.pid}
                  entityType="pid"
                  scene="PID_VISIT"
                  onFinish={() => contactsRef?.current?.refresh()}
                >
                  <Button type="link" size="small" className="info-btn">
                    <PlusCircleFilled />
                    新建联系人
                  </Button>
                </Comp>
              )}
            </ComponentLoader>
            <Button type="link" size="small" className="info-btn" onClick={() => toggleExpand()}>
              {switchPhoneListIcon}
            </Button>
          </Col>
        </Row>
      </div>

      {expanded && (
        <div className="contacts-list">
          <ComponentLoader appName="kb-visit" componentName="card-contacts">
            {(Comp) => (
              <Comp
                ref={contactsRef}
                entityId={merchantDetail?.pid}
                entityType="pid"
                queryType="MERCHANT"
                scene="PID_VISIT"
                sourceFrom="XY_PC_AGENT"
                onCall={showConfirm}
              />
            )}
          </ComponentLoader>
        </div>
      )}
    </div>
  );
};
