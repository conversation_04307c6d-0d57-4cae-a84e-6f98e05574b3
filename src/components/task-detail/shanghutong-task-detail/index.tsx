import React, { useEffect, useState } from 'react';
import { ShopSelect } from '../shop-select';
import './index.less';
import { getBaseRequestParam, sendEvent } from '@/common/utils';
import { Button, Empty, Result, message } from 'antd';
import { TASK_DETAIL_TABS, PAGE_STATUS } from '@/common/const';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import {
  AgentOperationShopRelationDTO,
  AgentOperationDetailDTO,
} from '@/_docplus/target/types/amap-sales-operation-client';
import { MerchantConditionTask } from '../merchant-condition-task';
import { useStore } from '@/context/global-store';
import { ModuleSPMKey, PageSPMKey, traceExp } from '@/utils/trace';

interface IProps {
  pid: string;
  visible: boolean;
  jumpSource: string;
  data: any;
  shopList: AgentOperationShopRelationDTO[];
}
export const ShanghutongTaskDetail: React.FC<IProps> = ({
  pid,
  visible,
  jumpSource,
  data,
  shopList: shops,
}) => {
  const { shopId, taskDetailDTOList } = data || {};

  const [detailList, setDetailList] = useState(taskDetailDTOList || []);
  const [shop, setShop] = useState<AgentOperationDetailDTO>({
    shopId,
    shopQualityScore: null,
  } as any);
  const [shopList, setShopList] = useState(shops);
  const [status, setStatus] = useState<PAGE_STATUS>(
    taskDetailDTOList?.length > 0 ? PAGE_STATUS.SUCCESS : PAGE_STATUS.EMPTY,
  );

  useEffect(() => {
    if (visible) {
      if (!detailList?.length) {
        fetchList(shop);
      }
      sendEvent(TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK, 'EXP');
      traceExp(PageSPMKey.首页, ModuleSPMKey['年费续签任务.模块曝光'], {
        shopId: shop?.shopId,
      });
    } else {
      setDetailList([]);
    }
  }, [visible]);

  useEffect(() => {
    // 解决从商户列表跳转，首次使用接口返回门店，拿不到商家分问题
    if (shopList?.length > 0 && shop && shop.shopQualityScore === null) {
      const currentShop = shopList.find((item) => item.shopId === shop?.shopId);
      if (currentShop) {
        setShop(currentShop);
      }
    }
  }, [shopList]);

  const { viewer } = useStore();
  const fetchList = (shop: AgentOperationDetailDTO) => {
    const { shopId } = shop || {};
    setStatus(PAGE_STATUS.LOADING);
    const param = {
      taskDetailScene: TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK,
      pid,
      jumpSource,
      shopId,
      viewOperatorId: viewer || undefined,
      ...getBaseRequestParam(),
    };
    service
      .queryAgentOperationDetail(param)
      .then((res) => {
        const { success, data, msgInfo: resultMessage } = res || {};
        if (!success) {
          message.error(resultMessage);
          setStatus(PAGE_STATUS.ERROR);
          return;
        }
        const { taskDetailScene, taskDetailDTOList } = data || {};
        if (!data || taskDetailScene !== TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK) {
          setStatus(PAGE_STATUS.EMPTY);
          return;
        }
        if (taskDetailDTOList?.length > 0) {
          setDetailList(taskDetailDTOList);
          setStatus(PAGE_STATUS.SUCCESS);
        } else {
          setStatus(PAGE_STATUS.EMPTY);
        }
      })
      .catch((e) => {
        console.error('任务详情请求失败: ', e);
        message.error(e?.res?.resultMsg);
        setStatus(PAGE_STATUS.ERROR);
      });
  };

  const handleShopChange = (shop: AgentOperationDetailDTO) => {
    setShop(shop);
    fetchList(shop);
  };

  const handleShopList = (value: AgentOperationDetailDTO[]) => {
    setShopList(value);
    if (value?.length > 0 && !shop) {
      setShop(value[0]);
    }
  };

  return (
    <div className="task-tab-container">
      <div className="action-container">
        <ShopSelect
          pid={pid}
          shop={shop}
          shopList={shopList}
          tabKey={TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK}
          onShopChange={handleShopChange}
          onShopList={handleShopList}
        />
      </div>
      {status === PAGE_STATUS.SUCCESS && <MerchantConditionTask taskDetailDTOList={detailList} />}
      {status === PAGE_STATUS.EMPTY && <Empty style={{ margin: '16px' }} />}
      {status === PAGE_STATUS.ERROR && (
        <Result
          className="task-result"
          title="网络出错了"
          subTitle="网络开小差了，请检查网络连接后重试"
          extra={
            <Button
              type="primary"
              onClick={() => {
                fetchList(shop);
              }}
            >
              点击重试
            </Button>
          }
        />
      )}
    </div>
  );
};
