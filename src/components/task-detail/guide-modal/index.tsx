import React, { useEffect, useState } from 'react';
import { Modal, Button } from 'antd';
import { JUMP_TYPE } from '@/common/const';

import './index.less';

export interface IGuideModalProps {
  type: JUMP_TYPE; // 图片/文案
  visible: boolean;
  jumpUrl: string; // 内容：图片链接/文案内容
  title: string;
  buttonText: string;
  onCancel?: () => void;
}

export default (data: IGuideModalProps) => {
  const type: JUMP_TYPE = data?.type;
  const jumpUrl: string = data?.jumpUrl;
  const [title, setTitle] = useState<string>(data?.title);
  const [buttonText, setButtonText] = useState<string>(data?.buttonText);
  const [content, setContent] = useState<string>('');

  useEffect(() => {
    if (type === JUMP_TYPE.TEXT) {
      getTextData();
    }
  }, [type]);

  const getTextData = () => {
    let text = null;

    try {
      text = JSON.parse(jumpUrl);
    } catch (e) {
      text = null;
    }

    setTitle(text?.title);
    setContent(text?.content);
    setButtonText(text?.buttonText);
  };

  return (
    <div>
      <Modal
        className="guide-modal-component"
        title={title}
        open={data?.visible}
        width={480}
        onCancel={data?.onCancel}
        footer={[
          <Button type="primary" onClick={data?.onCancel}>
            {buttonText}
          </Button>,
        ]}
      >
        <div className="container">
          {type === JUMP_TYPE.PICTURE ? <img src={jumpUrl} alt="" /> : null}
          {type === JUMP_TYPE.TEXT ? <div className="text">{content}</div> : null}
        </div>
      </Modal>
    </div>
  );
};
