import React from 'react';

interface AuditTypeTagProps {
  auditType: 'MANUAL' | 'SEMI_AUTO' | 'AUTO';
}

const AuditTypeTag: React.FC<AuditTypeTagProps> = ({ auditType }) => {
  const getAuditTypeConfig = (
    type: string,
  ): {
    text: string;
  } => {
    switch (type) {
      case 'MANUAL':
        return {
          text: '(人工)',
        };
      case 'SEMI_AUTO':
        return {
          text: '(半自动)',
        };
      case 'AUTO':
        return {
          text: '(自动)',
        };
      default:
        return null;
    }
  };

  const config = getAuditTypeConfig(auditType);

  if (!config) {
    return null;
  }

  return (
    <div
      style={{
        display: 'inline-block',
        padding: '4px',
        lineHeight: '16px',
        marginRight: 4,
      }}
    >
      {config.text}
    </div>
  );
};

export default AuditTypeTag;
