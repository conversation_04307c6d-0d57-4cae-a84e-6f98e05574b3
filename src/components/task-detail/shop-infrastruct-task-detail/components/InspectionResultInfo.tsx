import React from 'react';
import { Flex } from 'antd';
import * as EspAuditTypes from '@/types/esp-audit';
import AuditTypeTag from './AuditTypeTag';

interface IProps {
  shopInspectionInfo?: EspAuditTypes.ShopItemInspectInfo;
  lastAuditInfo?: EspAuditTypes.LastAuditInfo;
  auditStatus?: string; // 当前审核状态
  onViewDetail?: () => void;
  auditId?: string; // 当前审核ID
}

/**
 * 质检结果信息显示组件
 * 显示上次质检结果和时间，格式：上次质检结果/时间：通过/2025-01-01 15:30
 */
const InspectionResultInfo: React.FC<IProps> = ({
  shopInspectionInfo,
  lastAuditInfo,
  auditStatus,
  onViewDetail,
  auditId,
}) => {
  // 质检状态通过后，显示最近一次通过的时间&详情链接
  const isAuditPassed = auditStatus === 'AUDIT_PASS';

  // 如果没有lastAuditInfo，则不显示任何内容
  if (!lastAuditInfo) {
    return null;
  }

  // 检查当前auditId和lastAuditInfo.auditId是否相同
  const isSameAudit = auditId === lastAuditInfo.auditId;

  // 如果没有shopInspectionInfo且不是审核通过状态，且不是同一个审核，则不显示
  if (!shopInspectionInfo && !isAuditPassed && !isSameAudit) {
    return null;
  }

  const auditTime = lastAuditInfo?.gmtTime;
  const auditStatusDesc = lastAuditInfo?.auditStatusDesc;

  // 渲染质检结果内容
  const renderContent = () => {
    const labelText = '上次质检结果/时间：';

    // 根据状态描述判断颜色
    let statusColor = '#666';
    if (auditStatusDesc?.includes('通过')) {
      statusColor = '#52c41a'; // 绿色
    } else if (auditStatusDesc?.includes('驳回')) {
      statusColor = '#ff4d4f'; // 红色
    }

    return (
      <>
        {labelText}
        <span style={{ color: statusColor }}>{auditStatusDesc}</span>
        {lastAuditInfo.orderAuditType && <AuditTypeTag auditType={lastAuditInfo.orderAuditType} />}/
        <span>{auditTime}</span>
        {onViewDetail && (
          <>
            {' '}
            <a onClick={onViewDetail} style={{ fontSize: 12 }}>
              查看详情
            </a>
          </>
        )}
      </>
    );
  };

  return (
    <Flex justify="flex-end" style={{ marginTop: 8 }}>
      <div style={{ fontSize: 12, color: '#666' }}>{renderContent()}</div>
    </Flex>
  );
};

export default InspectionResultInfo;
