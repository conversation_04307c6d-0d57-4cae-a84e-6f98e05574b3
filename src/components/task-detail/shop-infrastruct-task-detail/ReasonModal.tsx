/* eslint-disable no-nested-ternary */
import React from 'react';
import { Modal, Input, Form, message, Checkbox, Tooltip } from 'antd';
import { useRequest } from 'ahooks';
import { IModalProps } from '@/hooks/useModal';
import { queryUnableCommitReason, saveUnableCommitReason } from '@/services/index';
import * as EspAuditTypes from '@/types/esp-audit';
import { QuestionCircleOutlined } from '@ant-design/icons';

interface IReasonModalProps extends IModalProps<{}, { shopId: string }> {
  initialValues?: any;
}

const ReasonModal: React.FC<IReasonModalProps> = (props) => {
  const [form] = Form.useForm();
  const { shopId } = props;

  // 监听表单字段变化来控制"其他原因说明"的显示
  const processBarrierValue = Form.useWatch('processBarrier', form) || [];

  const { data: reasonData, loading: fetching } = useRequest(
    async () => {
      if (shopId && props.open) {
        const res = await queryUnableCommitReason({ shopId });
        if (res?.unableSubmitReason) {
          // 设置表单初始值
          const processBarrierArray = res.unableSubmitReason.processBarrier
            ? res.unableSubmitReason.processBarrier.split(',')
            : [];

          // 如果有 otherReason，需要在选项中添加 "other"
          const selectedOptions = [...processBarrierArray];
          if (res.unableSubmitReason.otherReason) {
            selectedOptions.push('other');
          }

          const formValues = {
            lackMaterial: res.unableSubmitReason.lackMaterial || '',
            processBarrier: selectedOptions,
            otherReason: res.unableSubmitReason.otherReason || '',
          };
          form.setFieldsValue(formValues);
        }
        return res;
      }
      return null;
    },
    {
      refreshDeps: [shopId, props.open],
    },
  );

  const { run: handleSubmit, loading: submitting } = useRequest(
    async (values) => {
      const params = {
        shopId,
        unableSubmitReason: values,
      };
      await saveUnableCommitReason(params);
      message.success('提交成功');
      props.onOk?.();
    },
    {
      manual: true,
    },
  );

  const onOk = async () => {
    try {
      const values = await form.validateFields();
      const { lackMaterial = '', processBarrier = [], otherReason = '' } = values;

      // 处理卡点待解决字段 - 使用英文逗号隔开
      let processBarrierText = '';
      let otherReasonText = '';

      if (Array.isArray(processBarrier) && processBarrier.length > 0) {
        // 过滤掉"其他"选项，只保留具体的卡点原因
        const specificBarriers = processBarrier.filter((item) => item !== 'other');
        processBarrierText = specificBarriers.join(',');
      }

      // 如果选择了"其他"并且填写了其他原因说明
      if (processBarrier.includes('other') && otherReason.trim()) {
        otherReasonText = otherReason.trim();
      }

      const submitData = {
        lackMaterial: lackMaterial.trim(),
        processBarrier: processBarrierText,
        otherReason: otherReasonText,
      };

      handleSubmit(submitData);
    } catch (error) {
      // 验证失败，不执行提交
    }
  };

  const handleCancel = () => {
    form.resetFields();
    props.onCancel?.();
  };

  // 构建多选框选项
  const processBarrierOptions =
    reasonData?.barrierReasons?.map((item: EspAuditTypes.BarrierReason) => ({
      label: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <span style={{ fontWeight: 'bold' }}>{item.reason}</span>
          {item.reasonDesc && (
            <Tooltip title={item.reasonDesc} placement="top">
              <QuestionCircleOutlined style={{ color: '#999', fontSize: '12px' }} />
            </Tooltip>
          )}
        </div>
      ),
      value: item.reason,
    })) || [];

  // 添加"其他"选项
  const allOptions = [
    ...processBarrierOptions,
    {
      label: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <span style={{ fontWeight: 'bold' }}>其他</span>
          <Tooltip title="其他原因，请在下方详细说明" placement="top">
            <QuestionCircleOutlined />
          </Tooltip>
        </div>
      ),
      value: 'other',
    },
  ];

  return (
    <Modal
      title="无法提报原因"
      {...props}
      confirmLoading={submitting}
      onOk={onOk}
      okText="提交"
      loading={fetching}
      onCancel={handleCancel}
      width={600}
    >
      <Form form={form} labelCol={{ style: { width: 120 } }}>
        <Form.Item label="缺失材料" name="lackMaterial">
          <Input.TextArea rows={4} maxLength={500} placeholder="请描述缺失的材料" />
        </Form.Item>
        <Form.Item label="卡点待解决" name="processBarrier" rules={[{ required: false }]}>
          <Checkbox.Group
            options={allOptions}
            style={{
              display: 'flex',
              flexDirection: 'column',
              maxHeight: 500,
              overflowY: 'auto',
            }}
          />
        </Form.Item>
        {/* 当选择"其他"时显示的额外说明输入框 */}
        {processBarrierValue.includes('other') && (
          <Form.Item
            label="其他原因说明"
            name="otherReason"
            rules={[
              {
                required: true,
                message: '请详细说明其他卡点',
              },
            ]}
          >
            <Input.TextArea rows={3} maxLength={500} placeholder="请详细说明其他卡点" />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default ReasonModal;
