import React, { useState } from 'react';
import { Input, Checkbox, Button, Form, Flex, message, Spin, Timeline } from 'antd';
import { useRequest } from 'ahooks';
import { queryShopCollectInfo, submitShopCollect } from '@/services';
import ExpandableCard from '@/components/expandable-card';
import { ModuleSPM<PERSON>ey, PageSPMKey, traceClick, traceExp } from '@/utils/trace';

interface PurchaseRequestFormProps {
  shopId: string;
}

interface CollectDataType {
  code: string;
  desc: string;
  collect: boolean;
  latestRecord?: string;
  toPublish?: {
    buttonText: string;
    jumpUrl: string;
  };
}

const PurchaseRequestForm: React.FC<PurchaseRequestFormProps> = ({ shopId }) => {
  const [form] = Form.useForm();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isAllOptionsDisabled, setIsAllOptionsDisabled] = useState(false);

  const {
    data: collectData,
    loading: collectDataLoading,
    run: refreshCollectData,
  } = useRequest(
    async () => {
      if (shopId) {
        const data = await queryShopCollectInfo(shopId);
        if (data?.collectDataTypes?.length) {
          const initialDataTypes =
            data.collectDataTypes.filter((item) => item.collect).map((item) => item.code) || [];
          form.setFieldValue('dataTypes', initialDataTypes);
          setIsAllOptionsDisabled(data?.collectDataTypes?.every((item) => item.collect));
        }
        if (data?.collectShopName) {
          form.setFieldValue('collectShopName', data?.collectShopName);
        }
        return data;
      }
    },
    {
      refreshDeps: [shopId],
      onSuccess: (data) => {
        if (data?.collect) {
          // 组装可用素材信息
          const availableMaterials =
            data?.collectDataTypes?.map((material) => ({
              code: material.code,
              desc: material.desc,
              isCollected: material.collect,
              status: material.collect ? 'collected' : 'not_collected',
            })) || [];

          traceExp(PageSPMKey.首页, ModuleSPMKey['基建任务.装修素材提报'], {
            shopId,
            availableMaterials,
          });
        }
      },
    },
  );

  const collectDataTypes = (collectData?.collectDataTypes || []).map((item: CollectDataType) => ({
    label: (
      <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <span>
          {item.desc}
          {item.latestRecord && (
            <span style={{ opacity: 0.5, fontSize: 12, marginLeft: 8 }}>{item.latestRecord}</span>
          )}
        </span>
        {item.toPublish && (
          <a
            onClick={(e) => {
              e.stopPropagation();
              window.open(item.toPublish.jumpUrl, '_blank');
            }}
          >
            {item.toPublish?.buttonText || '去发布'}
          </a>
        )}
      </span>
    ),
    value: item.code,
    disabled: item.collect,
  }));

  const { run: submitForm, loading: submitting } = useRequest(
    async (params: any) => {
      await submitShopCollect(shopId, params.collectShopName, params.dataTypes);
      message.success('提交成功');
      refreshCollectData();
    },
    {
      manual: true,
      onSuccess: (_, params) => {
        const submitData = params[0];

        // 组装提交的素材信息
        const submittedMaterials =
          submitData?.dataTypes?.map((materialCode: string) => {
            const originalMaterial = collectData?.collectDataTypes?.find(
              (item) => item.code === materialCode,
            );
            return {
              code: materialCode,
              desc: originalMaterial?.desc || materialCode,
            };
          }) || [];

        traceClick(PageSPMKey.首页, ModuleSPMKey['基建任务.装修素材提报'], {
          shopId,
          submittedMaterials,
          shopName: submitData.collectShopName,
        });
      },
    },
  );

  // 表单提交处理
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 过滤掉已禁用的选项
      const filteredDataTypes = values.dataTypes.filter((type: string) => {
        const item = collectDataTypes.find((option) => option.value === type);
        return !item?.disabled;
      });
      if (filteredDataTypes.length === 0) {
        message.warning('没有可提交的选项');
        return;
      }

      submitForm({
        ...values,
        dataTypes: filteredDataTypes,
      });
    } catch (error) {
      console.error(error);
    }
  };
  const STATE_COLORS = {
    任务完成: '#ACF04F',
    提交任务: '#4891F5',
    任务失败: '#C72E2D',
  };
  if (!collectData?.collect) {
    return null;
  }
  return (
    <Spin spinning={collectDataLoading} tip="加载中...">
      <Form form={form}>
        <ExpandableCard
          style={{ marginTop: 10 }}
          title="装修素材提报任务"
          defaultExpand={false}
          onExpandChange={setIsExpanded}
          expanded={isExpanded}
          header={
            <div
              onClick={() => {
                setIsExpanded(true);
              }}
            >
              <Form.Item name="dataTypes" preserve>
                <Checkbox.Group options={collectDataTypes} />
              </Form.Item>
            </div>
          }
        >
          <Flex gap={8} align="start">
            <div style={{ flex: 1 }}>
              <Form.Item
                name="collectShopName"
                label="目标任务门店名称"
                rules={[{ required: true, message: '请输入目标任务门店名称' }]}
                extra={
                  <p style={{ color: 'red', marginTop: 15 }}>
                    为确保任务数据准确，请完整填写正确的目标任务门店名称。示例：巴比馒头（阿里巴巴C6店）
                  </p>
                }
              >
                <Input placeholder="请输入门店名称" style={{ width: 330 }} />
              </Form.Item>

              <Form.Item
                name="dataTypes"
                label="基建素材任务需求"
                preserve
                rules={[
                  {
                    required: true,
                    message: '请至少选择一个基建素材任务需求',
                    type: 'array',
                  },
                ]}
              >
                <Checkbox.Group
                  options={collectDataTypes}
                  style={{ display: 'flex', flexDirection: 'column', gap: 8 }}
                />
              </Form.Item>

              <Form.Item style={{ marginTop: 20 }}>
                <Button
                  type="primary"
                  onClick={handleSubmit}
                  loading={submitting}
                  disabled={isAllOptionsDisabled}
                >
                  提报任务
                </Button>
              </Form.Item>
            </div>

            {/* 右侧信息区域 */}
            {collectData?.collectDataRecords?.length > 0 ? (
              <div
                style={{
                  width: 300,
                  marginLeft: 16,
                  padding: 8,
                  height: 250,
                  overflowY: 'auto',
                }}
              >
                <Timeline
                  items={collectData.collectDataRecords.map((item) => ({
                    dot: (
                      <div
                        style={{
                          width: 10,
                          height: 10,
                          borderRadius: '50%',
                          backgroundColor: STATE_COLORS[item?.status] || '#4891F5',
                        }}
                      />
                    ),
                    children: (
                      <div>
                        <span>{item.time}</span>
                        <span style={{ marginLeft: 8 }}>{item.desc}</span>
                        {item.failReason && <div style={{ fontSize: 12 }}>{item.failReason}</div>}
                      </div>
                    ),
                  }))}
                />
              </div>
            ) : null}
          </Flex>
        </ExpandableCard>
      </Form>
    </Spin>
  );
};

export default PurchaseRequestForm;
