import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import { <PERSON><PERSON>, But<PERSON>, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Link from '@/components/Link';
import {
  JUMP_TYPE,
  MERCHANT_INTENTION_OPTIONS,
  TASK_STATUS,
  MERCHANT_INTENTION_SOURCE_ENUM,
} from '@/common/const';
import { TaskProgress } from '../task-progress';
import { ImageViewerModalWrapper } from '@alife/mo-image-viewer';
import { TaskDetailDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import GuideModal, { IGuideModalProps } from '../guide-modal';
import './index.less';
import { sendEvent } from '@/common/utils';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { IfButtonShow } from '@/components/server-controller/useAction';

interface IProps {
  taskDetail: TaskDetailDTO;
}
export const MerchantConditionTaskCard: React.FC<IProps> = ({ taskDetail }) => {
  const [guideModalData, setGuideModalData] = useState<IGuideModalProps>({
    type: JUMP_TYPE.PICTURE,
    visible: false,
    jumpUrl: '',
    title: '',
    buttonText: '我知道了',
  });
  const {
    taskIndicatorCompletedNum,
    taskIndicatorTargetNum,
    taskDetailStatus,
    taskName = '',
    taskDesc = '',
    taskTip = '',
    sopButtons = [],
    taskDetailJumpList = [],
    extInfo = {},
  } = taskDetail || {};
  const { shangHuTongDetail = {}, payJumpList = [], merchantIntention = [] } = extInfo || {};
  const { intentionDegree, intentionDesc, recordTime, source } = merchantIntention?.[0] || {};

  const taskProgress =
    taskIndicatorCompletedNum && taskIndicatorTargetNum
      ? `(${taskIndicatorCompletedNum}/${taskIndicatorTargetNum})`
      : '';
  const completed = taskDetailStatus === TASK_STATUS.COMPLETED;

  useEffect(() => {
    sendEvent(completed ? 'RENEW_TASK_COMPLETED_BTN' : 'TO_DO_RENEW_TASK_BTN', 'EXP');
    // 新增标准格式曝光埋点
    traceExp(PageSPMKey.首页, ModuleSPMKey['年费续签任务.任务详情曝光'], {
      taskName,
      taskStatus: taskDetailStatus,
      completed,
    });
  }, []);

  const handleBtnClick = () => {
    const jumpInfo = taskDetailJumpList?.[0];
    const { jumpType, jumpUrl } = jumpInfo || {};
    switch (jumpType) {
      case JUMP_TYPE.PICTURE:
        setGuideModalData({
          type: JUMP_TYPE.PICTURE,
          visible: true,
          jumpUrl,
          title: taskName,
          buttonText: '我知道了',
        });
        break;
      case JUMP_TYPE.TEXT:
        setGuideModalData({
          type: JUMP_TYPE.TEXT,
          visible: true,
          jumpUrl,
          title: taskName,
          buttonText: '我知道了',
        });
        break;
      case JUMP_TYPE.LINK:
        window.open(jumpUrl);
        break;
      default:
        break;
    }
    sendEvent(completed ? 'RENEW_TASK_COMPLETED_BTN' : 'TO_DO_RENEW_TASK_BTN', 'CLK');
    // 新增标准格式点击埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['年费续签任务.任务操作'], {
      taskName,
      actionType: completed ? '查看已完成任务' : '去完成任务',
      jumpType: jumpInfo?.jumpType,
    });
  };

  const handleSopClick = (e, value) => {
    const { jumpUrlList = [], buttonText } = value;
    e.preventDefault();
    window.open(jumpUrlList?.[0]);
    traceClick('drawerBlock_TaskDetail.renewTaskSopBtn', {
      params: {
        sopName: buttonText,
      },
    });
  };
  const jumpInfo = taskDetailJumpList?.[0];

  return (
    <div className="merchant-condition-task-card">
      <div className="main-info-card">
        <div className="merchant-condition-info">
          <div className="card-name-container">
            <div className="task-card-name">{`${taskName}${taskProgress}`}</div>
            {taskTip && (
              <Tooltip title={taskTip}>
                <QuestionCircleOutlined className="name-icon" />
              </Tooltip>
            )}
          </div>
          <div className="task-card-desc">{taskDesc}</div>
          <div className="task-card-jump-info">
            <div className="task-card-status">
              <div>
                {sopButtons
                  ?.filter((i) => i.client === 'PC')
                  ?.map((item) => (
                    <>
                      <IfButtonShow button={item} ext={item.jumpTypeNew === JUMP_TYPE.LINK}>
                        <Link
                          disabled={!!item.greyButton}
                          to=""
                          style={{ display: 'block', fontSize: 12, textWrap: 'nowrap' }}
                          onClick={(e) => handleSopClick(e, item)}
                        >
                          {item.buttonText}
                        </Link>
                      </IfButtonShow>
                      <IfButtonShow button={item} ext={item.jumpTypeNew === JUMP_TYPE.PIC}>
                        <ImageViewerModalWrapper imageUrls={item?.jumpUrlList || []}>
                          <Link
                            to=""
                            disabled={!!item.greyButton}
                            style={{ display: 'block', fontSize: 12, textWrap: 'nowrap' }}
                          >
                            {item.buttonText}
                          </Link>
                        </ImageViewerModalWrapper>
                      </IfButtonShow>
                    </>
                  ))}
              </div>
              <span className="task-card-status-badge">
                <Badge status={completed ? 'success' : 'error'} />
                {completed ? '已完成' : '未完成'}
              </span>
            </div>
            <IfButtonShow button={jumpInfo}>
              <Button
                className={classNames({
                  'button-disabled': completed,
                })}
                type="primary"
                onClick={handleBtnClick}
                disabled={!!jumpInfo?.greyButton}
              >
                {completed ? '已完成' : '去完成'}
              </Button>
            </IfButtonShow>
          </div>
        </div>
        <TaskProgress shangHuTongDetail={shangHuTongDetail} payJumpList={payJumpList} />
      </div>
      <div className="other-info-card">
        <div className="info-name">最近触达时间</div>
        <p className="info-time">{recordTime || '-'}</p>
        <div className="info-name"> 续签意向</div>
        <Tooltip
          title={
            intentionDegree
              ? `${
                  MERCHANT_INTENTION_OPTIONS.filter((item) => item.value === intentionDegree)[0]
                    ?.title
                }${intentionDesc && intentionDesc.trim() ? `-${intentionDesc}` : ''}`
              : ''
          }
        >
          <div className="info-tetail">
            {intentionDegree
              ? `${
                  MERCHANT_INTENTION_OPTIONS.filter((item) => item.value === intentionDegree)[0]
                    ?.title
                }${intentionDesc && intentionDesc.trim() ? `-${intentionDesc}` : ''}`
              : '-'}
          </div>
        </Tooltip>
        <span className="source-info">
          来源于：{source ? MERCHANT_INTENTION_SOURCE_ENUM[source] : '-'}
        </span>
      </div>
      {guideModalData?.visible && (
        <GuideModal onCancel={() => setGuideModalData(undefined)} {...guideModalData} />
      )}
    </div>
  );
};
