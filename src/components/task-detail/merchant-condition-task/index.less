.merchant-condition-task-card {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  color: #000;

  .main-info-card {
    background-color: #fff;
    border-radius: 2px;
    padding: 20px 15px 20px 10px;
    margin-right: 10px;
    flex: 1;
    display: flex;
    justify-content: space-between;
  }

  .merchant-condition-info {
    width: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-right: 10px;

    .card-name-container {
      display: flex;
      align-items: center;
      .task-card-name {
        font-size: 18px;
        line-height: 30px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 170px;
      }
      .name-icon {
        margin-left: 5px;
        font-size: 16px;
        flex-shrink: 0;
      }
    }
    .task-card-desc {
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      line-height: 22px;
      min-height: 44px;
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .task-card-jump-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 18px;

      .task-card-status {
        &-link {
          margin-bottom: 18px;
          display: flex;
          a,
          span {
            color: #1890ff;
            font-size: 12px;
            cursor: pointer;
            user-select: none;
          }
          a:first-child {
            margin-right: 10px;
          }
        }
        &-badge {
          margin-bottom: 5px;
          white-space: nowrap;
        }
      }

      .button-disabled {
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        text-shadow: none;
        box-shadow: none;
      }
    }
  }

  .other-info-card {
    background-color: #fff;
    border-radius: 2px;
    padding: 20px 10px;
    width: 140px;
    .info-name {
      margin-bottom: 8px;
    }
    .info-time {
      text-align: center;
    }
    .info-tetail,
    .source-info {
      padding-left: 8px;
    }
    .info-tetail {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      cursor: pointer;
    }
    .source-info {
      color: rgba(0, 0, 0, 0.45);
      font-size: 10px;
      font-size: 600;
    }
  }
}
