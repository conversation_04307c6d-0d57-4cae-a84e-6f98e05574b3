import React from 'react';
import { MerchantConditionTaskCard } from './merchant-condition-task-card';
import { TaskDetailDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import './index.less';

interface IProps {
  taskDetailDTOList: TaskDetailDTO[];
}
export const MerchantConditionTask: React.FC<IProps> = ({ taskDetailDTOList = [] }) => {
  return (
    <div className="merchant-condition-task">
      {taskDetailDTOList.map((item, index) => (
        <MerchantConditionTaskCard taskDetail={item} key={index} />
      ))}
    </div>
  );
};
