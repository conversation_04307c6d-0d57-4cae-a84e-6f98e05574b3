import React from 'react';
import './index.less';
import { TaskDetailContent } from './task-detail-content';
import { Drawer } from 'antd';

interface IProps {
  visible: boolean;
  pid: string;
  jumpSource: string;
  from: string;
  shopId?: string;
  shopQualityScore?: string;
  onClose: () => void;
  [key: string]: any;
}

export const TaskDetailDrawer: React.FC<IProps> = ({
  visible,
  pid,
  jumpSource,
  from,
  shopId = '',
  shopQualityScore = '',
  onClose,
  ...rest
}) => {
  const closeDrawer = () => {
    onClose();
  };

  return (
    <Drawer
      title="任务详情"
      placement="right"
      size="large"
      width="950px"
      bodyStyle={{ height: '90%', padding: '0 15px' }}
      onClose={closeDrawer}
      open={visible}
      destroyOnClose
    >
      <TaskDetailContent
        pid={pid}
        jumpSource={jumpSource}
        from={from}
        {...rest}
        shopId={shopId}
        shopQualityScore={shopQualityScore}
      />
    </Drawer>
  );
};
