import React, { useEffect, useState } from 'react';
import './index.less';
import { getBaseRequestParam, sendEvent } from '@/common/utils';
import { TaskCard } from '../task-card';
import { Button, Empty, Result, Row, message } from 'antd';
import { TASK_DETAIL_TABS, TASK_STATUS, PAGE_STATUS } from '@/common/const';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import { useStore } from '@/context/global-store';

interface IProps {
  pid: string;
  visible: boolean;
  jumpSource: string;
}
export const AdTaskDetail: React.FC<IProps> = ({ pid, visible, jumpSource }) => {
  const [status, setStatus] = useState<PAGE_STATUS>();
  const [detailList, setDetailList] = useState([]);
  const [adJumpUrl, setAdJumpUrl] = useState<string>();
  const [isAllTaskCompleted, setAllTaskCompleted] = useState(false);
  const [greyButton, setGreyButton] = useState<boolean>(false);

  useEffect(() => {
    if (visible) {
      fetchList();
      sendEvent(TASK_DETAIL_TABS.AD_TASK, 'EXP');
    }
  }, [visible]);

  useEffect(() => {
    if (visible && adJumpUrl) {
      sendEvent(isAllTaskCompleted ? 'AD_TASK_COMPLETED_BTN' : 'TO_DO_AD_TASK_BTN', 'EXP');
    }
  }, [visible, adJumpUrl, isAllTaskCompleted]);

  const { viewer } = useStore();
  const fetchList = () => {
    setStatus(PAGE_STATUS.LOADING);
    const param = {
      viewOperatorId: viewer || undefined,
      taskDetailScene: TASK_DETAIL_TABS.AD_TASK,
      pid,
      jumpSource,
      ...getBaseRequestParam(),
    };
    setAllTaskCompleted(false);
    service
      .queryAgentOperationDetail(param)
      .then((res) => {
        const { success, data, msgInfo: resultMessage } = res || {};
        if (!success) {
          message.error(resultMessage);
          setStatus(PAGE_STATUS.ERROR);
          return;
        }
        // 商户没有门店接口无数据，默认返回基建任务tab
        if (!data) {
          setStatus(PAGE_STATUS.EMPTY);
          return;
        }
        const { taskDetailDTOList, agentOperationTaskJumpList } = data || {};
        if (taskDetailDTOList?.length > 0) {
          setStatus(PAGE_STATUS.SUCCESS);
          setDetailList(taskDetailDTOList);
          const allCompleted = taskDetailDTOList.every(
            (task) => task.taskDetailStatus === TASK_STATUS.COMPLETED,
          );
          if (allCompleted) {
            setAllTaskCompleted(true);
          }
          if (agentOperationTaskJumpList?.length > 0) {
            const jumpInfo = agentOperationTaskJumpList.find((item) => item.client === 'PC');
            setAdJumpUrl(jumpInfo?.jumpUrl || '');
            setGreyButton(jumpInfo?.greyButton || false); // 设置 greyButton 状态
          }
        } else {
          setStatus(PAGE_STATUS.EMPTY);
        }
      })
      .catch((e) => {
        console.error('任务详情请求失败: ', e);
        message.error(e?.res?.resultMsg);
        setStatus(PAGE_STATUS.ERROR);
      });
  };

  const openAdPage = () => {
    window.open(adJumpUrl);
    sendEvent(isAllTaskCompleted ? 'AD_TASK_COMPLETED_BTN' : 'TO_DO_AD_TASK_BTN', 'CLK');
  };

  return (
    <div className="task-tab-container">
      {status === PAGE_STATUS.SUCCESS && adJumpUrl && (
        <>
          <div className="action-container">
            <Button type="primary" disabled={greyButton} onClick={openAdPage}>
              {isAllTaskCompleted ? '已' : '去'}完成广告任务
            </Button>
          </div>
          {detailList?.length > 0 && (
            <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
              {detailList.map((detail, index) => (
                <TaskCard detailInfo={detail} tabKey={TASK_DETAIL_TABS.AD_TASK} key={index} />
              ))}
            </Row>
          )}
        </>
      )}
      {status === PAGE_STATUS.EMPTY && <Empty style={{ margin: '16px' }} />}
      {status === PAGE_STATUS.ERROR && (
        <Result
          className="task-result"
          title="网络出错了"
          subTitle="网络开小差了，请检查网络连接后重试"
          extra={
            <Button
              type="primary"
              onClick={() => {
                fetchList();
              }}
            >
              点击重试
            </Button>
          }
        />
      )}
    </div>
  );
};
