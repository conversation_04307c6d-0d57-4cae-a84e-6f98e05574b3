.infrastruct-card {
  .infrastruct-name-container {
    width: 100%;
    display: block;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .infrastruct-name {
      color: #000;
      font-size: 16px;
      line-height: 24px;
    }

    .index-icon {
      margin-left: 5px;
    }
  }

  .infrastruct-desc {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
    min-height: 44px;
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .infrastruct-status {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 18px;
    &-badge {
      margin-bottom: 5px;
      white-space: nowrap;
    }

    &-score {
      display: flex;
      flex-direction: column;
      // align-items: center;
      margin-left: 10px;
      margin-top: 2px;
      .task-score {
        height: 16px;
        font-size: 12px;
        min-width: 92px;
        white-space: nowrap;
      }
      .button {
        margin-top: 10px;
      }
      .button-disabled {
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        text-shadow: none;
        box-shadow: none;
      }
    }

    &-link {
      margin-bottom: 18px;
      display: flex;
      flex-direction: column;
      white-space: nowrap;
      a,
      span {
        color: #1890ff;
        font-size: 12px;
        cursor: pointer;
        user-select: none;
      }
    }
  }
}
