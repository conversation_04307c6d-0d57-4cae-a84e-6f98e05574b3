import React, { useRef } from 'react';
import dayjs from 'dayjs';
import AfeClipboard from '@alife/afe-clipboard';
import { message } from 'antd';
import classNames from 'classnames';
import { MERCHANT_EXPIRE_CONDITION_ENUM_OPTIONS, MERCHANT_SUB_STATUS } from '@/common/const';
import { TaskJumpDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import './index.less';
import { safeDivide, safeMultiply } from '@/common/utils';
import Link from '@/components/Link';
import { IfButtonShow } from '@/components/server-controller/useAction';
import { isAmapXy } from '@/utils/index';

interface IProps {
  shangHuTongDetail: any;
  payJumpList?: TaskJumpDTO[];
}

export const TaskProgress: React.FC<IProps> = ({ shangHuTongDetail = [], payJumpList = [] }) => {
  const {
    shangHuTongExpireTime,
    shangHuTongStatus,
    shangHuTongSubStatus,
    annualFeeExpireTime,
    annualFeeStatus,
    annualFeeSubStatus,
  } = shangHuTongDetail;
  const expireTime = annualFeeExpireTime || shangHuTongExpireTime;
  const expireStatus = annualFeeStatus || shangHuTongStatus;
  const subStatus = annualFeeSubStatus || shangHuTongSubStatus;
  const baseLineRef = useRef(null);

  // 当前时间与商户通到期时间对比
  const getDateDifference = (midDate, currentDate) => {
    const midDay = dayjs(midDate);
    const currentDay = dayjs(currentDate);
    const diffdays = Math.ceil(currentDay.diff(midDay, 'days'));
    return diffdays;
  };
  const currentDate = dayjs().format('YYYY-MM-DD');
  const daysDiff = getDateDifference(expireTime, currentDate);

  let progress = '';
  if (daysDiff === 0) {
    progress = '50';
  } else if (daysDiff < 0) {
    progress = daysDiff < -90 ? '0' : `${(100 - Math.abs(daysDiff)) / 2}`;
  } else {
    progress = daysDiff > 90 ? '100' : `${50 + daysDiff / 2}`;
  }
  // 获取左右极限距离做样式变更
  const baseLineWidth = baseLineRef?.current?.offsetWidth || 465;
  const leftDistance = baseLineWidth && safeMultiply(safeDivide(60, baseLineWidth), 100);
  const progressDistance = progress && Number(progress);
  const rightDistance = 100 - progressDistance;

  return (
    <>
      <div className="task-progress">
        <div className="progress">
          <div className="line base-line" ref={baseLineRef} />
          <div className="line progress-bar" style={{ width: `${progress}%` }}>
            <div className="current-mark">
              <div className="triangle-mark" />
            </div>
            <div
              className={classNames({
                'current-mark-content': true,
                'left-current-mark-content':
                  progressDistance <= leftDistance && rightDistance > leftDistance,
                'right-current-mark-content':
                  rightDistance <= leftDistance && progressDistance > leftDistance,
              })}
            >
              <div className="status">
                {expireStatus
                  ? `${
                      MERCHANT_EXPIRE_CONDITION_ENUM_OPTIONS.filter(
                        (item) => item.value === expireStatus,
                      )[0]?.title
                    }${subStatus ? `-${MERCHANT_SUB_STATUS[subStatus]}` : ''}`
                  : '-'}
              </div>
              {payJumpList && (
                <div className="pay-btn">
                  {payJumpList.map((item, index) => (
                    <IfButtonShow button={item}>
                      <Link to="" disabled={!!item.greyButton}>
                        <div className="copy-text" key={index}>
                          <AfeClipboard
                            text={`${item.jumpUrl || ''}`}
                            onSuccess={() => message.success('复制成功')}
                          >
                            {item.buttonText}
                            <img
                              src="https://img.alicdn.com/imgextra/i1/O1CN01pOrhic1h6TTkUDcZV_!!6000000004228-2-tps-64-64.png"
                              alt=""
                            />
                          </AfeClipboard>
                        </div>
                      </Link>
                    </IfButtonShow>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="step">
          <div className="main-date">
            <div className="time">{expireTime}</div>
            <div className="text">{isAmapXy() ? '年费到期时间' : '商户通到期时间'}</div>
            <div className="point" />
          </div>
          <div className="left-ninety">
            <div className="text">90</div>
            <div className="point" />
          </div>
          <div className="left-sixty">
            <div className="text">60</div>
            <div className="point" />
          </div>
          <div className="left-thirty">
            <div className="text">30</div>
            <div className="point" />
          </div>
          <div className="right-ninety">
            <div className="text">90</div>
            <div className="point" />
          </div>
        </div>
      </div>
    </>
  );
};
