.task-progress {
  --mark-size: 6px;
  padding: 60px 0 40px;
  width: 100%;
  position: relative;
  flex: 1;

  .progress,
  .step {
    width: 100%;
    position: absolute;
    padding: 0 5px;
  }

  .line {
    width: calc(100% - 10px);
    height: 3px;
    border-radius: 3px;
    position: absolute;
  }
  .base-line {
    width: 100%;
    background-color: rgba(0, 0, 0, 0.15);
  }

  .progress-bar {
    background-color: red;
    color: #000;
    .current-mark {
      position: absolute;
      top: 5px;
      right: -6px;
      .triangle-mark {
        width: 0;
        height: 0;
        border: var(--mark-size) solid transparent;
        border-bottom: 12px solid red;
      }
    }

    .current-mark-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      top: 24px;
      width: 120px;
      right: -60px;
      .status,
      .pay-btn {
        margin-top: 5px;
      }
      .status {
        font-weight: 500;
        font-size: 10px;
      }
      .pay-btn {
        display: flex;
        .copy-text {
          img {
            width: 18px;
            height: 18px;
          }
        }
        .copy-text:first-child {
          margin-right: 12px;
        }
      }
    }
    .left-current-mark-content {
      left: -6px;
      right: auto !important;
      align-items: flex-start;
    }
    .right-current-mark-content {
      right: -6px !important;
      align-items: flex-end;
    }
  }

  .step {
    .text {
      font-size: 18px;
    }
    .point {
      width: 5px;
      height: 5px;
      background-color: #000;
      border-radius: 50%;
      position: absolute;
      bottom: 1px;
    }

    .left-ninety {
      left: calc(5% + var(--mark-size));
    }

    .left-sixty {
      left: calc(20% + var(--mark-size));
    }

    .left-thirty {
      left: calc(35% + var(--mark-size));
    }

    .right-ninety {
      left: calc(95% + var(--mark-size));
    }
  }

  .step > div {
    position: absolute;
    bottom: -5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: translateX(-50%);
  }

  .step > div > .text {
    position: absolute;
    bottom: 14px;
    white-space: nowrap;
    height: 20px;
    line-height: 20px;
  }

  .main-date {
    left: calc(50% + var(--mark-size));
    .time {
      position: absolute;
      bottom: 38px;
      white-space: nowrap;
      font-size: 12px;
    }
    .text {
      font-size: 14px;
    }
    .point {
      width: 9px;
      height: 9px;
      border-radius: 50%;
      position: absolute;
      bottom: -1px;
      background-color: red;
    }
  }
}
