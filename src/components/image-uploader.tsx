import { ImgUpload } from '@alife/mp-oss-upload/esm/content';
import React from 'react';
import { UploadFile } from 'antd/lib/upload/interface';

export interface ICommonImgUploadProps {
  accept?: string[];
  value?: string[];
  maxCount?: number;
  maxFileSize?: number | string;
  actionRenderText?: string;
  onChange?: (img: string | string[]) => void;
  disabled?: boolean;
  valueType?: 'string' | 'array';
  isOriginalPic?: boolean;
  heightMax?: number;
  widthMax?: number;
}

interface IState {
  previewVisible: boolean;
  previewImage: string;
  fileList: Array<UploadFile<any>>;
}

export default class CommonImgUpload extends React.Component<ICommonImgUploadProps, IState> {
  render() {
    return <ImgUpload {...this.props} />;
  }
}
