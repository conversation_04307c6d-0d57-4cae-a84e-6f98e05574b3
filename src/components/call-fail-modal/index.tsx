import { message, Modal } from 'antd';
import React from 'react';
import fetch from '@alife/amap-fetch';

async function copyTextToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text);
  } catch (err) {
    console.error('Could not copy text to clipboard: ', err);
  }
}

let greyPromise: ReturnType<typeof fetch<any, any>>;

export const checkGrey = async () => {
  if (!greyPromise) {
    greyPromise = fetch({
      apiKey: 'alsc-kbt-leads-center.LeadsConfigGatewayService.queryDataMaskingConfig',
      params: {},
    });
  }
  const { data } = await greyPromise;
  return data.needDataMasking;
};

export const checkIsBizError = (data: any) => {
  return !!data.errorCode && data.errorCode > 201 && data.errorCode < 300;
};

export const checkCommonParams = (data: any, isBizError: boolean) => {
  const {
    phoneNumber, // 自己注入的
  } = data;
  const content = isBizError ? (
    '商户近期被呼叫频繁，建议稍后重试'
  ) : (
    <span>
      运营商风控拦截，线路不允许触达，请改用其他方式联系商户 <br />
      电话 {phoneNumber}
    </span>
  );
  return {
    isBizError,
    content,
  };
};

export const failModal = async (
  data: {
    phoneNumber: string;
  },
  isBizError: boolean,
  feedback?: () => void,
) => {
  const {
    phoneNumber, // 自己注入的
  } = data;
  const needDataMasking = await checkGrey();
  if (needDataMasking) {
    const { content } = checkCommonParams(data, isBizError);
    if (isBizError) {
      Modal.confirm({
        title: '提示',
        content,
        okCancel: false,
        okText: '我知道了',
      });
    } else {
      Modal.confirm({
        title: '提示',
        content,
        cancelText: '我知道了',
        okText: '复制电话',
        onOk: () => {
          copyTextToClipboard(phoneNumber).then(() => {
            message.success('复制成功');
          });
          // 复制电话
        },
      });
    }
  } else {
    feedback?.();
  }
};
