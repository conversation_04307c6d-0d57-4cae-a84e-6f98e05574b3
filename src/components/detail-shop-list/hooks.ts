import { useState } from 'react';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import { SHOP_QUERY_SOURCE_ENUM, TASK_STATUS } from '@/common/const';
import { getBaseRequestParam } from '@/common/utils';
import { useStore } from '@/context/global-store';

export const useDetailShopList = () => {
  const [pagination, setPagination] = useState<{
    pageNo?: number;
    pageSize?: number;
  }>({
    pageNo: 1,
    pageSize: 10,
  });
  const { viewer } = useStore();
  const {
    run: loadShopList,
    loading,
    data,
  } = useRequest(
    async (params) => {
      setPagination({
        pageNo: params?.page?.pageNo || 1,
        pageSize: params?.page?.pageSize || 10,
      });
      const listParam = {
        viewOperatorId: viewer || undefined,
        shopTaskStatus: TASK_STATUS.ALL,
        includeShopTaskProcess: false,
        source: SHOP_QUERY_SOURCE_ENUM.WAI_HU_SHOP_LIST,
        ...params,
        ...getBaseRequestParam(),
      };
      const res = await service.queryAgentOperationShopList(listParam);
      if (!res.success) {
        message.error(res?.msgInfo || '系统异常，请稍后再试～');
        return {};
      }
      return res?.data || {};
    },
    {
      manual: true,
    },
  );

  return {
    loading,
    data: data?.dataList || [],
    totalCount: data?.pageInfo?.totalCount || 0,
    pagination,
    loadShopList,
  };
};
