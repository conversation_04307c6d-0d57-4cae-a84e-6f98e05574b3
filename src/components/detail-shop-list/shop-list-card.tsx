import React from 'react';
import { ShopOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { AgentOperationShopRelationDTO } from '@/_docplus/target/types/amap-sales-operation-client';
import './index.less';

interface IProps {
  data: AgentOperationShopRelationDTO;
  selectedId: string;
  onListPanelClick: (value) => void;
}

export const ShopListCard: React.FC<IProps> = ({ data, selectedId, onListPanelClick }) => {
  const handleClick = () => {
    onListPanelClick({ shopId: data?.shopId, shopName: data?.shopName, kbShopId: data?.kbShopId });
  };

  return (
    <div
      className={classNames({
        'card-selected': selectedId === data?.shopId,
        'shop-list-card': true,
      })}
      onClick={handleClick}
    >
      <div className="card-info">
        <img className="card-img" src={data?.pic} />
        <div className="card-content">
          <div className="card-content-name">{data?.shopName || '-'}</div>
          <div className="card-content-id">门店 ID：{data?.shopId || '-'} </div>
        </div>
      </div>
      <div className="card-address">
        <ShopOutlined />
        <span className="address">{data?.address || '-'}</span>
      </div>
    </div>
  );
};
