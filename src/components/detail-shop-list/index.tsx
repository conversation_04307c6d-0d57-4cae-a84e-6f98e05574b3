import React, { useEffect } from 'react';
import { Spin, Pagination, Empty } from 'antd';
import { useDetailShopList } from './hooks';
import './index.less';
import { ShopListCard } from './shop-list-card';

interface IProps {
  pid: string;
  selectedId: string;
  onListPanelClick: (value) => void;
}

export const DetailShopList: React.FC<IProps> = ({ pid, selectedId, onListPanelClick }) => {
  const { loading, data = [], pagination, totalCount, loadShopList } = useDetailShopList();
  useEffect(() => {
    loadShopList({
      pid,
      page: {
        pageNo: 1,
        pageSize: 10,
      },
    });
  }, [loadShopList, pid]);

  const onChangePagination = (pageNo: number, pageSize: number) => {
    loadShopList({
      pid,
      page: {
        pageNo,
        pageSize,
      },
    });
  };
  return (
    <div className="detail-shop-list">
      <div className="detail-shop-list-title">{`门店列表(${totalCount})`}</div>
      <Spin spinning={loading}>
        <div className="detail-shop-list-content">
          {data?.map((item, index) => (
            <ShopListCard
              data={item}
              key={index}
              selectedId={selectedId}
              onListPanelClick={onListPanelClick}
            />
          ))}
          {!loading && !data?.length && (
            <Empty description="暂无数据" className="empty" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>
        <div className="pagination">
          <Pagination
            size="small"
            total={totalCount}
            current={pagination?.pageNo || 1}
            pageSize={pagination?.pageSize || 10}
            onChange={onChangePagination}
            showTotal={(val) => `总共 ${val} 条`}
          />
        </div>
      </Spin>
    </div>
  );
};
