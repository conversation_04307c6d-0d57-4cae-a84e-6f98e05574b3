.detail-shop-list {
  color: rgba(0,0,0,0.85);
  &-title {
    margin: 10px 0;
  }
  &-content {
    overflow: auto;
  }

  .pagination {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    margin-top: 5px;
  }

  .shop-list-card {
    border: 1px solid #eee;
    margin-bottom: 4px;
    padding: 10px;
    font-size: 12px;
    background-color: #fff;

    .card-info {
      display: flex; 
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 6px;

      .card-img {
        width: 35px;
        height: 35px;
        margin-right: 6px;
      }

      .card-content {
        flex: 1;
        &-name {
          color: #000;
          font-size: 13px;
        }
        &-id {
          word-break: break-all;
        }
      }
    }

    .card-address {
      display: flex;
      align-items: center;
      .address {
        margin-left: 4px;
      }
    }
  }

  .card-selected {
    background-color: rgb(254,247,242);
  }
}