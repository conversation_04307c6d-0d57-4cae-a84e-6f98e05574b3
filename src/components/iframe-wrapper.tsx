import { useSize } from 'ahooks';
import { cloneElement, PropsWithChildren, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

type IframeWrapperProps = PropsWithChildren<{
  title?: string;
  style?: React.CSSProperties;
  className?: string;
  id?: string;
}>;

export default function IframeWrapper({
  children,
  title = 'iframe-content',
  style,
  className,
  id,
}: IframeWrapperProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeBody, setIframeBody] = useState<HTMLElement | null>(null);
  const childrenRef = useRef<HTMLElement>(null);

  const ChildrenRender = cloneElement(children, { ref: childrenRef });
  const size = useSize(childrenRef);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const iframeDocument = iframe.contentDocument;
    if (!iframeDocument) return;

    // 复制父文档的所有样式表到 iframe
    const styleSheets = Array.from(document.styleSheets);
    const styles = styleSheets.reduce((acc, sheet) => {
      try {
        const rules = Array.from(sheet.cssRules);
        const cssText = rules.map((rule) => rule.cssText).join('\n');
        return acc + cssText;
      } catch (e) {
        // 处理跨域样式表的错误
        return acc;
      }
    }, '');

    // 设置基本的HTML结构，包含复制的样式
    iframeDocument.open();
    iframeDocument.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <style>
            ${styles}
          </style>
        </head>
        <body>
        </body>
      </html>
    `);
    iframeDocument.close();

    // 获取iframe的body元素
    const { body } = iframeDocument;
    setIframeBody(body);

    // 同步字体
    const links = document.getElementsByTagName('link');
    Array.from(links).forEach((link) => {
      if (link.rel === 'stylesheet') {
        const newLink = iframeDocument.createElement('link');
        newLink.rel = 'stylesheet';
        newLink.href = link.href;
        iframeDocument.head.appendChild(newLink);
      }
    });

    // 复制父文档的 style 标签
    const styleElements = document.getElementsByTagName('style');
    Array.from(styleElements).forEach((styleEl) => {
      const newStyle = iframeDocument.createElement('style');
      newStyle.textContent = styleEl.textContent;
      iframeDocument.head.appendChild(newStyle);
    });
    iframeDocument.body.style.backgroundColor = 'white';
  }, []);

  useEffect(() => {
    iframeRef.current.style.height = `${size?.height || 100}px`;
  }, [size]);

  const defaultStyle: React.CSSProperties = {
    border: 'none',
    width: '100%',
    height: '100%',
    ...style,
  };

  return (
    <iframe ref={iframeRef} id={id} title={title} style={defaultStyle} className={className}>
      {iframeBody && createPortal(ChildrenRender, iframeBody)}
    </iframe>
  );
}
