import { gdRequest } from '@/services/request';

export const getShopList = (pid: string) => {
  return gdRequest(
    'mtop.amap.mp.shop.listShopsByOrg',
    {
      pageSize: 1000,
      pageNum: 1,
      pid,
      bizVersion: 3,
      orderItems: [
        {
          attrCode: 'SHOP_CITY_NAME',
          type: 'DESC',
        },
      ],
      filterItems: [
        {
          attrCode: 'SHOP_SCOPE',
          ids: ['KOUBEI', 'GAODE'],
        },
        {
          attrCode: 'SHOP_STATE',
          ids: [4, 5],
        },
      ],
    },
    {
      headers: {
        requestSource: 7,
      },
    },
  );
};
