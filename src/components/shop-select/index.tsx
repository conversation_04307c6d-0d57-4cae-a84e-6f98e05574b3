import { useRequest } from 'ahooks';
import { TreeSelect, TreeSelectProps } from 'antd';
import { getShopList } from './service';

interface IProps {
  pid: string;
  onLoadFinish?: (data: any) => void;
}
export default function ShopSelect(props: TreeSelectProps & IProps) {
  const { pid, onLoadFinish } = props;
  const { data } = useRequest(
    async () => {
      const res = await getShopList(pid);
      const list: any[] = [];
      res.list.forEach((item) => {
        const cityname = item.cityName;
        const city = list.find((ele) => ele.label === cityname);
        if (city) {
          city.children.push({
            label: item.shopName,
            value: item.shopId,
          });
        } else {
          list.push({
            label: cityname,
            value: cityname,
            children: [
              {
                label: item.shopName,
                value: item.shopId,
              },
            ],
          });
        }
      });
      onLoadFinish?.(
        res.list.map((item) => ({
          label: item.shopName,
          value: item.shopId,
        })),
      );
      return list;
    },
    {
      refreshDeps: [pid],
    },
  );
  return (
    <TreeSelect
      treeCheckable
      maxTagCount={2}
      treeData={data || []}
      allowClear
      placeholder="请选择门店"
      labelInValue
      filterTreeNode={(input, option) => {
        if (option.label) {
          // @ts-ignore force text
          return option.label.includes(input);
        }
        return false;
      }}
      {...props}
    />
  );
}
