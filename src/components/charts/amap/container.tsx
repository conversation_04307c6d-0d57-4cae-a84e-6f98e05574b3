import { PropsWithChildren } from 'react';

export default function GraphContainer(
  props: PropsWithChildren<{
    domId?: string;
    style?: React.CSSProperties;
  }>,
) {
  const { style = {} } = props;
  return (
    <div style={{ width: 390, height: 280, margin: '5px auto', borderRadius: 8, ...style }}>
      <div id={props.domId} style={{ width: '100%', height: '100%' }}>
        {props.children}
      </div>
    </div>
  );
}
