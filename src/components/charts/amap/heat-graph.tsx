import { Map, LocaGridLayer, StorePoint } from '@ali/amap-react-studio';
import { Circle } from '@amap/amap-react';
import { useEffect, useState } from 'react';
import GraphContainer from './container';
import { Empty } from 'antd';

interface IProps {
  center?: [number, number];
  data?: any;
  radius?: number;
  domId?: string;
  flag?: boolean;
}
const heatOptions = {
  unit: 'meter',
  altitude: 0,
  height: 0,
  color: ['#AFEEEE', '#5AE1E1', '#00CED1', '#6495ED', '#1874CD'],
};

const radiusMap: Record<number, [number, number, number, number]> = {
  1: [1050, 100, 1, 13.6],
  2: [2050, 200, 2, 13],
  10: [10050, 1000, 10, 10.7],
  20: [20050, 2000, 20, 9.7],
};

const onMapComplete = (map: any) => {
  console.log(map);
};
export default function HeatGraph(props: IProps) {
  const { center, data: originData = '', radius = 1, flag } = props;
  const [data, setData] = useState(transformData(originData));
  useEffect(() => {
    if (originData) {
      setData(transformData(originData));
    }
  }, [originData]);
  function transformData(params: string) {
    if (params) {
      return params.split(';').map((c) => {
        const sub = c.split(':');
        const xy = sub[0].split(',');
        return {
          x: +xy[0],
          y: +xy[1],
          value: +sub[1],
        };
      });
    }
    return [];
  }
  if (!originData || center?.length !== 2) {
    return <Empty description="热力图暂无数据" />;
  }
  return (
    <GraphContainer domId={props.domId} style={{ marginBottom: 10 }}>
      <Map
        extraOptions={{
          WebGLParams: {
            preserveDrawingBuffer: true,
          },
        }}
        center={center}
        zoom={radiusMap[radius][3]}
        onComplete={onMapComplete}
      >
        <LocaGridLayer
          data={data}
          radius={radiusMap[radius][1]}
          visible
          flag={flag}
          options={{ ...heatOptions, radius: radiusMap[radius][1], gap: radiusMap[radius][2] }}
        />
        <Circle
          center={center}
          radius={radiusMap[radius][0]}
          fillOpacity={0}
          strokeColor="#F4A460"
          strokeDasharray={[3, 3, 3]}
          strokeWeight={3}
          strokeStyle="dashed"
          strokeOpacity={1}
        />
        <StorePoint mapPoint={center} />
      </Map>
    </GraphContainer>
  );
}
