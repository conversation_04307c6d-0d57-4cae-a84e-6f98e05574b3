import { Map, PulseLinkLayer } from '@ali/amap-react-studio';
import { useEffect, useState } from 'react';
import GraphContainer from './container';
import { Empty } from 'antd';

interface IProps {
  center: [number, number];
  data: any;
  domId?: string;
}
export default function FlyLineMap(props: IProps) {
  const { center, data: originData = '' } = props;
  const [data, setData] = useState(handleDataTransformer(originData));
  useEffect(() => {
    if (originData) {
      setData(handleDataTransformer(originData));
    }
  }, [originData]);
  function handleDataTransformer(dataParams: string) {
    if (dataParams) {
      return dataParams.split(';').map((c) => {
        const sub = c.split(':');
        const xy = sub[0].split(',')?.map((c) => +c);
        return {
          x: +xy[0],
          y: +xy[1],
          value: +sub[1],
        };
      });
    } else {
      return [];
    }
  }
  if (!originData || center?.length !== 2 || data.length === 0) {
    return <Empty description="飞线图暂无数据" />;
  }
  return (
    <GraphContainer domId={props.domId} style={{ marginBottom: 10 }}>
      <Map
        extraOptions={{
          WebGLParams: {
            preserveDrawingBuffer: true,
          },
        }}
        mapStyle={'amap://styles/grey'}
        center={center}
        zoom={13}
        viewMode="3D"
        pitch={48}
        onComplete={() => {}}
      >
        <PulseLinkLayer data={data} center={center as [number, number]} visible />
      </Map>
    </GraphContainer>
  );
}
