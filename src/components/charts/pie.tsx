import { Empty } from 'antd';
import { PieChart } from 'bizcharts';
import styled from 'styled-components';

const Container = styled.div`
  canvas {
    background: #fafbfc;
    border-radius: 8px;
  }
`;

const colors = [
  '#165DFF',
  '#14C9C9',
  '#F7BA1E',
  '#722ED1',
  '#3491FA',
  '#D91AD9',
  '#FF7D00',
  '#9FDB1D',
];
interface IProps {
  data: Array<{ type: string; value: number }>;
  title: string;
  width?: number;
  height?: number;
}

export function CirclePie(props: IProps) {
  if (!props.data || props.data.every((item) => !item.value)) {
    return (
      <Empty
        style={{
          width: 'auto',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        description={`暂无${props.title}数据`}
      />
    );
  }
  return (
    <Container>
      <PieChart
        radius={0.56}
        color={colors}
        padding={0}
        angleField="value"
        colorField="type"
        pieStyle={{ stroke: 'white', lineWidth: 1 }}
        legend={{
          position: 'bottom',
          offsetY: -20,
          flipPage: false,
        }}
        label={{
          formatter(angleField) {
            const { percent } = angleField;
            return `${(percent * 100).toFixed(2)}%`;
          },
          type: 'spider',
          style: {
            fontSize: 10,
          },
        }}
        // autoFit
        statistic={{
          title: {
            content: props.title,
          },
          content: false,
        }}
        innerRadius={0.7}
        data={props.data || []}
        width={props.width || 190}
        height={props.height || 220}
      />
    </Container>
  );
}
