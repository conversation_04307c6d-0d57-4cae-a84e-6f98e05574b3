import React from 'react';
import './index.less';
import { Alert } from 'antd';

interface IProps {}
export const HeaderTips: React.FC<IProps> = () => {
  return (
    <Alert
      style={{ marginTop: 16, fontWeight: 'bold', padding: '16px 24px', marginBottom: 12 }}
      description={
        '时效提示：广告任务：除账户预算撞线和计划预算撞线T+1小时更新外，其余任务操作完成后T+1天更新数据。基建任务：操作完成后T+1天更新数据'
      }
      type="info"
      showIcon
    />
  );
};
