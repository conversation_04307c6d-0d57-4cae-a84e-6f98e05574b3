import { Button, FormInstance, Table, TableProps, Typography, Pagination } from 'antd';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { EditableColumItem } from './dynamic-form-item';
import './ad-plan-table.less';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

const formatInterval = (interval: string[]) => {
  const needEllipsis = interval?.length > 3;
  const text = (interval || []).slice(0, 3).join(';') || '';
  return needEllipsis ? `${text}等` : text;
};

const getAdPlanConfig = (
  productType: adPlanFEModel.IAdPlanShopProductConfig['productType'],
  adShopPlanConfig: adPlan.IShopAdPlanConfig,
): adPlan.IAdPlanConfig => {
  const productName = productType === 'ARRIVE_STORE' ? '用户到店' : '客资线索';
  return adShopPlanConfig.adPlanConfigs?.find((config) => config.productName === productName);
};

const hasDataRealityData = (productionConfig: adPlanFEModel.IAdPlanShopProductConfig) => {
  return productionConfig.reality?.dailyBudget;
};

const buildAdsConfigItem = (
  productType: adPlanFEModel.IAdPlanShopProductConfig['productType'],
  isSuggestion: boolean,
  adShopPlanConfig?: adPlan.IShopAdPlanConfig,
): adPlanFEModel.IAdPlanShopProductConfig['suggestion'] => {
  const adPlanConfig = getAdPlanConfig(productType, adShopPlanConfig);
  return {
    isSuggestion,
    productType,
    dailyBudget:
      (isSuggestion ? adPlanConfig?.dailyBudgetSuggestion : adPlanConfig?.dailyBudgetReality) || '', // 日预算
    offer: isSuggestion ? adPlanConfig?.offerSuggestion : adPlanConfig?.offerReality || '', // 出价
    interval: isSuggestion
      ? adPlanConfig?.intervalSuggestion
      : formatInterval(adPlanConfig?.intervalReality) || '', // 投放时间
    cycle: isSuggestion ? adPlanConfig?.cycleSuggestion : adPlanConfig?.cycleReality || '', // 投放周期
    effectPrediction: (isSuggestion ? adPlanConfig?.effectPrediction : '-') || '',
  };
};

const maybeDynamicFormItemRender =
  ({ previewMode }) =>
  (data, record) => {
    if (!data || typeof data === 'string') {
      return <span style={{ color: '#999' }}>{data}</span>;
    } else {
      return (
        <EditableColumItem
          style={{ marginLeft: 8 }}
          previewMode={previewMode}
          name={(record?.formName || []).concat(data.name)}
          config={data}
        />
      );
    }
  };

const buildDataSource = (
  shopAdConfigs: adPlanFEModel.IAdPlanShopConfig[],
  shopNameMap: Record<string, string>,
) => {
  return (shopAdConfigs || []).reduce((pre, shopAdConfig) => {
    const { shopProductionConfigs } = shopAdConfig;
    const next = [...pre];
    const shopRows = shopProductionConfigs.reduce((n: number, cur) => {
      return n + (hasDataRealityData(cur) ? 2 : 1);
    }, 0);
    const canCreateProductType =
      shopProductionConfigs?.[0]?.productType === 'ARRIVE_STORE'
        ? 'CUSTOMER_RESOURCE'
        : 'ARRIVE_STORE';
    shopProductionConfigs.forEach((productionConfig, index) => {
      const { productName, productType } = productionConfig;
      const items: Array<adPlanFEModel.IAdPlanConfigFormExtra | adPlanFEModel.IAdPlanConfigItem> = [
        {
          shopId: shopAdConfig.shopId,
          shopName: shopNameMap[shopAdConfig.shopId],
          productName,
          productType,
          formName: [shopAdConfig.shopId, 'shopProductionConfigs', productType],
          productNameRowSpan: hasDataRealityData(productionConfig) ? 2 : 1,
          canCreate: shopProductionConfigs.length < 2 ? canCreateProductType : undefined,
          shopNameRowSpan: index === 0 ? shopRows : 0,
          ...productionConfig.suggestion,
          dailyBudget: {
            type: 'text',
            defaultValue: productionConfig.suggestion.dailyBudget as string,
            name: 'dailyBudget',
            config: {
              maxLength: 20,
            },
          },
          offer: {
            type: 'text',
            defaultValue: productionConfig.suggestion.offer as string,
            name: 'offer',
            config: {
              maxLength: 20,
            },
          },
          interval: {
            type: 'text',
            defaultValue: (productionConfig.suggestion.interval || '全时段') as string,
            name: 'interval',
          },
          cycle: {
            type: 'text',
            defaultValue: (productionConfig.suggestion.cycle || '持续投放') as string,
            name: 'cycle',
            config: {
              maxLength: 50,
            },
          },
          effectPrediction: {
            type: 'textarea',
            defaultValue: productionConfig.suggestion.effectPrediction as string,
            name: 'effectPrediction',
            config: {
              maxLength: 100,
            },
          },
        },
      ];
      if (hasDataRealityData(productionConfig)) {
        items.push({
          shopId: shopAdConfig.shopId,
          shopName: shopNameMap[shopAdConfig.shopId],
          productName,
          productNameRowSpan: 0,
          shopNameRowSpan: 0,
          ...productionConfig.reality,
        });
      }
      next.push(...items);
    });
    return next;
  }, [] as Array<adPlanFEModel.IAdPlanConfigFormExtra | adPlanFEModel.IAdPlanConfigItem>);
};

export const AdPlanTable = forwardRef(
  (
    {
      shops,
      // merchantId,
      previewMode,
      adConfig,
      onRemoveShop,
      onTableDataChange,
      loading,
      form,
      isCapturing,
      merchantId,
    }: {
      form: FormInstance;
      loading?: boolean;
      onRemoveShop: (shopId: string) => void;
      onTableDataChange?: (data: any[]) => void;
      adConfig: adPlan.IMerchantAdPlanConfig;
      previewMode?: boolean;
      shops?: Array<{
        value: string;
        label: string;
      }>;
      isCapturing?: boolean;
      merchantId: string;
    },
    ref,
  ) => {
    const shopNameMap = useMemo(() => {
      return (shops || []).reduce((pre, shop) => {
        return {
          ...pre,
          [shop.value]: shop.label,
        };
      }, {});
    }, [shops]);
    // const [dataSource, setDataSource] = useState<adPlanFEModel.IAdPlanConfigItem[]>([]);
    const [shopAdConfigs, setShopAdConfigs] = useState<adPlanFEModel.IAdPlanShopConfig[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const pageSize = 20;

    useImperativeHandle(ref, () => ({
      updateDataFromForm: (formValues: any) => {
        const formPlan = formValues.adPlan;
        if (!formPlan) return;
        const nextShopAdConfigs = shopAdConfigs.map((shopConfig) => {
          const shopFormValues = formPlan[shopConfig.shopId];
          if (!shopFormValues) return shopConfig;

          const nextShopProductionConfigs = shopConfig.shopProductionConfigs.map((prodConfig) => {
            const prodFormValues =
              shopFormValues.shopProductionConfigs?.[prodConfig.productType] || {};

            return {
              ...prodConfig,
              suggestion: {
                ...prodConfig.suggestion,
                ...prodFormValues,
              },
            };
          });

          return {
            ...shopConfig,
            shopProductionConfigs: nextShopProductionConfigs,
          };
        });
        setShopAdConfigs(nextShopAdConfigs);
      },
    }));

    const pagedShopAdConfigs = useMemo(() => {
      if (shopAdConfigs.length <= pageSize) return shopAdConfigs;
      const start = (currentPage - 1) * pageSize;
      return shopAdConfigs.slice(start, start + pageSize);
    }, [shopAdConfigs, currentPage]);

    const pagedShopIds = useMemo(
      () => pagedShopAdConfigs.map((config) => config.shopId),
      [pagedShopAdConfigs],
    );

    const buildShopConfigs = useCallback(() => {
      let origin = [...shopAdConfigs];
      const shopsData = adConfig?.shopAdPlanConfigs || [];
      let shouldExistShops = [...(shopsData || [])];
      const shouldExistShopIds = shouldExistShops.map((shop) => shop.shopId);
      origin = origin.filter((dataItem) => {
        // 存在
        if (shouldExistShopIds.indexOf(dataItem.shopId) > -1) {
          // 在shouldExistShops中过滤一下
          shouldExistShops = shouldExistShops.filter((shop) => shop.shopId !== dataItem.shopId);
          return true;
        } else {
          // 不存在
          return false;
        }
      });
      shouldExistShops.forEach((shop) => {
        const shopProductionConfigs = [];
        const arriveStoreProduction = getAdPlanConfig('ARRIVE_STORE', shop);
        const customerResourceProduction = getAdPlanConfig('CUSTOMER_RESOURCE', shop);
        const newShop = !arriveStoreProduction && !customerResourceProduction;
        if (arriveStoreProduction || newShop) {
          shopProductionConfigs.push({
            productName: '用户到店',
            productType: 'ARRIVE_STORE',
            suggestion: buildAdsConfigItem('ARRIVE_STORE', true, shop),
            reality: buildAdsConfigItem('ARRIVE_STORE', false, shop),
          });
        }
        if (customerResourceProduction || newShop) {
          shopProductionConfigs.push({
            productName: '客资线索',
            productType: 'CUSTOMER_RESOURCE',
            suggestion: buildAdsConfigItem('CUSTOMER_RESOURCE', true, shop),
            reality: buildAdsConfigItem('CUSTOMER_RESOURCE', false, shop),
          });
        }
        origin = origin.concat([
          {
            shopId: shop.shopId,
            shopName: '',
            shopProductionConfigs,
          },
        ]);
      });
      setShopAdConfigs(origin);
    }, [adConfig]);
    useEffect(() => {
      buildShopConfigs();
    }, [buildShopConfigs]);

    const fullDataSource = useMemo(
      () => buildDataSource(shopAdConfigs, shopNameMap),
      [shopAdConfigs, shopNameMap],
    );

    useEffect(() => {
      onTableDataChange?.(fullDataSource);
    }, [fullDataSource, onTableDataChange]);

    const onCreate = useCallback(
      (
        productType: adPlanFEModel.IAdPlanShopProductConfig['productType'],
        record: adPlanFEModel.IAdPlanConfigItem,
      ) => {
        traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.新增'], {
          pid: merchantId,
          productType,
        });
        const next = shopAdConfigs.map((shopAdConfig) => {
          if (shopAdConfig.shopId === record.shopId) {
            const shop = adConfig.shopAdPlanConfigs.find((i) => i.shopId === shopAdConfig.shopId);
            return {
              ...shopAdConfig,
              shopProductionConfigs: [
                ...shopAdConfig.shopProductionConfigs,
                {
                  productName: productType === 'ARRIVE_STORE' ? '用户到店' : '客资线索',
                  productType,
                  suggestion: buildAdsConfigItem(productType, true, shop),
                  reality: buildAdsConfigItem(productType, false, shop),
                },
              ],
            };
          }
          return shopAdConfig;
        });
        setShopAdConfigs(next);
      },
      [shopAdConfigs, adConfig],
    );
    const onDelete = useCallback(
      (shopId: string, productType: adPlanFEModel.IAdPlanShopProductConfig['productType']) => {
        // 添加删除投放方案埋点
        traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.删除'], {
          pid: merchantId,
          shopId,
          productType,
        });

        let clearAll = false;
        let shopIndex: number;
        const next = shopAdConfigs.map((shopAdConfig, index) => {
          if (shopAdConfig.shopId === shopId) {
            shopIndex = index;
            const shopProductionConfigs = shopAdConfig.shopProductionConfigs.filter(
              (i) => i?.productType !== productType,
            );
            if (shopProductionConfigs.length === 0) {
              clearAll = true;
            }
            return {
              ...shopAdConfig,
              shopProductionConfigs,
            };
          }
          return shopAdConfig;
        });
        form.setFieldValue(['adPlan', shopIndex, 'shopProductionConfigs', productType], null);
        if (clearAll) {
          onRemoveShop?.(shopId);
        } else {
          setShopAdConfigs(next);
        }
      },
      [shopAdConfigs, onRemoveShop],
    );
    const columns: TableProps['columns'] = useMemo(() => {
      const cols = [
        {
          title: '门店',
          dataIndex: 'shopName',
          onCell: (record) => {
            return {
              rowSpan: record.shopNameRowSpan,
              style: {
                paddingLeft: 24,
                paddingRight: 24,
              },
            };
          },
          onHeaderCell: () => {
            return {
              style: {
                width: 200,
                paddingLeft: 24,
                paddingRight: 24,
              },
            };
          },
        },
        {
          title: '投放目标',
          dataIndex: 'productName',
          onHeaderCell: () => {
            return {
              style: {
                width: 80,
              },
            };
          },
          onCell: (record) => {
            return {
              rowSpan: record.productNameRowSpan,
            };
          },
        },
        {
          title: '',
          onHeaderCell: () => {
            return {
              style: {
                width: 58,
              },
            };
          },
          dataIndex: 'isSuggestion',
          render: (data) => {
            return data ? (
              '建议'
            ) : (
              <span
                style={{
                  color: '#999',
                }}
              >
                当前
              </span>
            );
          },
        },
        {
          title: '计划日预算(元)',
          dataIndex: 'dailyBudget',
          render: maybeDynamicFormItemRender({ previewMode }),
          onHeaderCell: () => {
            return {
              style: {
                width: 120,
              },
            };
          },
        },
        {
          title: '出价(元)',
          dataIndex: 'offer',
          render: maybeDynamicFormItemRender({ previewMode }),
          onHeaderCell: () => {
            return {
              style: {
                width: 120,
              },
            };
          },
        },
        {
          title: '投放时间',
          dataIndex: 'interval',
          render: maybeDynamicFormItemRender({ previewMode }),
          onHeaderCell: () => {
            return {
              style: {
                width: 120,
              },
            };
          },
        },
        {
          title: '投放周期',
          dataIndex: 'cycle',
          render: maybeDynamicFormItemRender({ previewMode }),
          onHeaderCell: () => {
            return {
              style: {
                width: 120,
              },
            };
          },
        },
        {
          title: '预估效果',
          dataIndex: 'effectPrediction', // 投放预期结果',
          render: maybeDynamicFormItemRender({ previewMode }),
          onCell: () => {
            return {
              style: {
                paddingLeft: 24,
                paddingRight: 24,
              },
            };
          },
          onHeaderCell: () => {
            return {
              style: {
                paddingLeft: 24,
                paddingRight: 24,
              },
            };
          },
        },
        {
          title: '操作',
          colSpan: 2,
          width: 40,
          render: (_, record) => {
            return (
              <Button
                onClick={() => onDelete(record.shopId, record.productType)}
                style={{ padding: 0 }}
                size="small"
                type="link"
              >
                删除
              </Button>
            );
          },
          onCell: (record) => {
            return {
              rowSpan: record.productNameRowSpan,
            };
          },
        },
        {
          title: '操作',
          colSpan: 0,
          width: 40,
          render: (_, record) => {
            return record?.canCreate ? (
              <Button
                onClick={() => onCreate(record?.canCreate, record as any)}
                style={{ padding: 0 }}
                size="small"
                type="link"
              >
                新增
              </Button>
            ) : (
              '-'
            );
          },
          onCell: (record) => {
            return {
              rowSpan: record.shopNameRowSpan,
            };
          },
        },
      ] as TableProps['columns'];
      return previewMode ? cols.filter((i) => i.title !== '操作') : cols;
    }, [previewMode, onCreate, onDelete]);

    return (
      <>
        <Table
          rowKey={(record) => `${record.shopId}-${record.productType}-${record.isSuggestion}`}
          className="ad-plan-table"
          rowClassName={(record) => {
            if (
              isCapturing ||
              shopAdConfigs.length <= pageSize ||
              pagedShopIds.includes(record.shopId)
            ) {
              return '';
            }
            return 'ad-plan-table-hidden-row';
          }}
          title={() => (
            <Typography.Title style={{ margin: 0 }} level={4}>
              高德广告投放方案
            </Typography.Title>
          )}
          loading={loading}
          tableLayout="fixed"
          size="small"
          pagination={false}
          dataSource={fullDataSource || []}
          columns={columns}
        />
        {!isCapturing && shopAdConfigs.length > pageSize && (
          <div
            style={{
              marginTop: 16,
              display: 'flex',
              justifyContent: 'flex-end',
              width: '100%',
            }}
          >
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={shopAdConfigs.length}
              onChange={setCurrentPage}
              showSizeChanger={false}
            />
          </div>
        )}
      </>
    );
  },
);
