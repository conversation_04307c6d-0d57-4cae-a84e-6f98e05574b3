import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Card, Form, Switch, Space, Button, message, Spin } from 'antd';
import { AdPlanTable } from './ad-plan-table';
import { useRequest } from 'ahooks';
import BdTextTpl from '../bd-text-tpl';
import { domToImg } from '@/utils/domToImg';
import { gdRequest } from '@/services/request';
import ShopSelect from '../shop-select';
import { ModuleSPMKey, PageSPMKey, traceClick } from '@/utils/trace';

const { Item } = Form;

// const mockData: adPlan.IMerchantAdPlanConfig = {
//   merchantName: '海底捞商户',
//   adCurrentBalance: '100',
//   adCurrentMonthCost: '100',
//   adYestodayCost: '100',
//   shopAdPlanConfigs: [
//     {
//       shopId: 'idddididid',
//       adPlanConfigs: [
//         {
//           productName: '用户到店',
//           dailyBudgetSuggestion: '100',
//           offerSuggestion: '100',
//           intervalSuggestion: '100',
//           cycleSuggestion: '100',
//           effectPrediction: '100',
//           dailyBudgetReality: '100',
//           offerReality: '100',
//           intervalReality: '100',
//           cycleReality: '100',
//         },
//       ],
//     },
//   ],
// };

export interface IAdPlanRef {
  send: () => Promise<boolean>;
  saveImg: () => Promise<boolean>;
  getTableData: () => Promise<any[]>;
  checkValid: () => boolean;
  previewMode: boolean;
  updateDataFromForm?: (formValues: any) => void;
}

const domId = 'dom2pic';

export const AdPlan = React.forwardRef(
  (
    {
      merchantId,
      sendMerchantNewsAvailable,
      onShowHideSendBtn,
      onShopsCountChange,
      refForm,
    }: {
      merchantId?: string;
      sendMerchantNewsAvailable?: boolean;
      onShowHideSendBtn: (show: boolean) => void;
      onShopsCountChange?: (count: number) => void;
      refForm?: (form: any) => void;
    },
    ref: any,
  ) => {
    const [previewMode, setPreviewMode] = useState(false);
    const [shops, setShops] = useState([]);
    const [tableData, setTableData] = useState<any[]>([]);
    const [isCapturing, setIsCapturing] = useState(false);
    const [form] = Form.useForm();

    useEffect(() => {
      onShowHideSendBtn(!!shops && shops.length > 0);
      onShopsCountChange?.(shops.length);
      setPreviewMode(false);
    }, [shops, onShowHideSendBtn, onShopsCountChange]);

    useEffect(() => {
      if (refForm) {
        refForm(form);
      }
    }, [form, refForm]);

    const bdWordsRef = useRef<any>();
    const tableRef = useRef<any>();
    const checkValid = useCallback(() => {
      if (!previewMode) {
        message.error('确认投放方案，关闭修改后再发送');
        return false;
      }
      if (!shops || shops.length <= 0) {
        message.error('请至少添加一个门店');
        return false;
      }
      return true;
    }, [previewMode, shops]);

    useImperativeHandle(
      ref,
      (): IAdPlanRef => {
        return {
          saveImg: async () => {
            if (!checkValid()) {
              return false;
            }
            setIsCapturing(true);
            try {
              await new Promise((resolve) => setTimeout(resolve, 100));
              await domToImg({
                domId,
                isDownload: true,
                fileName: `ad-plan-${merchantId}-${new Date().getTime()}`,
              });
            } finally {
              setIsCapturing(false);
            }
            return true;
          },
          send: async () => {
            if (!checkValid()) {
              return false;
            }
            const { materialId } = await bdWordsRef.current.saveTemplate();
            const url = await domToImg({
              domId,
              isUpload: true,
            });
            await gdRequest(
              'amap-sales-operation.OperationAutoPushFacade.merchantAdPlanConfigReachOut',
              {
                merchantId,
                materialId,
                merchantAdConfigImgUrl: url,
              },
            );
            return true;
          },
          getTableData: async () => {
            return tableData;
          },
          checkValid,
          previewMode,
          updateDataFromForm: (formValues: any) => {
            tableRef.current?.updateDataFromForm(formValues);
          },
        };
      },
      [previewMode, merchantId, checkValid, tableData],
    );

    // 新增：保存当前查询的门店
    const [queryShops, setQueryShops] = useState<any[]>([]);

    // useRequest改为手动模式
    const {
      data: adConfig,
      loading,
      run: queryMerAdPlanConfig,
    } = useRequest(
      async (shopsToQuery: any[]) => {
        if (shopsToQuery.length > 1000) {
          message.error('最多选择1000家门店，请重新选择');
          throw new Error('最多选择1000家门店，请重新选择');
        }
        const res = await gdRequest(
          'amap-sales-operation.AdOperationManageFacade.queryMerAdPlanConfig',
          {
            merchantId,
            shopIds: shopsToQuery.map((shop) => shop.value),
          },
        );
        return res as adPlan.IMerchantAdPlanConfig;
      },
      {
        manual: true,
      },
    );

    const filteredAdConfig = useMemo(() => {
      if (!adConfig) {
        return adConfig;
      }
      // Filter the data based on the shops that have been queried
      const queryShopIds = new Set(queryShops.map((s) => s.value));
      return {
        ...adConfig,
        shopAdPlanConfigs: (adConfig.shopAdPlanConfigs || []).filter((s) =>
          queryShopIds.has(s.shopId),
        ),
      };
    }, [adConfig, queryShops]);

    // 查询按钮点击事件
    const handleQuery = () => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.查询'], {
        pid: merchantId,
        shopsCount: shops.length,
      });
      setQueryShops(shops);
      queryMerAdPlanConfig(shops);
    };

    const { run: onSwitchPreviewMode } = useRequest(
      async (checked: boolean) => {
        traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.切换预览模式']);
        if (checked) {
          try {
            await form.validateFields();
            tableRef.current?.updateDataFromForm(form.getFieldsValue(true));
            setPreviewMode(checked);
          } catch (error) {
            message.error('请将投放方案填写完整后再关闭');
            console.log(error);
            return;
          }
        }
        setPreviewMode(checked);
      },
      {
        throttleWait: 300,
      },
    );

    return (
      <Card bordered={false}>
        <Form labelCol={{ span: 2 }} form={form}>
          <Spin spinning={loading}>
            <Item label="商户">{adConfig?.merchantName}</Item>
            <Item label="广告数据">
              <Space size="large">
                <span>广告现金余额: {adConfig?.adCurrentBalance}</span>
                <span>当月广告消耗: {adConfig?.adCurrentMonthCost}</span>
                <span>昨日消耗: {adConfig?.adYestodayCost}</span>
              </Space>
            </Item>
            <Item label="选择门店" required>
              <Space>
                <ShopSelect
                  pid={merchantId}
                  style={{
                    width: 250,
                  }}
                  maxTagCount={3}
                  value={shops}
                  onChange={(value) => {
                    setShops(value);
                    if (value.length < queryShops.length) {
                      const valueIds = new Set(value.map((s) => s.value));
                      setQueryShops((currentQueryShops) =>
                        currentQueryShops.filter((qs) => valueIds.has(qs.value)),
                      );
                    }
                  }}
                />
                <Button type="primary" onClick={handleQuery} disabled={shops.length === 0}>
                  查询
                </Button>
              </Space>
            </Item>
          </Spin>
          {/* 只有在已查询且有门店时才展示后续内容 */}
          {queryShops && queryShops.length > 0 ? (
            <>
              <Item label="投放规则">
                <Switch
                  checkedChildren="修改"
                  unCheckedChildren="修改"
                  checked={!previewMode}
                  onChange={(checked) => onSwitchPreviewMode(!checked)}
                />
              </Item>
              <div id={domId}>
                <Item
                  colon={false}
                  help={
                    !previewMode ? (
                      <Space
                        style={{
                          justifyContent: 'end',
                          flexDirection: 'row-reverse',
                          marginTop: 12,
                          marginBottom: 24,
                          width: '100%',
                        }}
                      >
                        <Button
                          onClick={() => {
                            onSwitchPreviewMode(true);
                            traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.关闭修改'], {
                              pid: merchantId,
                            });
                          }}
                          type="primary"
                        >
                          关闭修改
                        </Button>
                        关闭后本次修改不保存，页面刷新后恢复初始值
                      </Space>
                    ) : null
                  }
                >
                  <Form.List name="adPlan">
                    {() => {
                      return (
                        <AdPlanTable
                          ref={tableRef}
                          form={form}
                          merchantId={merchantId}
                          loading={loading}
                          onRemoveShop={(id: string) => {
                            traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.移除门店'], {
                              pid: merchantId,
                              shopId: id,
                            });
                            setShops(shops.filter((i) => i.value !== id));
                            setQueryShops(queryShops.filter((i) => i.value !== id));
                          }}
                          onTableDataChange={setTableData}
                          previewMode={previewMode}
                          adConfig={filteredAdConfig}
                          shops={queryShops}
                          isCapturing={isCapturing}
                        />
                      );
                    }}
                  </Form.List>
                </Item>
              </div>
              {sendMerchantNewsAvailable && (
                <Item label="企微群话术">
                  <BdTextTpl ref={bdWordsRef} scene="AD_PLAN_CONFIG" hideScenePicker />
                </Item>
              )}
            </>
          ) : null}
        </Form>
      </Card>
    );
  },
);
