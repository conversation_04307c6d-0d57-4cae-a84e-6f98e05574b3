import { Form, Input, InputNumber } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { useEffect, useMemo, useRef, useState } from 'react';

export const EditableItem = ({
  value,
  onEdit,
  format,
  previewMode,
}: {
  previewMode?: boolean;
  value?: any;
  onEdit?: () => void;
  format?: (value: any) => string;
}) => {
  const text = useMemo(() => {
    if (format) {
      return format(value);
    }
    return value || '';
  }, [value, format]);
  if (previewMode) {
    return <span>{text}</span>;
  }
  return (
    <span
      style={{
        wordBreak: 'break-all',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      {text}
      <EditOutlined
        style={{ marginLeft: 4, ...(text?.length ? {} : { color: 'red' }) }}
        onClick={onEdit}
      />
    </span>
  );
};

export const EditableColumItem = ({
  config,
  name,
  style,
  previewMode,
}: {
  previewMode?: boolean;
  style?: React.CSSProperties;
  name: Array<string | number>;
  config: adPlanFEModel.IEditColumnItemConfig;
}) => {
  const [editable, setEditable] = useState<boolean>();
  const ref = useRef<any>();
  const el = useMemo(() => {
    if (!editable || previewMode) {
      return <EditableItem previewMode={previewMode} onEdit={() => setEditable(true)} />;
    }
    switch (config.type) {
      case 'textarea':
        return (
          <Input.TextArea
            ref={ref}
            style={{
              width: 100,
            }}
            autoSize={{
              minRows: 1,
              maxRows: 5,
            }}
            {...(config.config || {})}
            onBlur={() => setEditable(false)}
          />
        );
      case 'number':
        return (
          <InputNumber
            ref={ref}
            style={{
              width: 100,
            }}
            {...(config.config || {})}
            onBlur={() => setEditable(false)}
            type="number"
          />
        );
      case 'text':
      default:
        return (
          <Input
            ref={ref}
            style={{
              width: 100,
            }}
            onBlur={() => setEditable(false)}
            {...(config.config || {})}
          />
        );
    }
  }, [config, editable, previewMode]);
  useEffect(() => {
    if (editable) {
      ref.current?.focus?.();
    }
  }, [editable]);
  return (
    <Form.Item
      rules={[
        {
          required: true,
        },
      ]}
      noStyle
      name={name}
      initialValue={config.defaultValue}
    >
      {el}
    </Form.Item>
  );
};
