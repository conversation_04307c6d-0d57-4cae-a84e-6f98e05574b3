import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer, message, Modal, Row } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { AdPlan, IAdPlanRef } from './index';
import { useRequest } from 'ahooks';
import { getMerchantReviewExtInfo } from '@/services';
import fetch from '@alife/amap-fetch';
import { v4 as uuid } from 'uuid';
import * as XLSX from 'xlsx';
import { traceClick, PageSPMKey, ModuleSPMKey, traceExp } from '@/utils/trace';

export const AdPlanModal = ({ merchantId, children }: { merchantId: string; children: any }) => {
  const [open, setOpen] = useState(false);
  const [shopsCount, setShopsCount] = useState(0);
  const [adPlanForm, setAdPlanForm] = useState<any>(null);
  const {
    data: extInfo,
    loading,
    run: getExtInfo,
  } = useRequest(
    async () => {
      const res = await getMerchantReviewExtInfo(merchantId, 'AD_PLAN_CONFIG');
      return res;
    },
    {
      manual: true,
    },
  );
  const ref = useRef<IAdPlanRef>();
  useEffect(() => {
    if (open) {
      traceExp(PageSPMKey.首页, ModuleSPMKey['投放方案.模块曝光'], {
        pid: merchantId,
      });
      getExtInfo();
    }
  }, [open]);
  const { loading: sending, run: send } = useRequest(
    async () => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.发送至企微群'], {
        pid: merchantId,
      });
      const res = await ref.current?.send();
      if (res) {
        setOpen(false);
      }
    },
    {
      manual: true,
      debounceLeading: true,
      debounceWait: 3000,
      debounceTrailing: false,
    },
  );

  const { loading: downloading, run: download } = useRequest(
    async () => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.下载图片'], {
        pid: merchantId,
      });
      await ref.current?.saveImg();
    },
    {
      manual: true,
    },
  );

  const { loading: downloadingExcel, run: downloadExcel } = useRequest(
    async () => {
      if (ref.current && adPlanForm) {
        const formValues = adPlanForm.getFieldsValue(true);
        if (typeof ref.current.updateDataFromForm === 'function') {
          ref.current.updateDataFromForm(formValues);
        }
      }
      await new Promise((resolve) => setTimeout(resolve, 100));
      const tableData = await ref.current?.getTableData();
      if (tableData && tableData.length > 0) {
        exportToExcel(tableData);
      } else {
        message.warning('暂无数据可导出');
      }
    },
    {
      manual: true,
    },
  );

  const exportToExcel = (data: any[]) => {
    const headers = [
      '门店名称',
      '投放目标',
      '类型',
      '计划日预算(元)',
      '出价(元)',
      '投放时间',
      '投放周期',
      '预估效果',
    ];

    const rows = data.map((row) => [
      row.shopName || '',
      row.productName || '',
      row.isSuggestion ? '建议' : '当前',
      typeof row.dailyBudget === 'object' ? row.dailyBudget.defaultValue : row.dailyBudget || '',
      typeof row.offer === 'object' ? row.offer.defaultValue : row.offer || '',
      typeof row.interval === 'object' ? row.interval.defaultValue : row.interval || '',
      typeof row.cycle === 'object' ? row.cycle.defaultValue : row.cycle || '',
      typeof row.effectPrediction === 'object'
        ? row.effectPrediction.defaultValue
        : row.effectPrediction || '-',
    ]);

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '广告投放方案');
    XLSX.writeFile(workbook, `广告投放方案_${new Date().toLocaleDateString()}.xlsx`);
    traceClick(PageSPMKey.首页, ModuleSPMKey['投放方案.下载'], {
      pid: merchantId,
    });
    message.success('表格下载成功');
  };

  const { run: checkShowModal } = useRequest(
    async () => {
      const { data } = await fetch({
        params: {
          action: 'amap-sales-operation.AdOperationManageFacade.queryAdvertiserInfo',
          bizContent: {
            requestId: uuid(),
            merchantId,
          },
        },
      });
      if (data?.existAdAccount) {
        setOpen(true);
      } else {
        message.error('商户未开通广告推广服务，提示商家开通后再创建投放方案');
      }
      return data;
    },
    {
      throttleWait: 500,
      manual: true,
    },
  );

  const [sendable, setSendAble] = useState(false);

  return (
    <>
      {React.cloneElement(children, {
        ...(children.props || {}),
        onClick: (e) => {
          e?.preventDefault?.();
          checkShowModal();
        },
      })}
      <Drawer
        destroyOnClose
        onClose={() => {
          Modal.confirm({
            title: '确认关闭？',
            content: '关闭弹层数据不会保存，确认关闭吗',
            onOk: () => {
              setOpen(false);
            },
          });
        }}
        footer={
          <Row justify="end" align="middle">
            {extInfo?.sendMerchantNewsAvailable && extInfo?.lastReachTime && !loading && (
              <Badge
                color="green"
                text={`上次发送时间${extInfo.lastReachTime}`}
                style={{
                  marginRight: 12,
                }}
              />
            )}
            {extInfo?.sendMerchantNewsAvailable && sendable && (
              <Button
                onClick={send}
                loading={sending}
                type="primary"
                style={{ marginRight: 12 }}
                disabled={shopsCount > 20}
              >
                发送至企微群
              </Button>
            )}
            <Button
              onClick={async () => {
                if (!ref.current) return;
                if (ref.current && typeof ref.current.getTableData === 'function') {
                  await downloadExcel();
                }
              }}
              loading={downloadingExcel}
              type="default"
              style={{
                marginRight: 12,
              }}
            >
              下载表格
            </Button>
            {sendable && (
              <Button
                disabled={loading || shopsCount > 1000 || shopsCount > 20}
                onClick={download}
                loading={downloading}
                type="primary"
              >
                下载图片
              </Button>
            )}
          </Row>
        }
        title="投放方案"
        open={open}
        width={1300}
      >
        <AdPlan
          ref={ref}
          merchantId={merchantId}
          onShowHideSendBtn={setSendAble}
          onShopsCountChange={setShopsCount}
          refForm={setAdPlanForm}
          sendMerchantNewsAvailable={extInfo?.sendMerchantNewsAvailable}
        />
      </Drawer>
    </>
  );
};
