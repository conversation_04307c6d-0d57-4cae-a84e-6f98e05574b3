import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useLocalStorageState } from 'ahooks';
import Draggable, { DraggableEvent, DraggableData } from 'react-draggable';
import './index.less';
import { ModuleSPMKey, PageSPMKey, traceClick, traceExp } from '@/utils/trace';

interface IProps {}

interface Position {
  x: number;
  y: number;
}

const ROBOT_WIDTH = 52;
const ROBOT_HEIGHT = 100;
const EDGE_BUFFER = 5; // 缓冲区域，防止内容被裁切

export const Robot: React.FC<IProps> = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [bounds, setBounds] = useState({ left: 0, top: 0, right: 0, bottom: 0 });
  const dragRef = useRef<HTMLDivElement>(null);
  const draggedRef = useRef(false);
  const lastWindowSize = useRef({ width: 0, height: 0 });

  // 获取默认位置
  const getDefaultPosition = useCallback((): Position => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    return {
      x: windowWidth - ROBOT_WIDTH - EDGE_BUFFER - 10,
      y: Math.max(EDGE_BUFFER, windowHeight - ROBOT_HEIGHT - 150),
    };
  }, []);

  // 使用 useLocalStorageState 来持久化位置
  const [position, setPosition] = useLocalStorageState<Position>('robot-position', {
    defaultValue: getDefaultPosition(),
  });

  useEffect(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey.机器人);

    // 初始化位置和边界
    const updatePosition = () => {
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const lastWidth = lastWindowSize.current.width;
      const lastHeight = lastWindowSize.current.height;

      // 如果是首次初始化或没有有效位置
      if (!position || lastWidth === 0) {
        setPosition(getDefaultPosition());
        lastWindowSize.current = { width: windowWidth, height: windowHeight };
      }
      // 如果窗口大小发生变化，需要调整位置保持相对位置
      else if (lastWidth !== windowWidth || lastHeight !== windowHeight) {
        const currentPos = position;

        // 判断机器人之前更靠近哪个边缘
        const wasNearLeftEdge = currentPos.x < lastWidth / 2;
        const distanceFromTop = currentPos.y;
        const distanceFromBottom = lastHeight - currentPos.y - ROBOT_HEIGHT;

        let newX;
        let newY;

        // 水平位置：保持靠近原来的边缘
        if (wasNearLeftEdge) {
          // 保持与左边缘的相对距离
          const leftDistance = currentPos.x;
          newX = Math.min(leftDistance, windowWidth - ROBOT_WIDTH - EDGE_BUFFER);
        } else {
          // 保持与右边缘的相对距离
          const rightDistance = lastWidth - currentPos.x - ROBOT_WIDTH;
          newX = windowWidth - ROBOT_WIDTH - rightDistance;
        }

        // 垂直位置：尽量保持原来的位置，但要在边界内
        if (distanceFromBottom < distanceFromTop && distanceFromBottom < 100) {
          // 如果更靠近底部，保持与底部的距离
          newY = windowHeight - ROBOT_HEIGHT - distanceFromBottom;
        } else {
          // 否则保持与顶部的距离
          newY = distanceFromTop;
        }

        // 确保新位置在有效范围内
        newX = Math.max(EDGE_BUFFER, Math.min(newX, windowWidth - ROBOT_WIDTH - EDGE_BUFFER));
        newY = Math.max(EDGE_BUFFER, Math.min(newY, windowHeight - ROBOT_HEIGHT - EDGE_BUFFER));

        setPosition({ x: newX, y: newY });
        lastWindowSize.current = { width: windowWidth, height: windowHeight };
      }

      setBounds({
        left: EDGE_BUFFER,
        top: EDGE_BUFFER,
        right: windowWidth - ROBOT_WIDTH - EDGE_BUFFER,
        bottom: windowHeight - ROBOT_HEIGHT - EDGE_BUFFER,
      });
    };

    updatePosition();
    window.addEventListener('resize', updatePosition);

    return () => {
      window.removeEventListener('resize', updatePosition);
    };
  }, [position, setPosition, getDefaultPosition]);

  // 自动吸附到边缘
  const snapToEdge = useCallback((x: number, y: number) => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    let newX = x;
    let newY = y;

    // 水平方向吸附
    const centerX = x + ROBOT_WIDTH / 2;
    if (centerX < windowWidth / 2) {
      // 吸附到左边，留出缓冲区域
      newX = EDGE_BUFFER;
    } else {
      // 吸附到右边，留出缓冲区域
      newX = windowWidth - ROBOT_WIDTH - EDGE_BUFFER;
    }

    // 垂直方向边界检查，确保完全在窗口内且留出缓冲区域
    if (y < EDGE_BUFFER) {
      newY = EDGE_BUFFER;
    } else if (y + ROBOT_HEIGHT > windowHeight - EDGE_BUFFER) {
      newY = windowHeight - ROBOT_HEIGHT - EDGE_BUFFER;
    } else {
      newY = y;
    }

    // 最终边界检查，确保位置有效且留出缓冲区域
    newX = Math.max(EDGE_BUFFER, Math.min(newX, windowWidth - ROBOT_WIDTH - EDGE_BUFFER));
    newY = Math.max(EDGE_BUFFER, Math.min(newY, windowHeight - ROBOT_HEIGHT - EDGE_BUFFER));

    return { x: newX, y: newY };
  }, []);

  // 拖拽开始
  const handleStart = () => {
    setIsDragging(true);
    draggedRef.current = false;
  };

  // 拖拽中
  const handleDrag = (e: DraggableEvent, data: DraggableData) => {
    draggedRef.current = true;
    // 这里不更新position，避免频繁触发localStorage
  };

  // 拖拽结束
  const handleStop = (e: DraggableEvent, data: DraggableData) => {
    setIsDragging(false);

    // 自动吸附到边缘
    const snappedPosition = snapToEdge(data.x, data.y);
    setPosition(snappedPosition);

    // 延迟重置拖拽状态，防止点击事件被误触发
    setTimeout(() => {
      draggedRef.current = false;
    }, 100);
  };

  // 唤起钉钉运维小蜜对话框
  const handleArouseRobot = () => {
    // 如果正在拖拽或刚拖拽过，不触发点击事件
    if (isDragging || draggedRef.current) return;

    window.open(
      `dingtalk://dingtalkclient/action/jumprobot?dingtalkid=$:LWCP_v1:$TolpPK7p4RtJpcLdL4y/SyKonkSUKx3+&content=${encodeURIComponent(
        '你好',
      )}`,
    );
    traceClick(PageSPMKey.首页, ModuleSPMKey.机器人);
  };

  // 如果position还没有初始化，先不渲染
  if (!position) {
    return null;
  }

  return (
    <Draggable
      nodeRef={dragRef}
      position={position}
      onStart={handleStart}
      onDrag={handleDrag}
      onStop={handleStop}
      bounds={bounds}
    >
      <div
        ref={dragRef}
        className="task-management-robot"
        onClick={handleArouseRobot}
        style={{
          transition: isDragging ? 'none' : 'all 0.3s ease-out',
          cursor: isDragging ? 'grabbing' : 'grab',
        }}
      />
    </Draggable>
  );
};
