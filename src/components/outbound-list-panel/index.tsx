import React, { useState, useRef } from 'react';
import { Modal, Tabs, message as throwMsg } from 'antd';
import { debounce } from 'lodash';
import {
  C33_CALL_ENUM,
  OUTBOUND_LIST_PANEL_TAB_ENUM,
  OUTBOUND_LIST_PANEL_TAB_NAME,
  OUTBOUND_VISIT_RECORD_TARGET_TYPE,
  PID_VISIT,
} from '@/common/const';
import { DetailShopList } from '../detail-shop-list';
import { AgentOperationMerchantDetailDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import { ComponentLoader } from '@/components/component-loader';
import { MerchantInfo } from '../merchant-info';
import service from '@/_docplus/target/service/alsc-kbt-intergration-toolkit-client/CallCenterQueryGatewayService';
import { uuid } from '@alife/kb-biz-util';
import { showVisitRecordFormPanel } from '../visit-plan-agency/visit-record-form';
import { VisitPlanAgency } from '../visit-plan-agency';
import './index.less';
import { isAmapXy, getKpFlag } from '@/utils';

const { TabPane } = Tabs;

interface IProps {
  /**
   * echo拨打
   */
  callOut: (...args: any) => void;
  pid: string;
  selectedId: string;
  selectType: string;
  merchantDetail: AgentOperationMerchantDetailDTO;
  onListPanelClick: (value) => void; // 左侧区域点击事件，门店根据shopid改变颜色
  loadMerchantDetail: (params: any) => void;
  missVisitRecordList: string[]; // 未记拜访小黄条数据
  loadMissVisitRecord: () => void;
}
export const OutboundListPanel: React.FC<IProps> = ({
  callOut,
  pid,
  selectedId,
  selectType,
  merchantDetail,
  onListPanelClick,
  loadMerchantDetail,
  missVisitRecordList,
  loadMissVisitRecord,
}) => {
  const [activeKey, setActiveKey] = useState<string>(
    OUTBOUND_LIST_PANEL_TAB_ENUM.MERCHANT_INFORMATION,
  );
  const [visitPlanTotal, setVisitPlanTotal] = useState<number>(0);
  const visitPlanRef = useRef(null);

  const handleTabChange = (key: string) => {
    setActiveKey(key);
    if (key === OUTBOUND_LIST_PANEL_TAB_ENUM.VISIT_PLAN) {
      visitPlanRef?.current?.recordPlanRefresh({
        pageNum: 1,
        pageSize: 10,
        targetId: pid,
        targetType: OUTBOUND_VISIT_RECORD_TARGET_TYPE.MERCHANT,
      });
    }
  };

  const onGetTotal = (total: number) => {
    setVisitPlanTotal(total || 0);
  };

  /**
   * 拜访计划拨打
   * 1.先记拜访校验
   * 2.才是走外呼
   */
  const showConfirm = debounce(async (data) => {
    await service
      .getCallRecordMissingVisit({
        bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
        requestId: uuid(),
      })
      .then((res) => {
        if (res?.success) {
          if (res?.data?.length > 0) {
            // 表示有未记拜访的record，需要拦截
            Modal.warning({
              title: '你有未填写的记拜访，请填写完成后再继续拨打',
              width: 438,
              okText: '记拜访',
              onOk() {
                showVisitRecordFormPanel({
                  callRecordId: res.data[0],
                  contactScene: PID_VISIT,
                });
              },
            });
          } else {
            const phoneNumber = isAmapXy() ? data?.contactNo : data?.mainContactWay?.contactValue;
            const cpName = isAmapXy() ? data.contactName : data.contactPersonName;
            callOut({
              phoneNumber,
              bizInfo: {
                targetId: pid,
                bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
                kpContactId: data?.contactId,
                targetType: OUTBOUND_VISIT_RECORD_TARGET_TYPE.MERCHANT,
                contactPerson: {
                  cpId: data?.contactId,
                  cpName,
                  supportCorrect: data.supportCorrect,
                  cpPosition: data.roleDesc,
                  keyPersonFlag: getKpFlag(data),
                  contactNo: phoneNumber,
                },
              },
            });
          }
        } else {
          // 报错
          throwMsg.error(res?.resultMessage);
        }
      })
      .catch((error) => {
        throwMsg.error(error?.errorMessage);
      });
  }, 600);

  return (
    <div className="left-panel-list-wrapper">
      <VisitPlanAgency
        missVisitRecordList={missVisitRecordList}
        loadMissVisitRecord={loadMissVisitRecord}
        pid={pid}
      />
      <Tabs activeKey={activeKey} onChange={handleTabChange}>
        <TabPane
          tab={OUTBOUND_LIST_PANEL_TAB_NAME.MERCHANT_INFORMATION}
          key={OUTBOUND_LIST_PANEL_TAB_ENUM.MERCHANT_INFORMATION}
        >
          <MerchantInfo
            callOut={callOut}
            isMerchantSelected={selectType === OUTBOUND_LIST_PANEL_TAB_ENUM.MERCHANT_INFORMATION}
            onListPanelClick={() =>
              onListPanelClick({
                pid,
                selectType: OUTBOUND_LIST_PANEL_TAB_ENUM.MERCHANT_INFORMATION,
              })
            }
            merchantDetail={merchantDetail}
            loadMerchantDetail={loadMerchantDetail}
          />
          <DetailShopList pid={pid} selectedId={selectedId} onListPanelClick={onListPanelClick} />
        </TabPane>
        <TabPane
          forceRender
          tab={`${OUTBOUND_LIST_PANEL_TAB_NAME.VISIT_PLAN}(${visitPlanTotal})`}
          key={OUTBOUND_LIST_PANEL_TAB_ENUM.VISIT_PLAN}
        >
          <ComponentLoader appName="kb-visit" componentName="visit-plan-list">
            {(Comp) => (
              <Comp
                targetId={pid}
                targetType={OUTBOUND_VISIT_RECORD_TARGET_TYPE.MERCHANT}
                scene={PID_VISIT}
                entityType="pid"
                onGetTotal={onGetTotal}
                onClickList={debounce(
                  () =>
                    onListPanelClick({
                      pid,
                      selectType: OUTBOUND_LIST_PANEL_TAB_ENUM.MERCHANT_INFORMATION,
                    }),
                  600,
                )}
                ref={visitPlanRef}
                onCall={showConfirm}
                sourceFrom="XY_PC_AGENT_VISIT_PLAN"
              />
            )}
          </ComponentLoader>
        </TabPane>
      </Tabs>
    </div>
  );
};
