import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Table, Button, Spin, TableColumnType, Image, Flex } from 'antd';
import { useRequest } from 'ahooks';
import { StatusMap, type IDraftItem } from '@/types/ai-material/merchant';
import { queryDraftList } from '@/services/ai-material';
import { trace } from '@/utils/trace';
import { getFullTime } from '@/utils';

interface MaterialModalProps {
  title: string;
  open: boolean;
  onOk: (selectedIds: string[]) => void;
  onCancel: () => void;
  shopId: string;
  moduleType: string;
  defaultSelectedKeys?: string[];
}

const MaterialModal: React.FC<MaterialModalProps> = ({
  title,
  open,
  onOk,
  onCancel,
  shopId,
  moduleType,
  defaultSelectedKeys = [],
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(defaultSelectedKeys);

  // 拉取素材列表
  const { data: draftListResponse, loading } = useRequest(
    async () => {
      if (shopId && moduleType) {
        const list = await queryDraftList({
          shopId,
          moduleType,
          extInfo: {
            filterNoNeedCollect: true,
          },
        });
        if (!defaultSelectedKeys.length && open) {
          setSelectedRowKeys(list.dataList.map((item) => item.draftId));
        }
        return list;
      }
      return Promise.resolve({ dataList: [], pageInfo: { totalCount: 0 } });
    },
    { refreshDeps: [shopId, moduleType, open] },
  );

  const draftList = draftListResponse?.dataList || [];

  // 打开时重置选中项
  useEffect(() => {
    if (open) {
      setSelectedRowKeys(defaultSelectedKeys);
      trace('material-modal-open', { shopId, moduleType });
    }
  }, [open, defaultSelectedKeys]);

  // 表格字段
  const columns: Array<TableColumnType<IDraftItem>> = useMemo(
    () => [
      {
        title: '推荐素材',
        dataIndex: 'name',
        key: 'name',
        render: (name: string, record: IDraftItem) => (
          <div>
            <Flex gap={10}>
              {/* 左侧图片 */}
              {record.image && (
                <Image
                  src={record.image}
                  alt="素材图片"
                  width={80}
                  height={60}
                  style={{ borderRadius: 4, objectFit: 'cover' }}
                />
              )}
              {/* 右侧信息 */}
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: 14, fontWeight: 500, marginBottom: 4 }}>
                  {name || record.summary?.name || '-'}
                </div>
              </div>
            </Flex>
            {/* 显示提醒信息 */}
            {record.tips && <div style={{ color: '#ff7875', fontSize: 12 }}>{record.tips}</div>}
            {/* 显示重复标识 */}
            {record.extInfo?.duplicateTag === 'true' && (
              <div style={{ color: '#ff4d4f', fontSize: 12 }}>线上已存在</div>
            )}
          </div>
        ),
      },
      { title: '推荐时间', dataIndex: 'createTime', key: 'createTime', render: getFullTime },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => StatusMap[status] || '-',
      },
    ],
    [],
  );

  return (
    <Modal title={title} open={open} onCancel={onCancel} footer={null} width={720} destroyOnClose>
      <Spin spinning={loading}>
        <Table
          rowKey="draftId"
          columns={columns}
          dataSource={draftList}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          pagination={false}
          style={{ marginBottom: 16 }}
        />
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onCancel} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            onClick={() => {
              onOk(selectedRowKeys as string[]);
              trace('material-modal-ok', { shopId, moduleType, selectedIds: selectedRowKeys });
            }}
          >
            确认
          </Button>
        </div>
      </Spin>
    </Modal>
  );
};

export default MaterialModal;
