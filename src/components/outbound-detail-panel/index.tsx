import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Tabs, Empty } from 'antd';
import {
  OUTBOUND_DETAIL_PANEL_TAB_ENUM,
  OUTBOUND_VISIT_RECORD_TARGET_TYPE,
  PID_VISIT,
  TEL_VISIT,
} from '@/common/const';
import { MerchantDetail } from '../merchant-detail';
import { getShopDetailLink, getWangpuShopDetailLink } from '@/common/utils';
import { ReplayData } from '../replay-data';
import './index.less';
import { AgentOperationMerchantDetailDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import { ComponentLoader } from '@/components/component-loader';
import { showVisitRecordFormPanel } from '../visit-plan-agency/visit-record-form';
import { isAmapXy } from '@/utils';
import { IfButtonShow } from '../server-controller/useAction';
import { ActionButtonType } from '@/constants';
import { useActions } from '@/context/action-controller';
import { ModuleSPMKey, PageSPMKey, traceClick } from '@/utils/trace';

const { TabPane } = Tabs;

interface IProps {
  pid: string;
  merchantName: string;
  merchantDetail: AgentOperationMerchantDetailDTO;
  merchantDetailLoading: boolean;
  visitRecordRef: React.MutableRefObject<any>;
  shopInfo?: any;
  boundDetailTabKey?: string;
}

export const OutboundDetailPanel: React.FC<IProps> = ({
  pid,
  merchantName,
  merchantDetail,
  merchantDetailLoading,
  visitRecordRef,
  shopInfo,
  boundDetailTabKey,
}) => {
  const [activeKey, setActiveKey] = useState<string>(boundDetailTabKey);

  useEffect(() => {
    if (shopInfo?.shopId) {
      setActiveKey(OUTBOUND_DETAIL_PANEL_TAB_ENUM.SHOP_DETAIL);
    } else if (!boundDetailTabKey) {
      setActiveKey(OUTBOUND_DETAIL_PANEL_TAB_ENUM.MERCHANT_DETAIL);
    }
  }, [shopInfo?.shopId]);

  const handleTabChange = (key: string) => {
    // 根据切换的tab确定埋点模块
    let moduleKey = '';
    if (key === OUTBOUND_DETAIL_PANEL_TAB_ENUM.VISIT_RECORD) {
      moduleKey = ModuleSPMKey['拜访记录.tab切换'];
    } else {
      moduleKey = `${ModuleSPMKey.门店详情}.${key}`;
    }

    traceClick(PageSPMKey.首页, moduleKey, {
      tabKey: key,
      pid,
    });
    setActiveKey(key);
  };

  const renderShopDetail = useCallback(() => {
    const { shopId, kbShopId } = shopInfo || {};
    let iframeContent = (
      <iframe
        id={'shop-detail'}
        src={getShopDetailLink(kbShopId || shopId)}
        style={{ width: '100%', height: '100%', flex: 'auto', border: 'none' }}
      />
    );
    if (isAmapXy() && !kbShopId) {
      // 新版轩辕需要有kbShopId
      iframeContent = (
        <iframe
          id={'shop-detail'}
          src={getWangpuShopDetailLink(shopId)}
          style={{ width: '100%', height: '100%', flex: 'auto', border: 'none' }}
          name="iframeHideMenu"
        />
      );
    }
    return (
      <TabPane
        tab="门店详情"
        key={OUTBOUND_DETAIL_PANEL_TAB_ENUM.SHOP_DETAIL}
        style={{ height: '100%' }}
      >
        {iframeContent}
      </TabPane>
    );
  }, [shopInfo]);

  const currentTargetType = useMemo(() => {
    const currentShop = isAmapXy()
      ? OUTBOUND_VISIT_RECORD_TARGET_TYPE.AMAP_SHOP
      : OUTBOUND_VISIT_RECORD_TARGET_TYPE.STORE;
    return shopInfo?.shopId ? currentShop : OUTBOUND_VISIT_RECORD_TARGET_TYPE.MERCHANT;
  }, [shopInfo]);

  const { actionMap } = useActions();

  return (
    <Tabs activeKey={activeKey} onChange={handleTabChange} className="outbound-detail-panel">
      {!shopInfo?.shopId && (
        <TabPane tab="商户详情" key={OUTBOUND_DETAIL_PANEL_TAB_ENUM.MERCHANT_DETAIL}>
          <MerchantDetail detail={merchantDetail} loading={merchantDetailLoading} />
        </TabPane>
      )}
      {shopInfo?.shopId && renderShopDetail()}
      <TabPane tab="拜访记录" key={OUTBOUND_DETAIL_PANEL_TAB_ENUM.VISIT_RECORD}>
        <ComponentLoader appName="kb-visit" componentName="visit-record">
          {(Comp) => (
            <Comp
              targetId={shopInfo?.shopId || pid}
              targetType={currentTargetType}
              showReplenish={!shopInfo?.shopId}
              scene="AGENT_TARGET"
              onReplenish={() => {
                showVisitRecordFormPanel({
                  replenish: true,
                  targetId: shopInfo?.shopId || pid,
                  targetType: shopInfo?.shopId
                    ? OUTBOUND_VISIT_RECORD_TARGET_TYPE.STORE
                    : OUTBOUND_VISIT_RECORD_TARGET_TYPE.MERCHANT,
                  contactScene: shopInfo?.shopId ? TEL_VISIT : PID_VISIT,
                });
              }}
              ref={visitRecordRef}
            />
          )}
        </ComponentLoader>
      </TabPane>
      {actionMap?.get?.(ActionButtonType.电话外呼复盘数据tab)?.showButton && (
        <TabPane tab="复盘数据" key={OUTBOUND_DETAIL_PANEL_TAB_ENUM.REPLAY_DATA}>
          {!shopInfo?.shopId && <ReplayData pid={pid} merchantName={merchantName} />}
          {shopInfo?.shopId && (
            <ReplayData
              pid={pid}
              merchantName={merchantName}
              defaultShop={{
                shop: {
                  id: shopInfo?.shopId,
                  name: shopInfo?.shopName,
                },
              }}
            />
          )}
        </TabPane>
      )}
    </Tabs>
  );
};
