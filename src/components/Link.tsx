import { Link as IceLink } from '@ice/runtime';
import classNames from 'classnames';
import { PropsWithChildren } from 'react';
import { LinkProps } from 'react-router-dom';
import styled from 'styled-components';
interface IProps {
  disabled?: boolean;
}
const StyledLink = styled(IceLink)`
  &.disabled {
    color: #999;
    cursor: not-allowed;
  }
`;
export default function Link(props: PropsWithChildren<LinkProps & IProps>) {
  return (
    <StyledLink
      {...props}
      className={classNames([
        props.className || '',
        {
          disabled: props.disabled,
        },
      ])}
      onClick={(e) => {
        if (props.disabled) {
          e.preventDefault();
        } else {
          props.onClick?.(e);
        }
      }}
    >
      {props.children}
    </StyledLink>
  );
}
