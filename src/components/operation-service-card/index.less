.operation-service-card {
  .ant-card-body {
    padding: 16px 14px;
  }

  .service-name-container {
    width: 100%;
    display: block;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .service-name {
      color: #000;
      font-size: 16px;
      line-height: 24px;
    }
    .name-icon {
      margin-left: 5px;
      cursor: help;
    }
  }

  .service-desc {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
    min-height: 44px;
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .service-status-container {
    margin-top: 18px;
    color: rgba(0,0,0,0.85);
    .service-frequency {
      display: flex;
      justify-content: space-between;
      align-items: center; 
      font-size: 12px;
    }

    .service-status {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;
      .service-button {
        min-width: 74px;
      }
      .service-button-disabled {
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;
        border-color: #d9d9d9;    
        text-shadow: none;
        box-shadow: none;
      }
    }

    .service-operate {
      display: flex;
      justify-content: flex-end;
      a {
        margin-left: 14px;
      }
    }
  }
}