import React from 'react';
import classNames from 'classnames';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Link from '../Link';
import './index.less';
import { OPERATING_TASK_DETAIL_STATUS_ENUM, OPERATING_TASK_JUMP_TYPE } from '@/common/const';
import {
  MerchantTaskDetailDTO,
  TaskJumpDTO,
} from '@/_docplus/target/types/amap-sales-operation-client';
import { IfButtonShow } from '@/components/server-controller/useAction';

interface IProps {
  taskDetail: MerchantTaskDetailDTO;
  onTaskClick: (taskJumpDTO: TaskJumpDTO, taskType: string) => void;
}
export const OperationServiceCard: React.FC<IProps> = ({ taskDetail, onTaskClick }) => {
  const {
    taskName,
    taskTip,
    taskDesc,
    taskServiceNum,
    taskIndicatorCompletedNum,
    taskDetailStatus,
    taskDetailJumpList = [],
    taskType,
  } = taskDetail || {};
  const finished = taskDetailStatus === OPERATING_TASK_DETAIL_STATUS_ENUM.FINISH;
  const [firstTaskDetail, ...restTaskDetail] = taskDetailJumpList || [];

  const renderFirstTaskButtonText = (value: TaskJumpDTO) => {
    // 任务详情去完成按钮 需要根据完成状态决定展示“已完成”或“未完成”
    if (value?.buttonType === OPERATING_TASK_JUMP_TYPE.GO_FINISH) {
      return finished ? '已完成' : '去完成';
    }
    return value?.buttonText || '-';
  };
  const handleClick = (e: any, value: TaskJumpDTO, type: string) => {
    e.preventDefault();
    onTaskClick(value, type);
  };
  return (
    <Card bordered={false} className="operation-service-card">
      <div className="service-name-container">
        <span className="service-name">{taskName || '-'}</span>
        {taskTip && (
          <Tooltip title={taskTip}>
            <QuestionCircleOutlined className="name-icon" />
          </Tooltip>
        )}
      </div>
      {taskDesc && <div className="service-desc">{taskDesc}</div>}
      <div className="service-status-container">
        <div className="service-frequency">
          <span>服务频次{taskServiceNum || '-'}</span>
          <span>本月完成{taskIndicatorCompletedNum || '-'}次</span>
        </div>
        <div className="service-status">
          <span>
            <Badge status={finished ? 'success' : 'error'} />
            {finished ? '已完成' : '未完成'}
          </span>
          <IfButtonShow button={firstTaskDetail}>
            <Button
              className={classNames({
                'service-button-disabled': finished,
                'service-button': true,
              })}
              type="primary"
              disabled={!!firstTaskDetail.greyButton}
              onClick={(e) => handleClick(e, firstTaskDetail, taskType)}
            >
              {renderFirstTaskButtonText(firstTaskDetail)}
            </Button>
          </IfButtonShow>
        </div>
        <div className="service-operate">
          {restTaskDetail.map((item: TaskJumpDTO) => (
            <IfButtonShow button={item}>
              <Link
                disabled={!!item.greyButton}
                to=""
                onClick={(e) => handleClick(e, item, taskType)}
              >
                {item?.buttonText || '-'}
              </Link>
            </IfButtonShow>
          ))}
        </div>
      </div>
    </Card>
  );
};
