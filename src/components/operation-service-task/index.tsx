import React from 'react';
import { Col, Row } from 'antd';
import { OperationServiceCard } from '../operation-service-card';
import {
  MerchantTaskDetailDTO,
  TaskJumpDTO,
} from '@/_docplus/target/types/amap-sales-operation-client';
import './index.less';

interface IProps {
  finishedList: MerchantTaskDetailDTO[];
  unfinishedList: MerchantTaskDetailDTO[];
  onTaskClick: (value: TaskJumpDTO, type: string) => void;
}
export const OperationServiceTask: React.FC<IProps> = ({
  finishedList,
  unfinishedList,
  onTaskClick,
}) => {
  return (
    <div className="operation-service-content">
      <div className="task-title">未完成任务（{unfinishedList?.length}项）</div>
      <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
        {unfinishedList.map((item, index) => (
          <Col span={6}>
            <OperationServiceCard taskDetail={item} key={index} onTaskClick={onTaskClick} />
          </Col>
        ))}
      </Row>
      <div className="task-title">已完成任务（{finishedList?.length}项）</div>
      <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
        {finishedList.map((item, index) => (
          <Col span={6}>
            <OperationServiceCard taskDetail={item} key={index} onTaskClick={onTaskClick} />
          </Col>
        ))}
      </Row>
    </div>
  );
};
