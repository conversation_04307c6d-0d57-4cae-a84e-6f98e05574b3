/* eslint-disable no-nested-ternary */
import { useRequest } from 'ahooks';
import { scriptLoader, checkEnv, EnvType, parseAppVersion } from '@alife/amap-mp-utils';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Spin } from 'antd';
import request from 'umi-request';
import { ErrorBoundary } from 'react-error-boundary';
import React, { useEffect, useMemo } from 'react';

const envMap = {
  [EnvType.DAILY]: 'daily',
  [EnvType.PROD]: 'prod',
  [EnvType.PRE]: 'pre',
};

const scriptLoaderPromise = {};
const loadedStyle: Record<
  string,
  {
    mounted?: boolean;
    el: HTMLLinkElement;
  }
> = {};

const getComp = (name?: string) => {
  if (name) {
    return (window as any)?.$$micro?.[name];
  }
};

export const ErrorBlock = ({ message }: { message: string }) => {
  return (
    <div
      style={{
        padding: '12px 0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <ExclamationCircleFilled style={{ color: 'red' }} />
      <span
        style={{
          marginLeft: 4,
        }}
      >
        {message}
      </span>
    </div>
  );
};

export const ComponentLoader = ({
  appName,
  componentName,
  children,
  $env,
}: {
  $env?: string;
  componentName: string;
  appName?: string;
  children?: (Comp: React.ComponentType<any>) => React.ReactElement;
}) => {
  const env = useMemo(() => {
    return $env || envMap[checkEnv()] || 'prod';
  }, [$env]);
  const compKey = useMemo(() => `${appName}/${env}/${componentName}/`, []);
  // const moduleName = useMemo(() => {
  //   return camelcase(componentName, {
  //     pascalCase: true,
  //     preserveConsecutiveUppercase: true,
  //   });
  // }, [componentName]);
  // const loadStyle = useCallback(() => {
  //   if (!loadedStyle[compKey]) {
  //     const linkEl = document.createElement('link');
  //     linkEl.href =
  //   }
  // }, []);
  const { data, loading, error, run } = useRequest(
    async () => {
      if (getComp(componentName)) {
        return getComp(componentName);
      }
      if (!scriptLoaderPromise[compKey]) {
        scriptLoaderPromise[compKey] = (async () => {
          const res = await request.get(`https://v.koubei.com/app/${appName}/${env}/version.json`);
          const { name, version } = parseAppVersion(res);
          if (!loadedStyle[compKey]?.mounted) {
            const linkEl = document.createElement('link');
            linkEl.rel = 'stylesheet';
            linkEl.href = `https://${
              env === 'prod' ? '' : 'dev.'
            }g.alicdn.com/${name}/${version}/micro-component/css/${componentName}.css`;
            document.head.appendChild(linkEl);
            loadedStyle[compKey] = {
              mounted: true,
              el: linkEl,
            };
          }
          return scriptLoader(
            `https://${
              env === 'prod' ? '' : 'dev.'
            }g.alicdn.com/${name}/${version}/micro-component/js/${componentName}.js`,
          );
        })();
      }
      await scriptLoaderPromise[compKey];
      return getComp(componentName);
    },
    {
      manual: true,
    },
  );
  // const unMountStyle = useCallback(() => {
  //   if (loadedStyle[compKey] && loadedStyle[compKey].mounted) {
  //     document.head.removeChild(loadedStyle[compKey].el);
  //     loadedStyle[compKey].mounted = false;
  //   }
  // }, [compKey]);

  // const tryMountStyle = useCallback(() => {
  //   if (loadedStyle[compKey] && !loadedStyle[compKey].mounted) {
  //     document.head.appendChild(loadedStyle[compKey].el);
  //   }
  // }, [compKey]);
  const Comp = useMemo(() => {
    // 如果已经有组件在window上的话，就不需要加载了，直接用就行了
    return getComp(componentName)?.Component || data?.Component;
  }, [data, componentName]);
  const el = Comp && children?.(Comp);
  useEffect(() => {
    // tryMountStyle();
    // 如果已经有组件在window上的话，就不需要加载了，直接用就行了
    if (!getComp(componentName)) {
      run();
    }
    // return () => {
    //   unMountStyle();
    // };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <ErrorBoundary fallback={<ErrorBlock message="渲染组件异常" />}>
      {loading ? (
        <div
          style={{
            padding: '12px 0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Spin />
        </div>
      ) : error ? (
        <ErrorBlock message="加载组件异常" />
      ) : (
        el
      )}
    </ErrorBoundary>
  );
};
