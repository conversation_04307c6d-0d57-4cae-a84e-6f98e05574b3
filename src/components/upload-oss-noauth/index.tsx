import React from 'react';
import { Upload, message } from 'antd';
import type { UploadProps, UploadFile } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import ossUpload from '@/utils/oss-upload';

interface UploadOssNoAuthProps {
  value?: string[];
  onChange?: (urls: string[], fileKeys?: string[]) => void;
  maxSizeMB?: number; // 单张图片最大MB，默认20
  maxCount?: number; // 最大数量，默认5
  accept?: string; // 默认 'image/png,image/jpeg,image/jpg'
  style?: React.CSSProperties;
  uploadText?: string; // 上传按钮文案
  tipText?: string; // 提示文案
  fileKeys?: string[]; // 新增 fileKeys
}

const UploadOssNoAuth: React.FC<UploadOssNoAuthProps> = ({
  value = [],
  onChange,
  maxSizeMB = 20,
  maxCount = 5,
  accept = 'image/png,image/jpeg,image/jpg',
  style,
  uploadText = '上传图片',
  tipText,
  fileKeys = [], // 新增 fileKeys
}) => {
  // antd Upload 受控 fileList
  const fileList: UploadFile[] = value.map((url, idx) => ({
    uid: url,
    name: url.split('/').pop() || `图片${idx + 1}`,
    status: 'done',
    url,
  }));

  // 校验格式和大小
  const beforeUpload = (file: File) => {
    const isAccept = accept.split(',').some((type) => file.type.match(type));
    if (!isAccept) {
      message.error('仅支持png/jpg/jpeg格式图片');
      return Upload.LIST_IGNORE;
    }
    const isLt = file.size / 1024 / 1024 < maxSizeMB;
    if (!isLt) {
      message.error(`图片不能超过${maxSizeMB}M`);
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  // 自定义上传
  const customRequest: UploadProps['customRequest'] = async ({ file, onSuccess, onError }) => {
    try {
      const { url, fileKey } = await ossUpload(file as File);
      const newUrls = [...value, url];
      const newFileKeys = [...fileKeys, fileKey];
      onChange?.(newUrls, newFileKeys);
      onSuccess && onSuccess({ url }, file as File);
    } catch (e: any) {
      message.error(e?.message || '上传失败');
      onError && onError(e as Error);
    }
  };

  // 删除
  const handleRemove = (file: UploadFile) => {
    const idx = value.findIndex((url) => url === file.url);
    const newList = value.filter((url) => url !== file.url);
    const newFileKeys = fileKeys.filter((_, i) => i !== idx);
    onChange?.(newList, newFileKeys);
  };

  // 上传按钮内容
  const uploadButton = (
    <div style={{ textAlign: 'center' }}>
      <PlusOutlined />
      <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>{uploadText}</div>
      {tipText && (
        <div style={{ marginTop: 4, fontSize: 11, color: '#999', lineHeight: '14px' }}>
          {tipText}
        </div>
      )}
    </div>
  );

  return (
    <Upload
      listType="picture-card"
      fileList={fileList}
      customRequest={customRequest}
      beforeUpload={beforeUpload}
      onRemove={handleRemove}
      accept={accept}
      multiple
      showUploadList
      style={style}
      maxCount={maxCount}
    >
      {fileList.length >= maxCount ? null : uploadButton}
    </Upload>
  );
};

interface UploadOssNoAuthListProps {
  titleList: string[];
  countLimit: number[];
  value?: string[][];
  onChange?: (urls: string[][], fileKeys?: string[][]) => void;
  maxSizeMB?: number;
  accept?: string;
  style?: React.CSSProperties;
  tipText?: string;
  fileKeys?: string[][];
}

const UploadOssNoAuthList: React.FC<UploadOssNoAuthListProps> = ({
  titleList,
  countLimit,
  value = [],
  onChange,
  maxSizeMB = 20,
  accept = 'image/png,image/jpeg,image/jpg',
  style,
  tipText,
  fileKeys = [],
}) => {
  // 确保value数组长度与titleList一致
  const normalizedValue = titleList.map((_, index) => value[index] || []);
  const normalizedFileKeys = titleList.map((_, index) => fileKeys[index] || []);

  const handleUploadChange = (index: number, urls: string[], nextFileKeys?: string[]) => {
    const newValue = [...normalizedValue];
    newValue[index] = urls;
    const newFileKeys = [...normalizedFileKeys];
    if (nextFileKeys) {
      newFileKeys[index] = nextFileKeys;
    }
    onChange?.(newValue, newFileKeys);
  };

  return (
    <div
      style={{
        display: 'flex',
        gap: 20,
        alignItems: 'center',
        flexWrap: 'wrap',
        ...style,
      }}
    >
      {titleList.map((title, index) => (
        <div key={index} style={{ marginBottom: 16 }}>
          <UploadOssNoAuth
            value={normalizedValue[index]}
            onChange={(urls, nextFileKeys) => handleUploadChange(index, urls, nextFileKeys)}
            maxCount={countLimit[index]}
            maxSizeMB={maxSizeMB}
            accept={accept}
            uploadText={title}
            tipText={tipText}
            fileKeys={normalizedFileKeys[index]}
          />
        </div>
      ))}
    </div>
  );
};

export default UploadOssNoAuth;
export { UploadOssNoAuthList };
