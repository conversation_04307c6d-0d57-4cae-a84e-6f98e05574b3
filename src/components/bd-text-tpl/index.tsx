import { getBdTemplateList, saveBdTemplate, editBdTemplate, sendGroupMsg } from '@/services/qw';
import { MATERIAL_SCENE_OPTIONS } from '@/common/const';
import { useMount, useRequest, useSessionStorageState, useUnmount } from 'ahooks';
import {
  Button,
  Checkbox,
  Form,
  Input,
  message,
  Modal,
  Result,
  Steps,
  Table,
  TableColumnsType,
} from 'antd';
import classNames from 'classnames';
import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import styled from 'styled-components';
import copy from 'copy-to-clipboard';
import { EmitterEventMap } from '@/utils/emitters/enum';
import emitter from '@/utils/emitters';
import { ErrorBlock } from '../component-loader';
import dayjs from 'dayjs';
import { PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';

const StyledTplTab = styled.div`
  width: 80px;
  font-size: 13px;
  background: #eee;
  text-align: center;
  line-height: 26px;
  cursor: pointer;
  &:hover:not(.active) {
    opacity: 0.8;
  }
  &.active {
    background: #fa5555;
    color: #fff;
  }
  &:not(:last-child) {
    margin-right: 8px;
  }
`;
const textAreaPrefix = '@老板，';

const adTaskLabelMap = {
  /**
   * 余额预警
   */
  BALANCE_WARNING: 'BALANCE_WARN',
  /**
   * 停投召回
   */
  STOP_EXPOSURE_RECALL: 'STOP_DROPPING',
  /**
   * 新签上线=>喜报
   */
  NEW_SIGN: 'BUSINESS_NEWS',
  /**
   * 无余额预警=>喜报
   */
  NO_BALANCE_CONTINUE: 'BUSINESS_NEWS',
};

/**
 * 过滤下hideInPicker的场景
 * 用于选择器中使用
 */
const sceneOptions = (MATERIAL_SCENE_OPTIONS || []).filter((opt) => !opt.hideInPicker);

interface IProps {
  scene?: string;
  hideScenePicker?: boolean;
  showSend?: boolean;
  sendParams?: any;
  isBatch?: boolean;
  disabled?: boolean;
  shopList?: any;
  onClose?: (hasSended?: boolean) => void;
  noCheckbox?: boolean;
  afterSend?: () => void;
}
const BdTextTpl = forwardRef((props: IProps, ref) => {
  const {
    scene: defaultScene,
    hideScenePicker,
    showSend,
    sendParams,
    isBatch,
    disabled,
    shopList,
    onClose,
    noCheckbox,
    afterSend,
  } = props;
  const [cacheTaskLabels] = useSessionStorageState('adTaskLabels', {
    defaultValue: [],
  });
  const [mapQuery, setMapQuery] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState(
    defaultScene ||
      (cacheTaskLabels.length === 1 ? adTaskLabelMap[cacheTaskLabels[0]] : 'BUSINESS_NEWS'),
  );
  const [checked, setChecked] = useState(true);
  const {
    data: tplData = [],
    refresh,
    error: tplError,
  } = useRequest(async () => {
    const res = await getBdTemplateList({
      scene: defaultScene,
    });
    const newMapQuery = {};
    MATERIAL_SCENE_OPTIONS.forEach(({ scene, content }) => {
      const apiContent = (res?.materials || []).find((item) => item?.scene === scene)?.content;
      newMapQuery[scene] = apiContent || content; // 使用接口数据或兜底模板内容
    });
    newMapQuery[defaultScene] = res?.materials?.find(
      (item) => item?.scene === defaultScene,
    )?.content;
    setMapQuery(newMapQuery);
    return res.materials || [];
  });

  const errRef = useRef(tplError);
  errRef.current = tplError;
  const [showResModal, setShowResModal] = useState<boolean>(false);
  const [modalData, setModalData] = useState([]);

  const saveTemplateFn = async () => {
    const currentTemplate = tplData.find((item) => item.scene === activeTab);
    const currentContent = currentTemplate?.content || '';
    const newContent = mapQuery[activeTab]?.trim() || '';
    if (!newContent) {
      const tip = '话术不可为空';
      message.error(tip);
      return Promise.reject(tip);
    }

    if (currentTemplate) {
      if (currentContent !== newContent) {
        await editBdTemplate({
          materialId: currentTemplate.materialId,
          scene: activeTab,
          content: newContent,
        });
        refresh();
        return { type: 'edit', materialId: currentTemplate.materialId };
      } else {
        return { type: 'noChanges', materialId: currentTemplate.materialId };
      }
    } else {
      const res = await saveBdTemplate({ scene: activeTab, content: newContent });
      refresh();
      // 这里服务端应该要给我一个materialId
      return { type: 'saved', materialId: res };
    }
  };
  const saveTemplateRef = useRef(saveTemplateFn);
  saveTemplateRef.current = saveTemplateFn;

  useImperativeHandle(ref, () => ({
    saveTemplate: saveTemplateFn,
  }));
  const checkPromise = () => {
    const { merchantNewsReachModelList } = sendParams;
    return new Promise((resolve) => {
      if (checked && merchantNewsReachModelList.length > 10) {
        message.error('喜报图片生成慢，建议选择10个以下商户进行发送，请重新选择商户。');
        return resolve(true);
      }

      Modal.confirm({
        title: '群发申请提交结果',
        icon: null,
        content: (
          <>
            <Steps size="small" current={0}>
              <Steps.Step title="提交群发申请" />
              <Steps.Step title="发送到企微群" />
            </Steps>
            <div style={{ marginTop: 10 }}>
              群发消息提交成功，确认继续发送后信息将群发至运维企微群内，发送后请关注群内商户反馈并及时回复！
            </div>
          </>
        ),
        onOk: () => resolve(false),
        onCancel: () => resolve(true),
        okText: '确认继续发送',
        cancelText: '暂不发送',
      });
    });
  };
  const { run: handleSend, loading } = useRequest(
    async () => {
      try {
        const checkBool = await checkPromise();
        if (checkBool) {
          return;
        }
        const result = await saveTemplateFn();

        const startDate = dayjs().add(-31, 'day').valueOf();
        const endDate = dayjs().add(-1, 'day').valueOf();

        const res = await sendGroupMsg({
          ...sendParams,
          scene: activeTab,
          content: mapQuery[activeTab]?.trim() || '',
          materialId: result?.materialId,
          sendMerchantNews: checked,
          startDate,
          endDate,
        });
        afterSend?.();
        if (res?.sendFailureList?.length > 0) {
          setModalData(res?.sendFailureList || []);
          setShowResModal(true);
        } else {
          onClose?.(true);
          message.success('推送成功');
        }
        return {
          materialId: result?.materialId,
        };
      } catch (error) {
        error?.message && message.error(error.message);
      }
    },
    {
      debounceLeading: true,
      debounceTrailing: false,
      debounceWait: 2000,
      manual: true,
      onSuccess: (res) => {
        traceClick(PageSPMKey.首页, ModuleSPMKey['企微任务.发送至企微群'], res || {});
      },
    },
  );

  /**
   * 批量添加的处理结果弹窗
   */
  const renderResModal = () => {
    const modalColumns: TableColumnsType<{
      pid: string;
      mainShopName: string;
      failureReason: string;
    }> = [
      {
        title: 'PID',
        dataIndex: 'pid',
      },
      {
        title: '主店名',
        dataIndex: 'mainShopName',
        render: (text: string) => text || '-',
      },
      {
        title: '失败原因',
        dataIndex: 'failureReason',
      },
    ];
    const handleCancel = () => {
      setShowResModal(false);
    };
    const isSuccess = sendParams?.merchantNewsReachModelList?.length > modalData?.length;
    const handleCopyPID = () => {
      const res = copy(modalData?.map((item) => item.pid).join(','));
      if (res) {
        message.success('复制PID成功');
        handleCancel();
      }
    };
    return (
      <Modal
        className="resModal"
        open={showResModal}
        title="群发结果"
        onCancel={handleCancel}
        maskClosable={false}
        width={550}
        onOk={handleCopyPID}
        okText="复制PID"
      >
        <Result
          style={{ padding: 0 }}
          status={isSuccess ? 'success' : 'error'}
          title={`发送${isSuccess ? '成功' : '失败'}`}
          subTitle={isSuccess ? '消息发送成功, 请关注群内商户反馈并及时回复' : ''}
        />
        <div style={{ margin: 16 }}>
          共选择{sendParams?.merchantNewsReachModelList?.length}个商户，其中
          {modalData?.length}
          个商户校验失败{isSuccess ? '，未发送' : ''}
        </div>
        <Table
          size="small"
          dataSource={modalData}
          columns={modalColumns}
          bordered
          rowKey="pid"
          scroll={{
            y: 200,
          }}
        />
      </Modal>
    );
  };
  async function updateMaterial(callback?: any) {
    if (!errRef.current) {
      const res = await saveTemplateRef.current();
      callback?.(res.materialId);
      return res;
    } else {
      const tip = '操作失败, 建议刷新页面';
      message.error(tip);
      return Promise.reject(tip);
    }
  }
  useMount(() => {
    emitter.on(EmitterEventMap.MaterialUpdate, updateMaterial);
  });
  useUnmount(() => {
    emitter.off(EmitterEventMap.MaterialUpdate);
  });
  if (tplError) {
    return <ErrorBlock message="获取话术失败" />;
  }

  return (
    <div style={{ position: 'relative' }}>
      <Input.TextArea
        rows={5}
        style={hideScenePicker ? {} : { paddingTop: 35, paddingBottom: 20 }}
        onChange={(e) => {
          const val = e.target.value.slice(textAreaPrefix.length);
          setMapQuery((prev) => ({ ...prev, [activeTab]: val }));
        }}
        value={`${textAreaPrefix}${mapQuery[activeTab] || ''}`}
      />
      {!hideScenePicker && (
        <div
          style={{
            position: 'absolute',
            top: 1,
            left: 1,
            background: '#fff',
            width: 'calc(100% - 3px)',
            padding: 6,
          }}
        >
          <div
            style={{
              display: 'flex',
              background: '#fff',
            }}
          >
            {sceneOptions.map((item) => (
              <StyledTplTab
                key={item.scene}
                className={classNames({ active: item.scene === activeTab }, 'qw-material-tab')}
                onClick={() => setActiveTab(item.scene)}
              >
                {item.label}
              </StyledTplTab>
            ))}
          </div>
        </div>
      )}
      {noCheckbox ? null : (
        <div
          style={{ display: 'flex', justifyContent: 'end', alignItems: 'center', flexWrap: 'wrap' }}
        >
          {activeTab !== 'AD_PLAN_CONFIG' && (
            <Form.Item
              name="sendReport"
              initialValue
              valuePropName="checked"
              style={{ marginBottom: 0 }}
            >
              <Checkbox
                disabled={!isBatch}
                checked={checked}
                onChange={(e) => setChecked(e.target.checked)}
              >
                发送喜报图片
              </Checkbox>
            </Form.Item>
          )}

          {shopList?.length > 1 && !isBatch ? (
            <Form.Item name="sendShopDetail" valuePropName="checked" style={{ marginBottom: 0 }}>
              <Checkbox>发送门店明细</Checkbox>
            </Form.Item>
          ) : null}
        </div>
      )}

      <p style={{ margin: '7px 0', textAlign: 'right' }}>
        {showSend && (
          <Button
            type="primary"
            onClick={handleSend}
            disabled={disabled}
            style={{ marginLeft: 15 }}
            loading={loading}
          >
            确认发送
          </Button>
        )}
      </p>
      {renderResModal()}
    </div>
  );
});

export default BdTextTpl;
