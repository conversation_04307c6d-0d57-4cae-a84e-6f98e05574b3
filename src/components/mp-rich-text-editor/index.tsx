import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './style.less';
import { Flex, Spin } from 'antd';

const MAX_TEXT_COUNT = 2000;

const formats = [
  'header',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'image',
  'color',
];

const modules = {
  toolbar: [
    [{ header: [1, 2, 3, 4, 5, 6, 7, false] }],
    [
      'bold',
      'italic',
      'underline',
      'strike',
      {
        color: [],
      },
      'blockquote',
    ],
    [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
    ['image'],
  ],
};

export default forwardRef<
  {
    onInputBlur: () => void;
  },
  {
    fromAi?: boolean;
    value: string;
    onBlur: (val: string) => void;
    styles?: React.CSSProperties;
    extraButton?: React.ReactNode;
    loading?: boolean;
  }
>((props, ref) => {
  const { value, onBlur, styles = {}, extraButton, loading, fromAi } = props;
  const [localVal, setLocalVal] = useState(value);
  const [beyondLimits, setBeyondLimits] = useState(false);

  useImperativeHandle(ref, () => ({
    onInputBlur,
  }));

  function onInputChange(content, delta, source, editor) {
    // 这个方法拿到的长度有点问题，会比实际长度多1个字
    const currentTextCount = editor.getText()?.length;
    if (currentTextCount > MAX_TEXT_COUNT + 1) {
      setBeyondLimits(true);
    } else {
      setLocalVal(content);
      setBeyondLimits(false);
    }
  }
  const onInputBlur = () => {
    onBlur && onBlur(localVal);
  };

  useEffect(() => {
    setLocalVal(value);
  }, [value]);

  return (
    <Spin spinning={!!loading} tip="AI分析中">
      <div className="mp-rich-text-editor">
        <Flex className="title" align="center">
          <img
            src="https://img.alicdn.com/imgextra/i4/O1CN01HT4TB51FdAdgpz0NN_!!6000000000509-2-tps-72-72.png"
            style={{ marginRight: 4, width: 20, height: 20 }}
          />
          AI智能分析 {extraButton || null}
        </Flex>
        <div
          style={
            fromAi
              ? {
                  position: 'relative',
                  background:
                    'linear-gradient(to bottom, rgba(240, 252, 255, 1), rgba(230, 248, 255, 0.5))',
                }
              : {}
          }
        >
          {fromAi && (
            <img
              style={{ position: 'absolute', top: 50, right: 0 }}
              src="https://img.alicdn.com/imgextra/i1/O1CN01KLrogl21sNLDRnz16_!!6000000007040-2-tps-156-142.png"
            />
          )}

          <ReactQuill
            theme="snow"
            value={localVal}
            onChange={onInputChange}
            onBlur={onInputBlur}
            formats={formats}
            modules={modules}
            style={{
              width: '100%',
              backgroundColor: fromAi ? '#ffffff52' : 'transparent',
              ...styles,
            }}
          />
        </div>
        {beyondLimits && <div className="error-text">{`最多输入${MAX_TEXT_COUNT}字`}</div>}
      </div>
    </Spin>
  );
});
