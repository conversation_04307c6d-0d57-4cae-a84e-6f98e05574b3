import React, { useRef, useState } from 'react';
import { Select, Spin } from 'antd';
import type { SelectProps } from 'antd';
import { useAntdTable } from 'ahooks';
import { getShopList } from '@/services/ai-material';
import type { IShopItem, IQueryShopListParams } from '@/types/ai-material/merchant';
import { trace } from '@/utils/trace';

interface ShopSelectV2Props {
  value?: IShopItem;
  onChange?: (shop: IShopItem | undefined) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  pid?: string; // 商户ID，用于联动筛选
  defaultValue?: string; // 默认门店 ID
}

const PAGE_SIZE = 20;

const ShopSelectV2: React.FC<ShopSelectV2Props> = ({
  value,
  onChange,
  placeholder = '请选择门店或输入完整门店id搜索',
  style,
  pid,
  defaultValue,
}) => {
  const searchRef = useRef('');
  const [list, setList] = useState<IShopItem[]>([]);
  const { loading, pagination } = useAntdTable(
    async ({ current, pageSize }) => {
      const params: IQueryShopListParams = {
        pid,
        source: 'AI_SUPPLY_TOOL',
        page: {
          pageNo: current,
          pageSize,
        },
      };
      if (searchRef.current) {
        params.shopId = searchRef.current;
      } else if (defaultValue) {
        params.shopId = defaultValue;
      }
      const res = await getShopList(params);
      const dataList = res?.dataList || [];
      const pageInfo = res?.pageInfo;
      if (current === 1) {
        setList(dataList);
      } else {
        setList((prev) => [...prev, ...dataList]);
      }
      // 默认值自动选中
      if (defaultValue && !value && current === 1) {
        const matchedShop = dataList.find((item) => item.shopId === defaultValue);
        if (matchedShop) {
          onChange?.(matchedShop);
        }
      }
      return {
        list: dataList,
        total: pageInfo?.totalCount || 0,
      };
    },
    {
      defaultPageSize: PAGE_SIZE,
      refreshDeps: [pid, defaultValue],
      debounceWait: 300,
      debounceLeading: false,
      debounceTrailing: true,
    },
  );

  // 搜索时重置分页
  const handleSearch = (val: string) => {
    searchRef.current = val;
    pagination.onChange(1, PAGE_SIZE);
  };

  // 下拉滚动加载更多
  const handlePopupScroll: React.UIEventHandler<HTMLDivElement> = (e) => {
    const target = e.target as HTMLDivElement;
    if (
      target.scrollTop + target.offsetHeight >= target.scrollHeight - 20 &&
      !loading &&
      list.length < pagination.total
    ) {
      pagination.onChange(pagination.current + 1, PAGE_SIZE);
    }
  };

  const handleChange: SelectProps<string>['onChange'] = (shopId) => {
    const shop = list.find((item) => item.shopId === shopId);
    onChange?.(shop);
    trace('shop-select-v2-change', { shopId: shop?.shopId, name: shop?.shopName });
  };

  return (
    <Select
      showSearch
      allowClear
      value={value?.shopId}
      placeholder={placeholder}
      style={style}
      loading={loading}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
      optionLabelProp="label"
      options={list.map((item) => ({
        key: item.shopId,
        value: item.shopId,
        label: item.shopName,
      }))}
      onPopupScroll={handlePopupScroll}
    />
  );
};

export default ShopSelectV2;
