import React from 'react';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { SERVICE_STATUS_ENUM } from '@/common/const';
import './index.less';

interface IProps {
  data: any;
}

export const OperationServiceHeader: React.FC<IProps> = ({ data }) => {
  const { actualService, serviceStatus, needServiceStr } = data || {};
  const completed = serviceStatus === SERVICE_STATUS_ENUM.COMPLETE;
  return (
    <div className="operation-service-header">
      <div className="header-left">
        服务任务状态：
        <span className={completed ? 'service-status-complete' : 'service-status-uncompleted'}>
          {completed ? '已完成' : '未完成'}
        </span>
      </div>
      <div className="header-right">
        服务总频次
        <Tooltip title="当前考核周期内需要服务商家的总次数（记录运维小记的次数）">
          <QuestionCircleOutlined className="frequency-icon" />
        </Tooltip>
        <div className="service-frequency">应服务：{needServiceStr || '-'}</div>
        <div className="service-frequency">本月实际服务：{actualService || '-'}次</div>
      </div>
    </div>
  );
};
