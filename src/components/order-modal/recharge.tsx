import { Spin, Form, Radio, Row, Col, Input, Flex, Button, message } from 'antd';
import ActivityTag from './components/ActivityTag';
import { getActivityList, recharge } from '@/services';
import { getFullTime } from '@/utils';
import { useRequest } from 'ahooks';
import { useTimeDown } from './components/useTimeDown';

const amountList = ['1000', '2000', '3000', '5000', '8000', '10000'];

export default function Recharge(props: { pid: string; onCancel: any }) {
  const [form] = Form.useForm();
  const { pid, onCancel } = props;
  const {
    data: actInfo,
    run,
    loading,
  } = useRequest(
    async (amount: string = form.getFieldValue('amount')) => {
      if (!pid) {
        return;
      }
      const res = await getActivityList({
        pid,
        amount: amount ? Number(amount) * 100 : undefined,
      });
      return res;
    },
    {
      refreshDeps: [pid],
    },
  );
  const { loading: recharging, run: runRecharge } = useRequest(
    async (amount: string) => {
      return recharge({ amount, pid });
    },
    {
      onSuccess: () => {
        message.success('提交成功');
      },
      onError: (err) => {
        message.error(err.message);
      },
      manual: true,
    },
  );
  const handleRecharge = () => {
    form.validateFields().then(async (values) => {
      if (!values.amount && !values._amount) {
        message.error('充值金额不能为空');
      } else {
        runRecharge(values.amount || values._amount);
      }
    });
  };
  const { endTime, name } = actInfo || {};
  const { day, hour, minute, second } = useTimeDown({
    deadline: getFullTime(endTime),
  });
  // 倒计时皆为0活动已结束 或者没有活动信息都不展示
  const activityFinished = day === 0 && hour === 0 && minute === 0 && second === 0;

  // 倒计时 结束时间存在且day小于30 展示倒计时
  const showTimeDown = endTime && day < 30;
  return (
    <Spin spinning={loading}>
      <ActivityTag
        name={name}
        dayDown={day}
        hourDown={hour}
        minuteDown={minute}
        secondDown={second}
        showTimeDown={showTimeDown}
        activityFinished={activityFinished}
      />

      <Form form={form} colon>
        <Form.Item label="充值金额" name="amount" initialValue={'1000'}>
          <Radio.Group
            optionType="button"
            buttonStyle="solid"
            onChange={(e) => {
              form.setFieldValue('_amount', '');
              run(e.target.value);
            }}
          >
            <Row gutter={[4, 12]}>
              {amountList.map((item) => (
                <Col span={8} key={item}>
                  <Radio.Button style={{ width: 100 }} value={item}>
                    {item}
                  </Radio.Button>
                </Col>
              ))}
            </Row>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="其他金额"
          name="_amount"
          extra="单位: 元"
          rules={[
            { pattern: /^\d+$/, message: '请输入整数' },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();
                const number = Number(value);
                if (number < 500 || number > 999999) {
                  return Promise.reject(new Error('请输入500~999999的整数'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input
            placeholder="请输入充值金额"
            onChange={() => {
              form.setFieldValue('amount', '');
            }}
          />
        </Form.Item>
      </Form>
      <Flex align="center" gap={8} justify="end">
        <Button
          onClick={() => {
            form.resetFields();
            onCancel();
          }}
        >
          取消
        </Button>
        <Button type="primary" onClick={handleRecharge} loading={recharging}>
          确认
        </Button>
      </Flex>
    </Spin>
  );
}
