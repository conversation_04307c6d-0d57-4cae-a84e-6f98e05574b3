import { enableAdPlan } from '@/services';
import { useRequest } from 'ahooks';
import { Form, message, Modal, Switch } from 'antd';
import { useState } from 'react';

interface IProps {
  refresh: () => void;
  shopId: string;
  orderId: string;
  disabled: boolean;
  enableTime: string;
  advertiserId: string;
  campaignId: string;
}
export default function AdSwitch(props: IProps) {
  const { refresh, shopId, orderId, disabled, enableTime, advertiserId, campaignId } = props;
  const [checked] = useState(false);
  const { runAsync: runEnableAdPlan } = useRequest(
    async () => {
      return enableAdPlan({ shopId, orderId, advertiserId, campaignId });
    },
    {
      manual: true,
      onSuccess: () => {
        message.success('广告计划开启成功');
      },
    },
  );
  function enableAd(val: boolean) {
    if (val) {
      Modal.confirm({
        title: '确认开启广告新手加速计划吗?',
        onOk: async () => {
          await runEnableAdPlan();
          refresh();
        },
      });
    }
  }
  return (
    <div>
      <Form>
        <Form.Item
          label={disabled ? '广告新手加速计划开启时间' : '广告新手加速计划'}
          style={{ marginBottom: 8 }}
          colon
        >
          {disabled && enableTime ? (
            <div style={{ fontSize: 13 }}>{enableTime}</div>
          ) : (
            <Switch
              checked={checked}
              unCheckedChildren="关闭"
              checkedChildren="开启"
              onChange={enableAd}
            />
          )}
        </Form.Item>
        <div style={{ fontSize: 13 }}>
          {/* 新签广告主签约产品中包含广告推广金, 门店商家分达到 3 分时, 可以开启广告新手加速计划 */}
          新签广告主签约产品中包含广告推广金，门店基建任务完成状态为「已完成」,可以开启广告新手加速计划
        </div>
      </Form>
    </div>
  );
}
