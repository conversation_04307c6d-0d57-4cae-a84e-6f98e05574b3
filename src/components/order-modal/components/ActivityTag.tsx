import React from 'react';
import { Row, Tag } from 'antd';

const ActivityTag = ({
  // origin,
  // addition,
  // name,
  dayDown,
  hourDown,
  minuteDown,
  secondDown,
  showTimeDown,
  activityFinished,
  name,
}: {
  // origin: string | undefined; // 原价
  // addition: string | undefined; // 优惠
  // name: string | undefined; // 活动名称
  dayDown: number | undefined; // 活动倒计时day
  hourDown: number | undefined; // 活动倒计时hour
  minuteDown: number | undefined; // 活动倒计时minute
  secondDown: number | undefined; // 活动倒计时second
  showTimeDown: boolean | any; // 是否展示倒计时
  activityFinished: boolean | undefined; // 活动是否已结束 true已结束不展示
  name: string;
}) => {
  return (
    !activityFinished && (
      <Row style={{ marginBottom: 10, borderRadius: '4px' }}>
        {name ? <Tag color="#f50">{name}</Tag> : null}
        {showTimeDown ? (
          <span style={{ color: '#f50' }}>
            {dayDown}天{hourDown}小时{minuteDown}分{secondDown}秒后活动结束
          </span>
        ) : null}
      </Row>
    )
  );
};

export default ActivityTag;
