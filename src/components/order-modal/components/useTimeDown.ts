import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';

dayjs.extend(isSameOrAfter);

export type Remains = Record<'day' | 'hour' | 'minute' | 'second', number>;

export const useTimeDown = ({
  deadline,
  format = 'YYYY-MM-DD HH:mm:ss',
}: {
  deadline: string | undefined; // 活动结束时间
  format?: 'YYYY-MM-DD HH:mm:ss' | string; // 时间格式化方法
}) => {
  // 由于 dayjs() 返回对象，setCurrent 修改值后指针不变，无法在 useEffect 中捕获变化，所以这里定义了一个 updater 用于 useEffect 捕获时间更新
  const [{ current, updater }, setCurrent] = useState({
    current: dayjs(),
    updater: 0,
  });
  const [remains, setRemains] = useState<Remains>({
    day: 0,
    hour: 0,
    minute: 0,
    second: 0,
  });
  useEffect(() => {
    if (!deadline) return;
    const timer = window.setInterval(() => {
      current.isSameOrAfter(dayjs(deadline, format))
        ? clearInterval(timer)
        : setCurrent((prev) => ({
            current: prev.current.add(1, 's'),
            updater: prev.updater + 1,
          }));
    }, 1000);
    return () => clearInterval(timer);
  }, [deadline]);

  // current 变化，计算相差多长时间
  useEffect(() => {
    if (!deadline) return;
    let millisec = dayjs(deadline, format).valueOf() - current.valueOf();
    // 处理 millisec 可能为负数的情况
    millisec = millisec >= 0 ? millisec : 0;
    // 用毫秒数得到秒、分、小时和天
    const days = Math.floor(millisec / (1000 * 60 * 60 * 24));
    const hours = Math.floor(millisec / (1000 * 60 * 60) - 24 * days);
    const minutes = Math.floor(millisec / (1000 * 60) - 24 * 60 * days - 60 * hours);
    const seconds = Math.floor(
      millisec / 1000 - 24 * 60 * 60 * days - 60 * 60 * hours - 60 * minutes,
    );

    setRemains({
      day: days,
      hour: hours,
      minute: minutes,
      second: seconds,
    });
  }, [updater]);

  return remains;
};
