import { getRechargeHistory } from '@/services';
import { useAntdTable } from 'ahooks';
import { Spin, Timeline } from 'antd';

interface IProps {
  pid: string;
}
export default function RechargeHistory(props: IProps) {
  const { data, loading, error } = useAntdTable(
    async ({ current }) => {
      const res = await getRechargeHistory({
        merchantId: props.pid,
        page: {
          pageSize: 50,
          pageNo: current,
        },
      });
      return {
        total: res?.pageInfo?.totalCount || 0,
        list: res?.dataList || [],
      };
    },
    {
      refreshDeps: [props.pid],
    },
  );
  const purchaseRecord = data?.list || [];
  return (
    <Spin size="small" spinning={loading}>
      {!error && purchaseRecord && purchaseRecord?.length > 0 ? (
        <Timeline>
          {purchaseRecord.map((item, index) => (
            <Timeline.Item key={index}>
              {item.submitTime} {item.displayText}
            </Timeline.Item>
          ))}
        </Timeline>
      ) : (
        <div className="nullBox">暂无数据</div>
      )}
    </Spin>
  );
}
