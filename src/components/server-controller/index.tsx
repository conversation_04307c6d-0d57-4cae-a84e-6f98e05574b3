import { ActionButtonType } from '@/constants';
import { IAction } from '@/types';
import { useAction } from './useAction';

export default function ServerController<T>(buttonType: ActionButtonType) {
  return (Component: React.FC<any>) => {
    return (props: T) => {
      const button = useAction(buttonType);
      if (!button.showButton) {
        return null;
      }
      return <Component {...button} {...props} />;
    };
  };
}

// 支持传入一个参数，结果是并集
export type IPropsWithController<T = unknown> = T & Partial<IAction>;
