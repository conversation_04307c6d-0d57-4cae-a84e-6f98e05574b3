import { isAmapAgent } from '@/common/utils';
import { ActionButtonType } from '@/constants';
import { useStore } from '@/context/global-store';
import { IAction } from '@/types';
import { isFunction } from 'radash';
import React from 'react';

export function useAction(but: ActionButtonType) {
  if (!but) {
    return {} as IAction;
  }
  const { actionMap } = useStore();
  return (actionMap?.get(but) || {}) as IAction;
}
type IFuncChildren = (props: IAction) => React.JSX.Element;
type IChildren = React.JSX.Element | IFuncChildren;
interface IProps {
  button?: IAction | any;
  ext?: boolean;
  children?: IChildren;
  buttonType?: ActionButtonType;
  disabled?: boolean;
  hideIn31?: boolean;
}
export function IfButtonShow(props: IProps): React.JSX.Element {
  const { button, ext = true, buttonType, disabled, hideIn31 } = props;
  const _button = useAction(buttonType);
  const isFunc = isFunction(props.children);
  const children = (isFunc ? props.children : () => props.children) as IFuncChildren;
  if (hideIn31 && isAmapAgent()) {
    return null;
  }
  if (disabled) {
    // @ts-ignore force type
    return ext ? children({}) : null;
  }
  const show = ext && (!!button?.showButton || !!_button?.showButton);
  if (!show) {
    return null;
  }
  return children(button || _button);
}
