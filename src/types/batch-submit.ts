// 门店信息接口（运维工单门店信息）
export interface IShopInfo {
  shopId: string; // 门店ID
  shopName: string; // 门店名称
  pid: string; // 商户ID
  lastSubmitTime?: string; // 最近一次提报时间
  collectShopName?: string; // 装修素材提报记录中的目标门店名称
  address?: string; // 门店地址
  shopCity?: string; // 门店城市
  shopQualityScore?: string; // 门店质量分数
  // 新增字段
  hasHistoricalReport?: boolean; // 历史是否提报
  isHistoricalOrderApproved?: boolean; // 历史工单是否过审
  isMerchantScoreQualified?: boolean; // 商家分是否达标
  canSelect?: boolean; // 是否可选择（运维关系控制）
}

// 批量提报状态枚举
export enum BatchSubmitStep {
  SHOP_SELECT = 1, // 门店选择步骤
  SUBMIT_REVIEW = 2, // 提报审核步骤
}

// 目标门店设置类型
export interface ITargetShopSetting {
  shopId: string; // 门店ID
  targetShopName?: string; // 目标门店名称，为空表示无目标门店
  hasSubmitRecord?: boolean; // 是否有装修素材提报记录
  isNoTarget?: boolean; // 是否勾选无目标门店
}

// 批量提报弹窗状态
export interface IBatchSubmitModalState {
  currentStep: BatchSubmitStep; // 当前步骤
  selectedShops: IShopInfo[]; // 已选门店列表
  targetShopSettings: ITargetShopSetting[]; // 目标门店设置
  isModalVisible: boolean; // 弹窗是否可见
}

// 门店选择组件回调函数类型
export interface IShopSelectorCallbacks {
  onShopsSelected: (shops: IShopInfo[]) => void; // 门店选择回调
  onCancel: () => void; // 取消回调
  onNext: () => void; // 下一步回调
}

// 提报审核组件回调函数类型
export interface ISubmitReviewCallbacks {
  onTargetShopChange: (shopId: string, targetShopName?: string, isNoTarget?: boolean) => void; // 目标门店设置变更
  onCancel: () => void; // 取消回调
  onSubmit: (settings: ITargetShopSetting[]) => void; // 提交回调
}

// 门店列表查询参数（运维工单门店信息查询）
export interface IBatchSubmitShopListParams {
  pid?: string; // 商户ID
  shopName?: string; // 门店名称
  shopId?: string; // 门店ID
  hasHistoricalReport?: boolean; // 历史是否提报
  isHistoricalOrderApproved?: boolean; // 历史工单是否过审
  isMerchantScoreQualified?: boolean; // 商家分是否达标
  lastSubmitStartTime?: string; // 最近一次提报开始时间
  lastSubmitEndTime?: string; // 最近一次提报结束时间
  sortBy?: string; // 排序字段
  sortType?: string; // 排序类型
  page: {
    pageNo: number;
    pageSize: number;
  };
  source: string; // 来源标识，用于区分批量提报场景
  queryType?: string; // 查询类型，固定为"OptOrderShopInfo"
  includeShopTaskProcess?: boolean; // 包含门店任务进度
}

// 门店列表查询响应
export interface IBatchSubmitShopListResponse {
  dataList: IShopInfo[];
  pageInfo: {
    totalCount: number;
    pageNo: number;
    pageSize: number;
    totalPage?: number;
    hasMore?: boolean;
    nextPageNo?: number;
    currentPageNo?: number;
  };
}

// 批量提报未完结流水查询参数
export interface IQueryUnfinishedOptWoosBizOrderParams {
  // 使用通用操作员信息，具体字段由后端处理
}

// 批量提报未完结流水查询响应
export type IUnfinishedOptWoosBizOrderResponse = boolean;

// 批量提报创建请求参数
export interface IBatchSubmitCreateReq {
  shopId: string; // 门店ID
  collectShopName: string; // 目标门店名称
}

// 批量提报任务创建参数
export interface IBatchSubmitAndCreateEspOrderParams {
  createReqs: IBatchSubmitCreateReq[]; // 批量创建请求列表
}

// 批量提报任务创建响应
export interface IBatchSubmitAndCreateEspOrderResponse {}

// 装修素材提报记录查询参数
export interface IQueryShopCollectInfoParams {
  shopId: string; // 门店ID
}

// 装修素材提报记录查询响应
export interface IShopCollectInfoResponse {
  shopId: string; // 门店ID
  collectShopName?: string; // 目标门店名称
  dataTypes?: string[]; // 数据类型
  // 根据实际接口响应补充其他字段
}

// 门店筛选条件类型
export interface IShopFilterConditions {
  hasHistoricalReport?: boolean; // 历史是否提报
  isHistoricalOrderApproved?: boolean; // 历史工单是否过审
  isMerchantScoreQualified?: boolean; // 商家分是否达标
  pid?: string; // 商户ID
  shopId?: string; // 门店ID
  lastSubmitStartTime?: string; // 最近一次提报开始时间
  lastSubmitEndTime?: string; // 最近一次提报结束时间
}
