declare namespace adPlan {
  /**
   * 根据以下的data结构写TS，interface名IMerchantAdPlanConfig,将Array里面的模型抽出来
   * {
	"code": "1",
	"data": {
		"merchantName": "", // 商户名称
		"adCurrentBalance": "", // 广告余额
		"adCurrentMonthCost": "", // 广告当月消耗
		"adYestodayCost": "", // 广告昨日消耗
		"shopAdPlanConfigs": [{
			"shopId": "", // 门店id,[门店名称]需要前端根据选店组件获取
			"adPlanConfigs": [{
				"productName": "", // 投放目标（用户到店/客资线索）
				"dailyBudgetSuggestion": "", // 建议：日预算
				"offerSuggestion": "", // 建议：出价
				"intervalSuggestion": "", // 建议：投放时间
				"cycleSuggestion": "", // 建议：投放周期
				"effectPrediction": "", // 预估效果

				"dailyBudgetReality": "", // 当前：日预算
				"offerReality": "", // 当前：出价
				"intervalReality": ["09:00-11:00","13:00-14:00","18:00-20:00"], // 当前：投放时间，数组
				"cycleReality": "" // 当前：投放周期
			}]
		}]
	},
	"message": null,
	"msgCode": "SUCCESS",
	"msgInfo": "调用成功",
	"result": true,
	"success": true,
	"timestamp": "1721368871186",
	"traceId": "2150408c17213688702812982e652b",
	"version": "1.0"
}
   */
  interface IMerchantAdPlanConfig {
    merchantName: string; // 商户名称
    adCurrentBalance: string; // 广告余额
    adCurrentMonthCost: string; // 广告当月消耗
    adYestodayCost: string; // 广告昨日消耗
    shopAdPlanConfigs: IShopAdPlanConfig[];
  }
  interface IShopAdPlanConfig {
    shopId: string; // 门店id,[门店名称]需要前端根据选店组件获取
    adPlanConfigs: IAdPlanConfig[];
  }
  interface IAdPlanConfig {
    productName: string; // 投放目标（用户到店/客资线索）
    dailyBudgetSuggestion: string; // 建议：日预算
    offerSuggestion: string; // 建议：出价
    intervalSuggestion: string; // 建议：投放时间
    cycleSuggestion: string; // 建议：投放周期
    effectPrediction: string; // 预估效果
    dailyBudgetReality: string; // 当前：日预算
    offerReality: string; // 当前：出价
    intervalReality: string[]; // 当前：投放时间，数组
    cycleReality: string; // 当前：投放周期
  }
}

declare namespace adPlanFEModel {
  interface IEditColumnItemTextConfigCommon {
    name?: string;
    defaultValue?: string;
  }
  interface IEditColumnItemTextConfig {
    type: 'text';
    config?: Pick<import('antd').InputProps, 'maxLength'>;
  }

  interface IEditColumnItemTextAreaConfig {
    type: 'textarea';
    config?: Pick<import('antd/lib/input/TextArea').TextAreaProps, 'maxLength'>;
  }

  interface IEditColumnItemNumberConfig {
    type: 'number';
    config?: Pick<import('antd').InputNumberProps, 'max' | 'stringMode' | 'precision'>;
  }

  type IEditColumnItemConfig = IEditColumnItemTextConfigCommon &
    (IEditColumnItemTextConfig | IEditColumnItemNumberConfig | IEditColumnItemTextAreaConfig);

  interface IAdPlanConfigFormExtra {
    shopNameRowSpan?: number;
    productNameRowSpan?: number;
    canCreate?: IAdPlanShopProductConfig['productType'];
    formName?: Array<string | number>;
  }

  interface IAdPlanConfigItem {
    shopId: string;
    shopName: string;
    productName: string;
    productType: string;
    isSuggestion: boolean;
    dailyBudget: string | IEditColumnItemConfig; // 日预算
    offer: string | IEditColumnItemConfig; // 出价
    interval: string | IEditColumnItemConfig; // 投放时间
    cycle: string | IEditColumnItemConfig; // 投放周期
    effectPrediction?: string | IEditColumnItemConfig;
  }
  interface IAdPlanShopProductConfig {
    productName: string;
    productType: 'ARRIVE_STORE' | 'CUSTOMER_RESOURCE';
    suggestion: Omit<IAdPlanConfigItem, 'shopId' | 'shopName' | 'productName'>;
    reality: Omit<IAdPlanConfigItem, 'shopId' | 'shopName' | 'productName'>;
  }
  interface IAdPlanShopConfig {
    shopId: string;
    shopName: string;
    shopProductionConfigs: IAdPlanShopProductConfig[];
  }
}
