// AI提效工具-门店装修相关类型定义

// 通用分页信息接口
export interface IPageInfo {
  currentPageNo: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
  hasMore: boolean;
  nextPageNo: number;
}

// 商户列表查询返回值接口
export interface IMerchantListResponse {
  dataList: IMerchantItem[];
  pageInfo: IPageInfo;
}

// 1. 商户列表查询
export interface IQueryMerchantListParams {
  source?: string; // 来源，选填
  page?: {
    pageNo: number;
    pageSize: number;
  };
  pid?: string; // 商户ID，选填
  merchantName?: string; // 商户名称，选填
}

// 服务进度信息接口
export interface IServiceProgress {
  code: string; // 进度代码，如 "INCOMPLETE"
  completedTaskCnt: string; // 已完成任务数量
  name: string; // 进度名称，如 "未完成"
  taskCnt: string; // 总任务数量
  showDetail: boolean; // 是否显示详情
}

// 服务满意度信息接口
export interface IServiceSatisfaction {
  score: string; // 满意度分数
  code: string; // 满意度代码，如 "URGENT_NEED_PROMOTION"
  name: string; // 满意度名称，如 "急需提升"
}

// 操作按钮信息接口
export interface IActionButton {
  buttonName: string; // 按钮名称
  greyButton: boolean; // 是否为灰色按钮
  showButton: boolean; // 是否显示按钮
  buttonCode: string; // 按钮代码，如 "VIEW_STORES", "TO_COMPLETE" 等
}

export interface IMerchantItem {
  pid: string; // 商户ID
  merchantName?: string; // 商户名称
  hasQwGroup?: boolean; // 是否有企微群
  adCurrentBalance?: string; // 当前广告余额
  optGroupCanReach?: boolean; // 运维群是否可达
  merchantLevel?: string | null; // 商户等级
  adRechargeCount?: string; // 广告充值次数
  serviceProgress?: IServiceProgress; // 服务进度信息
  priorityTaskInfo?: any; // 优先任务信息
  mainShopName?: string; // 主门店名称
  adCurrentMonthCost?: string; // 当月广告消费
  optGroupName?: string; // 运维群名称
  showCallButton?: boolean; // 是否显示呼叫按钮
  serviceSatisfaction?: IServiceSatisfaction; // 服务满意度
  staffName?: string | null; // 员工姓名
  merchantLabels?: string[] | null; // 商户标签
  actions?: IActionButton[]; // 操作按钮列表
  infrastructStaffName?: string | null; // 基础设施员工姓名
}

// 2. 门店列表查询
export interface IQueryShopListParams {
  pid?: string; // 商户ID，选填
  source?: string; // 来源，选填
  page?: {
    pageNo: number;
    pageSize: number;
  };
  shopId?: string; // 门店ID，选填
  shopName?: string; // 门店名称，选填
}

// 基础设施任务进度接口
export interface IInfraTaskProgress {
  progressType: 'MUST' | 'OPTIONAL'; // 进度类型：必须/可选
  totalTaskCount: number; // 总任务数
  completedTaskCount: number; // 已完成任务数
}

// 按钮跳转信息接口
export interface IButtonJump {
  jumpType: string | null; // 跳转类型
  jumpTypeNew: string | null; // 新跳转类型
  buttonText: string; // 按钮文本
  buttonType: string; // 按钮类型，如 "TO_COMPLETE", "GENERATE_NEWS", "TELEPHONE_COMMUNICATION"
  greyButton: boolean; // 是否为灰色按钮
  jumpUrlList: string[] | null; // 跳转URL列表
  showButton: boolean; // 是否显示按钮
  client: string | null; // 客户端
  jumpUrl: string | null; // 跳转URL
  desc: string | null; // 描述
}

// 门店信息接口
export interface IShopItem {
  shopLabels: string[]; // 门店标签
  shopName: string; // 门店名称
  annualProductList: any | null; // 年度产品列表
  pid: string; // 商户ID
  pic: string; // 门店图片
  shangHuTongExpireTime: string | null; // 商户通过期时间
  improveInfrastructure: string; // 改善基础设施状态
  merchantName: string; // 商户名称
  shopQualityScore: string; // 门店质量分数
  staffName: string | null; // 员工姓名
  showShopBusinessNews: boolean; // 是否显示门店商业新闻
  poiId: string | null; // POI ID
  shopId: string; // 门店ID
  infrastructStaffName: string | null; // 基础设施员工姓名
  shopTaskStatus: string; // 门店任务状态，如 "INCOMPLETE"
  shopCity: string; // 门店城市
  address: string; // 门店地址
  taskExpireTime: string | null; // 任务过期时间
  payJumpList: any | null; // 支付跳转列表
  taskRemainingDays: number | null; // 任务剩余天数
  signChannel: string | null; // 签约渠道
  infraTaskProgressList: IInfraTaskProgress[]; // 基础设施任务进度列表
  infrastructTaskTotalNum: number | null; // 基础设施任务总数
  shangHuTongStatus: string; // 商户通状态
  infrastructTaskCompletedTotalNum: number | null; // 基础设施任务已完成总数
  kbShopId: string; // KB门店ID
  buttonJumpList: IButtonJump[]; // 按钮跳转列表
  shopType: string; // 门店类型，如 "shanghutong"
  shangHuTongSubStatus: string | null; // 商户通子状态
  signEffectiveTime: string | null; // 签约生效时间
}

// 门店列表查询返回值接口
export interface IShopListResponse {
  dataList: IShopItem[];
  pageInfo: IPageInfo;
}

// 3. tab 列表查询
export interface IQueryTabInfoParams {
  shopId: string; // 门店ID
}
export interface ITabInfoVO {
  moduleType: string; // 模块类型
  moduleName: string; // 模块名称
  draftCount: number; // 草稿数量
  extInfo?: {
    subAlbumTypeList?: string[]; // 子相册类型列表
    [key: string]: any; // 其他扩展字段
  };
}
export interface IQueryTabInfoResult {
  shopId: string;
  tabList: ITabInfoVO[];
}

// 4. 素材列表查询
export interface IQueryDraftListParams {
  shopId: string;
  moduleType: string;
  status?: string; // 草稿状态，可多选
  extInfo?: Record<string, any>; // 扩展字段
  subAlbumType?: string; // 子相册类型
}
export const StatusMap = {
  USED: '已使用',
  UN_USED: '未使用',
};
export interface IDraftItem {
  shopId: string; // 门店Id
  moudleType: string; // 模块类型
  summary: any; // 推荐素材字段（JSON类型）
  name: string; // 名称
  image: string; // 图片链接
  tips: string; // 提醒：缺少3个必填字段
  draftId: string; // 草稿Id
  createTime: Date; // 推荐时间
  status: string; // 状态
  extInfo: {
    duplicateTag?: string; // true:线上已存在
    isMain?: string; // true:橱窗图
    [key: string]: any; // 其他扩展字段
  };
}

// 素材列表查询返回值接口
export interface IDraftListResponse {
  dataList: IDraftItem[];
  pageInfo: IPageInfo;
}

// 5. 发布明细内容批量查询（新增）
export interface IQueryPubContentListParams {
  shopId: string;
  moduleType: string;
  draftIdList: string[]; // 草稿Id list
}
export interface IPublishDraftVO {
  draftId: string;
  content: any; // 发布草稿VO内容
}
export interface IQueryPubContentListResult {
  shopId: string;
  moduleType: string;
  draftList: IPublishDraftVO[];
}

// 6. 素材批量提交接口
export interface IBatchPublishParams {
  shopId: string;
  moduleType: string;
  draftIdList: string[]; // 草稿ID列表，顺序推送
}
export interface IBatchPublishResult {
  success: boolean;
}

// 7. 素材收集配置查询（表单收集页面）
export interface IQueryCollectDraftParams {
  shopId: string; // 门店Id
  expireTimestamp: string; // 过期时间
}

// 字段类型枚举
export type FieldTypeEnum = 'string' | 'integer' | 'double' | 'array' | 'date' | 'object';

// 组件类型枚举
export type WidgetTypeEnum =
  | 'text'
  | 'image'
  | 'radio'
  | 'select'
  | 'multi_select'
  | 'checkbox'
  | 'date'
  | 'array_wrapper';

// 选项接口
export interface IOption {
  label: string;
  value: any;
}

// 隐藏条件接口
export interface IHiddenCondition {
  key: string; // 依赖字段的key
  value: any; // 依赖字段的值
  condition: 'equal' | 'not_equal' | 'greater_than' | 'less_than'; // 条件类型
}

// 表单元素模型接口
export interface IElementModel {
  key: string; // 识别key
  name: string; // 字段名称
  fieldType: FieldTypeEnum; // 字段格式
  required?: boolean; // 是否必填
  desc?: string; // 字段描述
  widget: WidgetTypeEnum; // 组件类型
  properties?: IElementModel[]; // 属性列表（仅当fieldType=object时生效）
  item?: IElementModel; // 数组元素类型（仅当fieldType=array时生效）
  rules?: {
    options?: IOption[]; // 可选内容
    maxLength?: number; // 最大字符长度
    pattern?: string; // 正则匹配规则
    titleList?: string[]; // 图片组件title list
    countLimit?: number[]; // 图片组件限制上传数量
    hidden?: IHiddenCondition; // 组件隐藏条件
    dateType?: string; // 日期格式
    accept?: string; // 文件类型限制，如 'image/*' 或 'image/png,image/jpeg'
    [key: string]: any; // 其他规则
  };
  extInfo?: Record<string, any>; // 扩展字段
}

export interface ICollectDraftDTO {
  draftNo: number; // 草稿number
  name: string; // 名称，用于素材收集页展示当前收集数据的名称，如手艺人的名字
  materialContent: IElementModel[]; // 表单字段配置列表
}

export interface ICollectDraftModule {
  moduleType: string; // 模块类型
  moduleName: string; // 模块类型名称
  collectDraftList: ICollectDraftDTO[];
}

export interface IQueryCollectDraftResult {
  shopId: string;
  shopName: string;
  collectDraftModuleList: ICollectDraftModule[];
  status: string;
  message: string;
}

// 8. 表单收集页面提交接口
export interface ISubmitDraftParams {
  shopId: string; // 门店Id
  draftNo: number; // 草稿number
  moduleType: string; // 模块类型
  materialContent: Record<string, any>; // 发布草稿VO内容
  shopName: string;
  materialName: string;
}
export interface ISubmitDraftResult {
  success: boolean;
  message?: string;
}

// 9. 草稿状态批量更新接口
export interface IBatchUpdateStatusParams {
  shopId: string;
  moduleType: string;
  draftIdList: string[];
}
export interface IBatchUpdateStatusResult {
  success: boolean;
}

// 10. 查询个人素材接口（新增）
export interface IGroupMaterialsQueryParams {
  scene?: string; // 场景类型，新增素材收集：DRAFT_CONTENT_COLLECT
}
export interface IMaterial {
  materialId: string; // 素材ID
  scene: string; // 场景类型
  content: string; // 素材内容
}
export interface IOptMaterialQueryResult {
  materials: IMaterial[]; // 素材列表
}
export interface IQueryGroupMaterialsResult {
  success: boolean; // 是否成功
  data: IOptMaterialQueryResult; // 返回数据
  errorCode?: string; // 错误码
  errorMsg?: string; // 错误信息
}

// 11. 企微素材推送接口（新增）
export interface IMerchantNewsReachModel {
  pid: string; // 商户ID
  shopId?: string; // 门店ID
  merchantNewsPageUrl?: string; // 喜报图片URL，当from=MERCHANT_NEWS时必填
  mainShopName?: string; // 主店名称
}
export interface IBatchMerchantNewReachOutParams {
  merchantNewsReachModelList: IMerchantNewsReachModel[]; // 商户触达列表
  materialId: string; // 素材ID
  from: string; // 来源场景，需符合FrontReachSceneEnum枚举
  sendMerchantNews?: boolean; // 是否发送喜报图片，默认false
  shopId?: string; // 门店Id
  draftNoList?: number[]; // 草稿列表
}
export interface ISendFailureDTO {
  pid: string; // 商户ID
  mainShopName: string; // 主店名称
  failReason: string; // 失败原因
}
export interface IBatchMerchantNewReachOutResult {
  success: boolean; // 是否成功
  data: {
    sendFailureList: ISendFailureDTO[]; // 发送失败列表
  };
  errorCode?: string; // 错误码
  errorMsg?: string; // 错误信息
}

// 素材内容收集推送（企微群）接口
export interface IInfraContentCollectReachOutParams {
  merchantId: string;
  materialId: string;
  shopId: string;
  draftNoList: number[];
}
export interface IInfraContentCollectReachOutResult {
  success: boolean;
  data: null;
}
