// OSS 相关接口类型定义

export interface IGetOssSignParams {
  contentType?: string; // 默认为 image
}
export interface IGetOssSignResult {
  accessKeyId: string;
  bucket: string;
  accessKeySecret: string;
  endpoint: string;
  stsToken: string;
  dir: string;
}

export interface IGetOssUrlParams {
  accountID: string;
  service: string;
  func: string;
  event: {
    ossConfigId: string;
    bucketName: string;
    filePath: string;
  };
  qualifier: 'policyHsfService-getPolicyUrlMse__stable';
}

export interface IGetOssUrlResult {
  url: string;
}

export interface UploadOSSOptions {
  host: string;
  ossSign: {
    policy: string;
    accessKeyId: string;
    signature: string;
  };
  fileKey: string;
  file: File;
}

export interface IDomainChangeParams {
  url: string;
  isOriginalPic?: boolean;
}
