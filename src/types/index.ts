import { ActionButtonType } from '@/constants';

export interface IAction {
  jumpType: string;
  jumpTypeNew: string;
  buttonText: string;
  buttonType: ActionButtonType;
  greyButton: boolean;
  jumpUrlList: string;
  showButton: boolean;
  client: string;
  jumpUrl: string;
  desc?: string;
}
export interface IActivity {
  activityId: string;
  originRechargeFee: string;
  additionalFee: string;
  expireDay: string;
  endTime: string;
  name: string;
}
