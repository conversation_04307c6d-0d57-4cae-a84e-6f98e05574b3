export enum ActionScene {
  切换视角 = 'VIEW_OPERATOR_BUTTON',
  发送企微触达 = 'SEND_WECHAT_BUTTON',
  通用区块 = 'INFRASTRUCTURE_OPERATOR_VIEW',
}

export const legacyActionScene = [ActionScene.切换视角, ActionScene.发送企微触达];

export enum ActionButtonType {
  商家分 = 'merchantScore',
  广告任务 = 'adTask',
  预警任务 = 'warningTask',
  续签任务 = 'renewTask',
  基建任务 = 'infrastructureTask',
  基建运维关系 = 'infrastructureMaintenanceRelation',
  绩效区块 = 'kpi',
  柱状图区块 = 'barChat',
  今日必做 = 'todayTodo',
  超时任务区块 = 'timeOutTask',
  电话外呼复盘数据tab = 'callReviewDataTab',
  门店任务详情广告任务tab = 'taskDetailAdTab',
  门店任务详情续签任务tab = 'taskDetailRenewTab',
  一键发送企微群 = 'sendWeiXin',
  仅查看有群商户 = 'viewOnlyGroupShop',
  商户多选 = 'multipleButton',
  切换视角 = 'viewOperatorButton',
  去完成 = 'TO_COMPLETE',
  生成喜报 = 'GENERATE_NEWS',
  ESP工单 = 'OPT_ESP_ORDER_CREATE_GREY',
}

export const sceneMap = {
  [ActionScene.切换视角]: [ActionButtonType.切换视角],
  [ActionScene.发送企微触达]: [
    ActionButtonType.一键发送企微群,
    ActionButtonType.商户多选,
    ActionButtonType.仅查看有群商户,
  ],
};

export enum DemotionSceneEnum {
  商户列表 = 'MERCHANT_LIST',
  门店列表 = 'SHOP_LIST',
}
