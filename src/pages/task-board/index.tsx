import React, { useState } from 'react';
import { Card, ConfigProvider, Table, TableProps } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import { Header } from '@/components/header';
import './index.less';
import { TASK_BOARD } from '@/common/const';
import { definePageConfig } from 'ice';
import { useMount } from 'ahooks';

export default () => {
  const [dataSource, setDataSource] = useState([]);

  const columns: TableProps['columns'] = [
    {
      title: '入口类别',
      dataIndex: 'entryType',
      key: 'entryType',
      width: 500,
      onCell: (row: any) => ({ rowSpan: row.rowSpan || 0 }),
    },
    {
      title: '入口名称',
      dataIndex: 'entryName',
      key: 'entryName',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 240,
      render: (_, record) => (
        <a
          onClick={() => {
            window.open(record.operation);
          }}
        >
          查看
        </a>
      ),
    },
  ];

  useMount(() => {
    filterDataSource(TASK_BOARD);
  });

  const handleData = (data: any, key: string): any => {
    let startItem = data[0];
    startItem.rowSpan = 1;
    data.forEach((item, index: number) => {
      const nextItem = data[index + 1] || {};
      if (item[key] === nextItem[key]) {
        startItem.rowSpan++;
      } else {
        startItem = nextItem;
        startItem.rowSpan = 1;
      }
    });
    return data;
  };

  const filterDataSource = (data) => {
    const newDataSource = [];
    data.forEach((section) => {
      section.child.forEach((child) => {
        const newEntry = {
          entryType: section.title,
          entryName: child.title,
          operation: child.url,
        };
        newDataSource.push(newEntry);
      });
    });
    setDataSource(handleData(newDataSource, 'entryType'));
  };

  return (
    <ConfigProvider locale={zhCN}>
      <div className="task-board">
        <Header title="数据看板" />
        <Card style={{ marginTop: 16 }}>
          <Table columns={columns} dataSource={dataSource} />
        </Card>
      </div>
    </ConfigProvider>
  );
};

export const spmConfig = definePageConfig(() => ({
  title: '看板统一入口',
  spm: {
    spmB: 'xy-task-pc-board',
  },
}));
