import React, { useState } from 'react';
import { useRequest } from 'ahooks';
import { useQuery } from '@/hooks/useQuery';
import { queryCollectDraft, submitDraft } from '@/services/ai-material';
import type {
  IQueryCollectDraftResult,
  ISubmitDraftParams,
  IElementModel,
  ICollectDraftDTO,
} from '@/types/ai-material/merchant';
import { Button, Tooltip, message, Form, Radio, Tabs } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import DynamicItem from './components/dynamic-item';
import { trace } from '@/utils/trace';
import styled from 'styled-components';

const StyledForm = styled(Form)`
  .ant-form-item-label label {
    min-height: 32px;
    height: auto !important;
  }
`;

const AiMaterialCollect: React.FC = () => {
  // 获取参数
  const [query] = useQuery();
  const { shopId: urlShopId, expireTimestamp } = query;
  const [formValue, setFormValue] = useState({});

  // 拉取表单配置
  const { data, loading, refresh } = useRequest<IQueryCollectDraftResult, []>(
    () =>
      urlShopId
        ? queryCollectDraft({ shopId: urlShopId, expireTimestamp })
        : Promise.resolve(undefined),
    {
      onSuccess: (res) => {
        if (res.status !== 'SUCCESS') {
          message.warning(res.message);
        }
      },
    },
  );

  // 当前tab
  const [activeTab, setActiveTab] = useState(0);
  const [activeDraftIndex, setActiveDraftIndex] = useState(0);
  const [form] = Form.useForm();

  // 使用新的数据结构
  const modules = data?.collectDraftModuleList || [];
  const shopName = data?.shopName || '';
  const currentModule = modules[activeTab];
  const currentDraftList: ICollectDraftDTO[] = currentModule?.collectDraftList || [];
  const currentDraft: ICollectDraftDTO | undefined = currentDraftList[activeDraftIndex];
  const formItems: IElementModel[] = currentDraft?.materialContent || [];

  // 提交
  const { run: runSubmitDraft, loading: submitLoading } = useRequest(
    (params: ISubmitDraftParams) => submitDraft(params),
    {
      manual: true,
      onSuccess: () => {
        message.success('提交成功');
        form.resetFields();
        refresh();
      },
    },
  );

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (!currentModule || !currentDraft) {
        message.error('当前模块无表单项');
        return;
      }

      // 调用submitDraft接口，使用新的参数结构
      runSubmitDraft({
        shopId: data?.shopId,
        draftNo: currentDraft.draftNo,
        moduleType: currentModule.moduleType,
        materialContent: values,
        shopName,
        materialName: currentDraft?.name,
      });
    } catch (e) {
      // 校验失败自动提示
    }
  };

  // 渲染表单项的递归函数
  const renderFormItems = (items: IElementModel[], prefix = '') => {
    return items.map((item) => {
      const fieldName = prefix ? `${prefix}.${item.key}` : item.key;
      const widget = item.widget.toLowerCase();

      // 处理隐藏逻辑
      const isHidden =
        item.rules?.hidden && form.getFieldValue(item.rules.hidden.key) === item.rules.hidden.value;

      if (isHidden) {
        return null;
      }

      return (
        <Form.Item
          // @ts-ignore 这是用来触发重新渲染而存在
          formValue={formValue}
          key={fieldName}
          label={
            <>
              {item.name}
              {item.desc && (
                <Tooltip title={item.desc} trigger={['click']}>
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              )}
            </>
          }
          name={fieldName}
          rules={[
            ...(item.required ? [{ required: true, message: `${item.name}为必填项` }] : []),
            ...(item.rules?.maxLength
              ? [{ max: item.rules.maxLength, message: `最多输入${item.rules.maxLength}个字符` }]
              : []),
            ...(item.rules?.pattern
              ? [{ pattern: new RegExp(item.rules.pattern), message: '格式不正确' }]
              : []),
            ...(widget === 'image'
              ? [
                  {
                    validator: (_rule, value) => {
                      // 递归校验多维数组
                      const check = (val) => {
                        if (
                          item.rules?.titleList?.length &&
                          val?.length === item.rules?.titleList?.length &&
                          val.every((v) => v?.length)
                        ) {
                          return true;
                        }

                        return false;
                      };
                      if (!check(value)) {
                        return Promise.reject(new Error('每一组图片都至少上传一张'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]
              : []),
          ]}
        >
          <DynamicItem
            elementModel={item}
            onChange={(value) => {
              // 当依赖字段变化时，重新渲染表单
              if (items.some((i) => i.rules?.hidden?.key === item.key)) {
                form.setFieldsValue({ [fieldName]: value });
              }
            }}
          />
        </Form.Item>
      );
    });
  };

  return (
    <div style={{ padding: 32 }}>
      <div style={{ textAlign: 'center' }}>
        <div style={{ fontSize: 20, fontWeight: 500, marginBottom: 10 }}>门店装修素材收集</div>
        <div style={{ fontSize: 16 }}>{shopName}</div>
      </div>
      {/* tab 切换 */}
      <div style={{ marginBottom: 16 }}>
        <Tabs
          activeKey={String(activeTab)}
          onChange={(key) => {
            const idx = Number(key);
            setActiveTab(idx);
            setActiveDraftIndex(0);
          }}
          items={modules.map((m, idx) => ({
            key: String(idx),
            label: m.moduleName,
          }))}
        />
      </div>
      {/* draft 切换 */}
      {currentDraftList.length > 0 && (
        <Radio.Group
          value={activeDraftIndex}
          onChange={(e) => setActiveDraftIndex(e.target.value)}
          optionType="button"
          buttonStyle="solid"
          style={{ marginBottom: 16 }}
        >
          {currentDraftList.map((draft, idx) => (
            <Radio.Button key={draft.draftNo} value={idx}>
              {draft.name}
            </Radio.Button>
          ))}
        </Radio.Group>
      )}
      {/* 动态表单 */}
      {loading ? (
        <div>加载中...</div>
      ) : (
        <StyledForm form={form} onValuesChange={setFormValue}>
          {formItems.length === 0 && <div>暂无表单项</div>}
          {renderFormItems(formItems)}
        </StyledForm>
      )}
      {/* 温馨提示 */}
      <div style={{ margin: '32px 0', color: '#888' }}>
        温馨提示:
        您为门店合法经营者对于以上信息有权进行编辑并保证所上传的信息真实、合法、有效。您点击提交后此信息会同步到运维小二并协助您完成对应信息的配置，若您后续需要进行修改，可以联系运维小二或通过阿里本地通后台进行修改
      </div>
      {/* 提交按钮 */}
      {shopName && (
        <Button
          type="primary"
          style={{ width: '100%' }}
          onClick={handleSubmit}
          loading={submitLoading}
        >
          提交
        </Button>
      )}
    </div>
  );
};

export default AiMaterialCollect;
