import React, { useState } from 'react';
import { Input, Select, Radio, Checkbox, DatePicker, Form, Button } from 'antd';
import { UploadOssNoAuthList } from '@/components/upload-oss-noauth';
import type { IElementModel } from '@/types/ai-material/merchant';

interface DynamicItemProps {
  elementModel: IElementModel;
  value?: any;
  onChange?: (v: any) => void;
  style?: React.CSSProperties;
}

const DynamicItem: React.FC<DynamicItemProps> = ({ elementModel, value, onChange, style }) => {
  const { widget, rules } = elementModel;
  const lowerCaseWidget = widget.toLowerCase();

  // 处理不同的组件类型
  switch (lowerCaseWidget) {
    case 'select':
      return (
        <Select
          value={value}
          onChange={onChange}
          options={rules?.options}
          placeholder={`请选择${elementModel.name}`}
          style={style}
        />
      );

    case 'multi_select':
      return (
        <Select
          mode="multiple"
          value={value}
          onChange={onChange}
          options={rules?.options}
          placeholder={`请选择${elementModel.name}`}
          style={style}
        />
      );

    case 'radio':
      return (
        <Radio.Group
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          options={rules?.options}
          style={style}
        />
      );

    case 'checkbox':
      return (
        <Checkbox.Group value={value} onChange={onChange} options={rules?.options} style={style} />
      );

    case 'date': {
      const dateFormat = rules?.dateType || 'YYYY-MM-DD';
      const showTime = dateFormat.includes('HH:mm:ss');

      return (
        <DatePicker
          value={value}
          onChange={onChange}
          format={dateFormat}
          showTime={showTime}
          placeholder={`请选择${elementModel.name}`}
          style={{
            minWidth: 180,
            ...style,
          }}
        />
      );
    }

    case 'image': {
      return (
        <UploadOssNoAuthList
          titleList={rules.titleList}
          countLimit={rules.countLimit}
          value={value}
          onChange={(urls) => {
            onChange?.(urls);
          }}
          accept={rules?.accept || 'image/*'}
          style={style}
        />
      );
    }

    case 'array_wrapper': {
      // 数组包装器，用于处理复杂的数组类型
      if (elementModel.item) {
        const arrayValue = value || [];

        const handleArrayChange = (index: number, itemValue: any) => {
          const newArray = [...arrayValue];
          newArray[index] = itemValue;
          onChange?.(newArray);
        };

        const addItem = () => {
          onChange?.([...arrayValue, {}]);
        };

        const removeItem = (index: number) => {
          const newArray = arrayValue.filter((_: any, i: number) => i !== index);
          onChange?.(newArray);
        };

        return (
          <div style={style}>
            {arrayValue.map((item: any, index: number) => (
              <div
                key={index}
                style={{
                  marginBottom: 16,
                  padding: 16,
                  border: '1px solid #d9d9d9',
                  borderRadius: 4,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: 8,
                  }}
                >
                  <span>第 {index + 1} 项</span>
                  <Button
                    type="link"
                    danger
                    onClick={() => removeItem(index)}
                    style={{ padding: 0 }}
                  >
                    删除
                  </Button>
                </div>

                {/* 递归渲染数组项的字段 */}
                {elementModel.item.properties?.map((property) => (
                  <Form.Item key={property.key} label={property.name} style={{ marginBottom: 12 }}>
                    <DynamicItem
                      elementModel={property}
                      value={item[property.key]}
                      onChange={(val) => {
                        const newItem = { ...item, [property.key]: val };
                        handleArrayChange(index, newItem);
                      }}
                    />
                  </Form.Item>
                )) || (
                  <DynamicItem
                    elementModel={elementModel.item}
                    value={item}
                    onChange={(val) => handleArrayChange(index, val)}
                  />
                )}
              </div>
            ))}

            <Button
              onClick={addItem}
              style={{
                width: '100%',
                padding: '8px 16px',
                border: '1px dashed #d9d9d9',
                background: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                color: '#1890ff',
              }}
            >
              + 添加{elementModel.name}
            </Button>
          </div>
        );
      }
      return null;
    }

    default:
      // 默认使用文本输入
      return (
        <Input
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={elementModel.desc}
          maxLength={rules?.maxLength}
          style={style}
        />
      );
  }
};

export default DynamicItem;
