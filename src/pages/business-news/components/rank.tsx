import { IconBangDan, IconGold<PERSON>, IconSlivery } from '@/components/icons';
import DataCard from './card-list/card';
import { useDataState } from './store/useDataState';
import { useRequest, useUnmount } from 'ahooks';
import { Divider, Flex } from 'antd';
import { useState } from 'react';
import RedSelect from './common/red-select';
import styled from 'styled-components';
import classNames from 'classnames';
import { DataDisplay } from './data-display';
import { queryData } from '@/services';
import { formatterTargetValue } from './utils';

const RowWrapper = styled.div`
  padding: 0.6em 1.2em;
  border-radius: 8px;
  display: flex;
  align-items: center;
  background: linear-gradient(270deg, rgba(246, 247, 248, 0) 1%, #f6f7f8 100%);
  margin-bottom: 10px;
  &.rank-title {
    background: #1a66ff1a;
    color: #1a66ff;
    font-weight: 500;
  }
  &.my-shop {
    background: linear-gradient(270deg, rgba(246, 247, 248, 0) 1%, #fffbf3 100%);
    color: #663400;
  }
`;

export default function Rank(props: { data: any }) {
  const { data } = props;
  const {
    dateRange,
    shopIdList,
    shopName,
    isShow,
    registerField,
    shopList,
    unregisterField,
    isChild,
  } = useDataState();
  const [selectedRankList, setSelectedRankList] = useState<string[]>([]);
  const shownFields = isShow([
    'comment_cnt_std',
    'ngtv_comment_cnt_std',
    'optimal_comment_cnt_std',
  ]);
  const dataItems = [
    {
      title: '累计评论数',
      key: 'comment_cnt_std',
      tip: '该门店的所有高德的评论数',
    },
    {
      title: '累计差评数',
      key: 'ngtv_comment_cnt_std',
      tip: '该门店所有定义为差评（评分为1、2分）的评论数',
    },
    {
      title: '累计优质好评数',
      key: 'optimal_comment_cnt_std',
      tip: '该门店的所有打了优质标签的评论数',
    },
  ];
  const { data: rankList = [] } = useRequest(
    async () => {
      const startDate = dateRange[0];
      const endDate = dateRange[1];
      if (!startDate || !endDate || !shopIdList?.length) {
        return [];
      }
      const res = await queryData(
        {
          shopIdList,
          startDate,
          endDate,
        },
        'xibao_year_report_rank_application',
      );
      const list: Array<{
        toplist_title: string;
        rank_list: Array<{
          shop_name: string;
          rank_num: number;
          right_label: string;
        }>;
      }> = res?.applicationDataList?.[0]?.ruleDataList?.[0]?.values || [];
      setSelectedRankList(list.map((item) => item.toplist_title));
      return list;
    },
    {
      refreshDeps: [dateRange, shopList],
      onSuccess(list = []) {
        registerField({
          label: '内容互动',
          key: 'content_interaction',
          defaultHidden: true,
          children: [
            {
              key: 'content_interaction_title',
              title: '整体模块',
              defaultHidden: true,
            },
            {
              key: 'rank',
              render: () => {
                return (
                  <Flex align="center" style={{ marginBottom: 8 }}>
                    <span style={{ marginRight: 8 }}>评入榜单</span>
                    <RedSelect
                      allowClear
                      maxTagCount="responsive"
                      mode="multiple"
                      defaultValue={list.map((item) => item.toplist_title)}
                      style={{ flex: 1 }}
                      placeholder="评入榜单"
                      onChange={setSelectedRankList}
                      options={list.map((item) => ({
                        label: item.toplist_title,
                        value: item.toplist_title,
                      }))}
                    />
                  </Flex>
                );
              },
            },
            ...dataItems,
          ],
        });
      },
    },
  );
  useUnmount(() => {
    unregisterField('content_interaction');
  });
  const isAllHidden = !isShow('content_interaction_title');
  const filterDataItems = dataItems.filter((item, index) => shownFields[index]);
  if (!isChild || isAllHidden || (!selectedRankList.length && !shownFields.some(Boolean))) {
    return null;
  }
  return (
    <DataCard data={{ label: '内容互动' }}>
      <div style={{ padding: '0 1.2em' }}>
        {selectedRankList.map((rankName) => {
          const rank = rankList.find((item) => item.toplist_title === rankName);
          const title = rank?.toplist_title;
          return (
            <div style={{ marginBottom: '1.2em' }}>
              <RowWrapper className="rank-title">
                <IconBangDan style={{ marginRight: 6, fontSize: '1.8em' }} />
                <div style={{ fontSize: '1.4em', fontWeight: 500 }}>{title}</div>
              </RowWrapper>
              {(rank?.rank_list || []).map((shop) => (
                <RowWrapper
                  className={classNames({
                    'my-shop': shop.shop_name === shopName,
                  })}
                >
                  <div
                    style={{
                      minWidth: 18,
                      // textAlign: 'center',
                      marginRight: 6,
                      fontWeight: 500,
                      color: shop.rank_num === 1 ? '#FF4B4B' : '',
                      marginLeft: 3,
                      flexShrink: 0,
                    }}
                  >
                    {shop.rank_num}
                  </div>
                  <div style={{ fontSize: '1.4em' }}>{shop.shop_name}</div>
                  {shop.right_label && (
                    <Flex align="center" style={{ marginLeft: 8, fontSize: 22, flexShrink: 0 }}>
                      {shop.right_label === 'LOW_V' ? <IconSlivery /> : <IconGoldV />}
                    </Flex>
                  )}
                </RowWrapper>
              ))}
            </div>
          );
        })}
        <DataDisplay
          items={filterDataItems.map((item) => ({
            title: item.title,
            value: formatterTargetValue(data?.[item.key]) || '-',
            key: item.key,
            tips: item.tip,
          }))}
        />
      </div>
    </DataCard>
  );
}
