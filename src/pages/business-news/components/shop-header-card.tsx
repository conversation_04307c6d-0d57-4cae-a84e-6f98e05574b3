import { Divider, Flex } from 'antd';
import styled from 'styled-components';
import { useDataState } from './store/useDataState';
import { useEffect } from 'react';

const Container = styled.div`
  background: #fff;
  border-radius: 1.2em;
  width: 100%;
  padding: 1.6em;
  margin-bottom: 1.4em;
`;

// 等级图标映射
const levelIcons = {
  LV1: 'https://img.alicdn.com/imgextra/i2/O1CN01YFO3Fq1OMzEOTvAHB_!!6000000001692-2-tps-63-63.png',
  LV2: 'https://img.alicdn.com/imgextra/i2/O1CN01hmiRG721NESSinYXk_!!6000000006972-2-tps-63-63.png',
  LV3: 'https://img.alicdn.com/imgextra/i2/O1CN01MEjBWI1pxA8laMQtx_!!6000000005426-2-tps-63-63.png',
  LV4: 'https://img.alicdn.com/imgextra/i2/O1CN01ZZHMd61hZKbd99t7t_!!6000000004291-2-tps-63-63.png',
  LV5: 'https://img.alicdn.com/imgextra/i1/O1CN01pjeChy1z8SRxDXP2n_!!6000000006669-2-tps-63-63.png',
  LV6: 'https://img.alicdn.com/imgextra/i4/O1CN01P3MInS1oUw7DZo6HH_!!6000000005229-2-tps-63-63.png',
};

// LabelContainer 支持显示等级图标
const LabelContainer = (props: { value: any; score_level: string; levelIcon: string }) => {
  const { value, score_level, levelIcon } = props;

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        width: 100,
        fontWeight: 500,
        paddingLeft: 8,
        height: 37,
        lineHeight: '37px',
        fontSize: 16,
        flexShrink: 0,
      }}
    >
      <img
        src={levelIcon}
        alt={score_level}
        style={{
          width: 24,
          height: 24,
          marginRight: 4,
          flexShrink: 0,
        }}
      />
      {value}
    </div>
  );
};

const TargetWrapper = styled.div`
  text-align: center;
  &:first-child {
    justify-self: left;
  }
  &:nth-child(2) {
    justify-self: center;
  }
  &:nth-child(3) {
    justify-self: right;
  }
`;
const TargetItem = ({ label, value }) => {
  return (
    <TargetWrapper>
      <div style={{ color: '#999', fontSize: '1.4em' }}>{label}</div>
      <div
        style={{
          fontSize: '1.8em',
          fontWeight: 500,
          color: '#1a66ff',
        }}
      >
        {value || '-'}
      </div>
    </TargetWrapper>
  );
};
function ShopHeaderCard(props: {
  competivence: string;
  duration: string;
  score_level: string;
  situation: string;
}) {
  const { duration, score_level, situation, competivence } = props;
  const { shopName, shopList, isMultiShops, unregisterField, registerField, isShow } =
    useDataState();

  const shownFields = isShow?.(['competition', 'operatingStatus', 'potentialOpportunity']) || [
    true,
    true,
    true,
  ];

  // 多门店展示逻辑
  let displayShopName = shopName;
  if (shopList && shopList.length > 1) {
    const firstName = shopList[0]?.label || '';
    displayShopName = `${firstName}等 ${shopList.length}家门店`;
  }

  // 获取等级图标，兜底 LV1
  const levelKey = score_level?.toUpperCase() || 'LV1';
  const levelIcon = levelIcons[levelKey];

  useEffect(() => {
    if (isMultiShops) {
      unregisterField('shopHeaderCard');
    } else {
      registerField({
        label: '店铺概览',
        key: 'shopHeaderCard',
        children: [
          {
            title: '同行竞争',
            key: 'competition',
          },
          {
            title: '经营现状',
            key: 'operatingStatus',
          },
          {
            title: '潜在商机',
            key: 'potentialOpportunity',
          },
        ],
      });
    }
  }, [isMultiShops]);

  return (
    <Container>
      <Flex wrap={false} align="center">
        <div style={{ fontSize: '1.6em', fontWeight: 500, marginRight: '1em' }}>
          {displayShopName}
        </div>
        {score_level && duration && !isMultiShops ? (
          <LabelContainer value={duration} levelIcon={levelIcon} score_level={score_level} />
        ) : null}
      </Flex>

      {isMultiShops ? null : (
        <>
          <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />
          <div
            style={{ padding: '4px 12px', display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)' }}
          >
            {shownFields?.[0] && <TargetItem label="同行竞争" value={competivence || '-'} />}
            {shownFields?.[1] && <TargetItem label="经营现状" value={situation} />}
            {shownFields?.[2] && <TargetItem label="潜在商机" value="空间大" />}
          </div>
        </>
      )}
    </Container>
  );
}

export default ShopHeaderCard;
