.progressBar_wrap {
  // height: 107px;
  margin-top: 26px;
  position: relative;
  width: 100%;
}
.progressBar {
  padding-left: 12px;
  padding-right: 10px;
  position: absolute;
  height: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  box-sizing: border-box;
  .progressBar_trail {
    border-radius: 8px;
    background-color: #fce9e9;

    .progressBar_fill {
      width: 0%;
      height: 8px;
      background-color: #ff4d2a;
      border-radius: 8px;
    }
  }
}

.progress_scroll {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .scroll_content {
    text-align: center;
    .content_icon {
      width: 42px;
      display: flex;
      justify-content: center;
      margin-bottom: 7px;
      .indicator {
        z-index: 0;
        height: 28px;
        width: 28px;
        background-color: #fff5f4;
        border: 4px solid #fdeded;
        border-radius: 50%;
        box-sizing: border-box;
      }
      .icon_indicator {
        z-index: 1;
        background-image: url('https://img.alicdn.com/imgextra/i2/O1CN012JekQq1Rs4kE8pgoW_!!6000000002166-2-tps-84-84.png');
        height: 28px;
        width: 28px;
        border-radius: 50%;
        background-size: 28px 28px;
        background-repeat: no-repeat;
      }
    }

    .scroll_title {
      color: #ff4d2a;
      font-size: 1em;
      height: 34px;
      font-family: 'PingFangSC';
    }
    .scroll_scoreTitle {
      width: 42px;
      height: 34px;
      line-height: 34px;
      font-size: 1em;
      color: #ff4d2a;
      text-align: center;
      margin-bottom: 4px;
    }
  }

  .scroll_content:last-child {
    .scroll_scoreTitle {
      text-align: right;
    }
    .content_icon {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }
  }

  .scroll_content:first-child {
    .scroll_title {
      margin-left: 10px;
    }
    .indicator,
    .icon_indicator {
      background: none;
    }
  }
}
