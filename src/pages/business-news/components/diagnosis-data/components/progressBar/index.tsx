import React from 'react';

import style from './index.module.less';

const ProgressBar = ({ scoreBar }) => {
  const { completedCount = 0, countUnit = '张', scoreList = [] } = scoreBar ?? {};
  const linearLength = () => {
    let firstPercent = 0;
    scoreList?.length &&
      scoreList.forEach((val, index) => {
        const parts = 100 / (scoreList.length - 1);
        if (
          (completedCount < val.completeLimit &&
            completedCount > scoreList[index - 1].completeLimit) ||
          completedCount === val.completeLimit
        ) {
          firstPercent =
            completedCount === val.completeLimit ? parts * index : parts * (index - 0.5);
        }
      });
    return { firstPercent };
  };

  return (
    <>
      {scoreBar ? (
        <div className={style.progressBar_wrap}>
          <div className={style.progressBar}>
            <div className={style.progressBar_trail}>
              <div
                className={style.progressBar_fill}
                style={{ width: `${linearLength().firstPercent}%` }}
              />
            </div>
          </div>

          <div className={style.progress_scroll}>
            {scoreList.map((val, index) => {
              return (
                <div className={style.scroll_content} key={val.completeLimit}>
                  <div className={style.scroll_scoreTitle}>
                    {val?.score ? `+${val.score}` : null}
                  </div>
                  <div className={style.content_icon}>
                    <div
                      className={
                        completedCount >= val.completeLimit ? style.icon_indicator : style.indicator
                      }
                    />
                  </div>
                  <div
                    className={style.scroll_title}
                    style={{ color: completedCount <= val.completeLimit ? '#888' : '#ff4d2a' }}
                  >
                    {val.completeLimit}
                    {countUnit || ''}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : null}
    </>
  );
};

export default ProgressBar;
