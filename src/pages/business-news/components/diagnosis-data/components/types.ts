export interface DiagnoseResultDTO {
  showGrade: string | number;
  industryMerchantScore: number;
  protectedDays: number | null;
  protected: boolean;
  merchantScoreChange: string;
  merchantScore: number;
  realGrade: string;
  class: string;
  undoCount: number;
  nextGradeScoreGap: number | null;
  awardList: string[];
}

export interface AlertItem {
  schema: string;
  image: string;
  btnText: string;
  title: string;
  type: string | null;
  class: string;
  desc: string;
}

export interface TaskInstanceItemDTO {
  scoreProcessBar: any | null;
  buttonText: string;
  gmtModified: number;
  level: number;
  taskOwner: string;
  completionCallbackMode: string;
  link: string | null;
  icon: string;
  completionMode: string;
  scoreRule: any | null;
  label: string;
  title: string;
  tagName: string | null;
  gmtCreate: number;
  score: string | null;
  subTitle: string;
  taskCode: string;
  alert: AlertItem | null;
  taskName: string;
  tag: string;
  state: number;
  class: string;
  desc: string;
}

export interface TaskSummaryDTOV3 {
  diagnoseResultDTO: DiagnoseResultDTO;
  shopName: string;
  list: TaskInstanceItemDTO[];
  class: string;
  new: boolean;
}
