/* eslint-disable no-nested-ternary */
import TaskItem from '@alife/mp-task-item';
import { MerchantGradeCard } from './merchant-grade-card';
import { TaskSummaryDTOV3 } from './types';

export default function TaskSummaryV3(props: { data: TaskSummaryDTOV3 }) {
  const { data } = props;
  const list = data?.list || [];
  return (
    <div
      style={{
        padding: '0.8em 1.2em',
      }}
    >
      <div>
        <MerchantGradeCard isNew={!!data?.new} data={data?.diagnoseResultDTO} />
        {list.length ? (
          <div>
            {list.map((item) => (
              <TaskItem platform="pc" data={item} readOnly />
            ))}
          </div>
        ) : null}
      </div>
    </div>
  );
}
