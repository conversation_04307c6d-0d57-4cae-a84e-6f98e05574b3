import { useMount, useUnmount } from 'ahooks';
import DataCard from './card-list/card';
import { useDataState } from './store/useDataState';
import styled from 'styled-components';
import { CirclePie } from '@/components/charts/pie';
import FlyLineMap from '@/components/charts/amap/flyline';
import { Flex, message, Spin } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { canvasToGif } from '@/utils/canvasToGif';
import { useState } from 'react';
import { trace } from '@/utils/trace';

const GridWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  & > div {
    width: 0;
  }
`;

const formatterNumber = (number: string | number) => {
  if (typeof number === 'string') {
    return Number(number);
  } else {
    return number;
  }
};

export default function ConsumerAnalysis(props: { data: any }) {
  const { data } = props;
  const { registerField, unregisterField, isShow, isChild } = useDataState();
  const isAllHidden = !isShow('consumer_analysis_title');
  useMount(() => {
    registerField({
      label: '客群分析',
      key: 'consumer_analysis',
      children: [
        {
          key: 'consumer_analysis_title',
          title: '整体模块',
          defaultHidden: true,
        },
        {
          title: '飞线图',
          key: 'fly_graph',
        },
        {
          title: '性别',
          key: 'gender',
        },
        { title: '年龄', key: 'age' },
        {
          title: '学历',
          key: 'education',
        },
        {
          title: '有车',
          key: 'has_car',
        },
      ],
    });
  });
  useUnmount(() => {
    unregisterField('consumer_analysis');
  });
  const resData = data?.structureData || {};
  const shownFields = isShow(['gender', 'age', 'education', 'has_car', 'fly_graph']);
  const handleDownloadImg = async () => {
    setDownloading(true);
    try {
      await canvasToGif({
        domId: 'flyline-map',
        fileName: '飞线图',
      });
    } catch {
      message.error('下载失败');
    }
    trace('download_flyline');
    setDownloading(false);
  };
  const [downloading, setDownloading] = useState(false);
  if (!isChild || isAllHidden || !shownFields.some(Boolean)) {
    return null;
  }
  return (
    <DataCard
      data={{
        label: (
          <Flex align="center" justify="space-between">
            客群分析
            {shownFields[4] && <DownloadOutlined id="empty" onClick={handleDownloadImg} />}
          </Flex>
        ),
      }}
    >
      <div style={{ padding: '0 1.2em 1.2em' }}>
        {shownFields[4] && (
          <Spin spinning={downloading}>
            <FlyLineMap
              domId="flyline-map"
              data={data?.graphData?.flylinegraph}
              center={data?.graphData?.companyxy ? data?.graphData?.companyxy.split(',') : []}
            />
          </Spin>
        )}

        <GridWrapper>
          {shownFields[0] && (
            <CirclePie
              title="性别"
              data={[
                { type: '男', value: formatterNumber(resData.arrive_is_male_pcnt) },
                { type: '女', value: formatterNumber(resData.arrive_is_female_pcnt) },
              ]}
            />
          )}
          {shownFields[1] && (
            <CirclePie
              title="年龄"
              data={[
                { type: '19-29', value: formatterNumber(resData.arrive_age_19_29_pcnt) },
                { type: '30-49', value: formatterNumber(resData.arrive_age_30_49_pcnt) },
                { type: '50-65', value: formatterNumber(resData.arrive_age_50_64_pcnt) },
                { type: '>65', value: formatterNumber(resData.arrive_age_ge_65_pcnt) },
              ]}
            />
          )}
          {shownFields[2] && (
            <CirclePie
              title="学历"
              data={[
                { type: '高中及以下', value: formatterNumber(resData.arrive_is_junior_high_pcnt) },
                { type: '大专', value: formatterNumber(resData.arrive_is_junior_college_pcnt) },
                { type: '本科', value: formatterNumber(resData.arrive_is_bachelor_pcnt) },
                { type: '硕博及以上', value: formatterNumber(resData.arrive_is_master_pcnt) },
              ]}
            />
          )}
          {shownFields[3] && (
            <CirclePie
              title="有车"
              data={[
                { type: '有车', value: formatterNumber(resData.arrive_has_car_pcnt) },
                { type: '其他', value: formatterNumber(resData.arrive_has_not_car_pcnt) },
              ]}
            />
          )}
        </GridWrapper>
      </div>
    </DataCard>
  );
}
