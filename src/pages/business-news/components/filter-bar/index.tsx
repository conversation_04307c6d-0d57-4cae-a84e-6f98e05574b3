import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { useQuery } from '@/hooks/useQuery';
import styled from 'styled-components';

const timeFormat = (time: string | number) => {
  if (!time) return;
  if (typeof time === 'string') {
    return Number(time);
  }
  return time;
};
const DateContainer = styled.div`
  position: absolute;
  left: 1.6em;
  bottom: 1.4em;
  color: rgba(0, 0, 0, 0.6);
  font-size: 1.2em;
`;
export const FilterBar = ({ date }: { date: [Dayjs, Dayjs] }) => {
  const [query] = useQuery();
  const startTime = timeFormat(date?.[0] || query?.startTime);
  const endTime = timeFormat(date?.[1] || query?.endTime);
  const isLastWeek = () => {
    const yestoday = dayjs().add(-1, 'days');
    const lastWeekEnd = dayjs().add(-7, 'days');
    return dayjs(endTime).isSame(yestoday, 'day') && dayjs(startTime).isSame(lastWeekEnd, 'day');
  };

  const formatDate = () => {
    const dateFormat = `${dayjs(startTime).format('M.D')}-${dayjs(endTime).format('M.D')}`;
    return isLastWeek() ? `上周${dateFormat}` : dateFormat;
  };

  return (
    <div style={{ position: 'relative' }}>
      <img
        src="https://img.alicdn.com/imgextra/i2/O1CN01jwBxTJ1pPjBtJohu6_!!6000000005353-2-tps-1125-342.png"
        style={{ width: '100%' }}
      />
      <DateContainer>{formatDate()}</DateContainer>
    </div>
  );
};
