import { TASK_LABEL_ENUM } from '@/common/const';

export const enum BusinessNewsType {
  TRADE = 'TRADE',
  CONSULT = 'CONSULT',
  FOOD = 'FOOD',
}

/**
 * 喜报卡片类型
 */
export const enum BusinessNewsDataType {
  AMAP_DATA = 'AMAP_DATA', // 高德独家数据分析
  SHOP_BUSINESS_DATA = 'SHOP_BUSINESS_DATA', // 门店经营数据
  BUSINESS_DATA = 'BUSINESS_DATA', // 经营数据
  AD_DATA = 'AD_DATA', // 广告投放
}

export const enum NewBusinessNewsType {
  FOOD = 'FOOD',
  OTHER = 'OTHER',
}

/**
 * 喜报卡片类型
 */
export enum BusinessDataType {
  AMAP_DATA = 'AMAP_DATA', // 高德独家数据分析
  SHOP_BUSINESS_DATA = 'SHOP_BUSINESS_DATA', // 门店经营数据
  BUSINESS_DATA = 'BUSINESS_DATA', // 经营数据
  AD_DATA = 'AD_DATA', // 广告投放
}

export interface IBusinessBaseData {
  dataResult: Record<string, any>;
  recharge: boolean;
  shopScore: string;
  taskSummary?: {
    list: any[];
  };
  strategySuggestDTO: {
    annualExpirationDate: string;
    annualRenewalShopNum: number;
    parentAdLabelList: TASK_LABEL_ENUM[];
    shopAdPlanConfigs: Array<{
      shopId: string;
      adPlanConfigs: any[];
    }>;
    cashBalance: string; // 现金余额
    balanceConsumable: number; // 余额可消耗天数
    consecutiveNotAdvertising: string; // 已停投xx天
  };
}
export interface IBusinessData extends IBusinessBaseData {
  businessNewType: string;
  dataResult: any;
  recharge: boolean;
  fields: Array<{ label: string; children?: any[] }>;
}

export interface ITraceData {
  (
    businessData: {
      data: IBusinessBaseData;
      businessNewType: string;
      formData: Record<string, any>;
    },
    detailData: {
      shopData: any[];
      fields: Array<{ label: string; children?: any[] }>;
    },
  ): void;
}

// 需要同行选择器的数据类型
export const PEER_SELECTOR_DATA_TYPES = [
  '高德独家数据',
  '经营数据',
  '广告数据',
  '客资线索',
  '用户到店',
  '智能体数据',
] as const;
