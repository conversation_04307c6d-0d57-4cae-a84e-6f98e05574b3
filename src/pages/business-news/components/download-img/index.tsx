import React from 'react';
import { useRequest } from 'ahooks';
import { getMerchantReviewExtInfo } from '@/services';
import { ModuleSPMKey, PageSPMKey, trace, traceClick } from '@/utils/trace';
import { But<PERSON>, Row, Col } from 'antd';
import { domToImg } from '@/utils/domToImg';
import dayjs from 'dayjs';

interface IProps {
  name: string;
  hasOptGroup: boolean;
  sendReport: any;
  pid: string;
  isMultiShops: boolean;
}
const DownloadImg = (props: IProps) => {
  const { name, hasOptGroup, sendReport, pid, isMultiShops } = props;
  const onClick = () => {
    domToImg({
      domId: 'downloadImg',
      fileName: '经营喜报',
      isDownload: true,
    });
    trace('DOWNLOAD_IMAGE', {
      isMultiShops,
    });
    traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.下载图片'], {
      pid,
    });
  };
  const { data: extInfo } = useRequest(
    async () => {
      if (pid && hasOptGroup) {
        return getMerchantReviewExtInfo(pid);
      }
      return {};
    },
    {
      refreshDeps: [pid, hasOptGroup],
    },
  );
  const canSend = extInfo?.sendMerchantNewsAvailable;
  const lastTime = extInfo?.lastReachTime;
  const sendToQW = async () => {
    if (!canSend) {
      return Promise.resolve();
    }

    // 添加发到企微群点击埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.发到企微群'], {
      pid,
    });

    const fileName = `${dayjs().format('YYYY-MM-DD')}-${name}经营喜报.png`;
    const res = await domToImg({
      domId: 'downloadImg',
      isUpload: true,
    });
    trace(
      'sendToQw',
      {
        url: res,
        name: fileName,
      },
      isMultiShops,
    );
    sendReport?.(res);
  };
  const { run: handleSend, loading } = useRequest(sendToQW, {
    debounceLeading: true,
    manual: true,
    debounceTrailing: false,
    debounceWait: 5000,
  });

  return (
    <div
      style={{
        position: 'sticky',
        bottom: 0,
        width: '100%',
        // height: 100,
        backgroundColor: '#fff',
        // padding: '30px 15px 0 15px',
        zIndex: 10,
        paddingTop: '2em',
      }}
    >
      <Row align="middle" gutter={12}>
        {hasOptGroup && (
          <Col span={16}>
            <Button
              // className={styles.btn}
              disabled={!canSend}
              type="primary"
              onClick={handleSend}
              loading={loading}
              style={{ width: '100%' }}
            >
              发到企微群
            </Button>
          </Col>
        )}
        <Col span={hasOptGroup ? 8 : 24}>
          <Button
            // className={styles.btn}
            onClick={onClick}
            style={{
              border: '1px solid #e5e5e5',
              borderRadius: 8,
              width: '100%',
            }}
          >
            下载图片
          </Button>
        </Col>
      </Row>
      {lastTime && hasOptGroup ? (
        <div style={{ fontSize: 12, color: '#898989', marginTop: 5 }}>上次发送时间: {lastTime}</div>
      ) : null}
    </div>
  );
};

// eslint-disable-next-line no-restricted-syntax
export default DownloadImg;
