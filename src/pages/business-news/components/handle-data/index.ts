import { ReactElement } from 'react';
import { getFieldKeys, getFields } from './datas';
import { isAmapAgent } from '@/common/utils';

export interface DataDisplayItem {
  key: string;
  title?: string;
  tips?: string;
  value?: string | number;
  compareValue?: number | string;
  adCount?: number;
  expandText?: boolean;
  expandChildren: ReactElement;
  expandChildrenData?: DataDisplayItem[];
}

export interface IDataRecord {
  label: string;
  children?: IDataRecord[] | DataDisplayItem[] | null;
}

// 美食数据
export const getFoodData = (
  dataResult: any = {},
  options?: {
    hasAiData?: boolean;
    isNode?: boolean;
  },
) => {
  const is31 = isAmapAgent();
  const _getFields = (fields: Parameters<typeof getFields>[0]) =>
    getFields(fields, dataResult, true);
  const isNode = options?.isNode;
  const foodData = [
    {
      label: '门店经营数据',
      children:
        is31 && !isNode
          ? _getFields(['门店曝光量', '门店访问量', '商家相册数', '电话拨打量'])
          : _getFields([
              '门店曝光量',
              '门店访问量',
              '商家相册数',
              '商家招牌菜数',
              '预约到店量',
              '电话拨打量',
              '在线订座量',
              '电话拨打量',
              '电话接通率',
              '在线订座接单率',
              '商品核销率',
              '商品下单量',
            ]),
    },
    {
      label: '广告投放',
      children: _getFields([
        '广告总消耗',
        '广告有效到店量',
        '广告有效到店成本',
        '广告曝光量',
        '广告点击量',
        '广告点击率',
        '广告预约到店成本',
      ]),
    },
  ];
  if (options?.hasAiData) {
    foodData.push({
      label: '智能体数据',
      children: [
        ..._getFields([
          '智能体曝光量',
          '智能体使用量',
          '智能体会话量',
          '平均有效对话轮次',
          '引导到店数',
          // '新客引导到店数',
          '引导到店占有率',
          '有效播报次数',
          {
            key: '智能体导购次数',
            children: ['留资卡片', '服务卡片'],
          },
        ]),
      ],
    });
  }
  return foodData;
};

// 非美食数据
export const getOtherData = (
  dataResult: any = {},
  options?: {
    hasAiData?: boolean;
  },
) => {
  const _getFields = (fields: Parameters<typeof getFields>[0]) => getFields(fields, dataResult);
  const otherData = [
    {
      label: '高德独家数据',
      children: getFields(['门店曝光量', '导航到店量'], dataResult),
    },
    {
      label: '经营数据',
      children: _getFields([
        '商品数',
        '商家相册数',
        '成交订单数',
        '成交金额',
        '商品核销金额',
        '商品核销量',
        '电话拨打量',
        '新增评论数',
      ]),
    },
    {
      label: '广告数据',
      children: [
        {
          label: '流量及消耗',
          children: _getFields([
            '广告曝光量',
            {
              key: '广告日均曝光量',
              defaultHidden: true,
            },
            '广告点击量',
            {
              key: '广告点击率',
              defaultHidden: true,
            },
            {
              key: '千次曝光成本',
              defaultHidden: true,
            },
            {
              key: '日均现金消耗',
              defaultHidden: true,
            },
            '广告总消耗',
            {
              key: '现金消耗',
              defaultHidden: true,
            },
            {
              key: '红包消耗',
              defaultHidden: true,
            },
          ]),
        },
        {
          label: '客资线索',
          children: _getFields([
            '客资线索消耗',
            {
              key: '客资线索消耗占比',
              defaultHidden: true,
            },
            // {
            //   key: '进店留资率',
            //   defaultHidden: true,
            // },
            {
              key: '日均广告客资量',
              defaultHidden: true,
            },
            '广告客资成本',
            {
              key: '广告客资量',
              children: [
                {
                  key: '电话客资',
                  defaultHidden: true,
                },
                {
                  key: '电话客资占比',
                  defaultHidden: true,
                },
                {
                  key: '平台客资',
                  defaultHidden: true,
                },
                {
                  key: '平台客资占比',
                  defaultHidden: true,
                },
                {
                  key: '订单客资',
                  defaultHidden: true,
                },
                {
                  key: '订单客资占比',
                  defaultHidden: true,
                },
                {
                  key: '在线咨询客资',
                  defaultHidden: true,
                },
                {
                  key: '在线咨询客资占比',
                  defaultHidden: true,
                },
                {
                  key: '预约礼客资',
                  defaultHidden: true,
                },
                {
                  key: '预约礼客资占比',
                  defaultHidden: true,
                },
                {
                  key: '到店预约客资',
                  defaultHidden: true,
                },
                {
                  key: '到店预约客资占比',
                  defaultHidden: true,
                },
                {
                  key: '高德打车客资',
                  defaultHidden: true,
                },
                {
                  key: '高德打车客资占比',
                  defaultHidden: true,
                },
              ],
            },
            // '不可见客资',
            // '不可见客资占比',
            // {
            //   key: '分城市客资量占比',
            //   defaultHidden: true,
            // },
            // {
            //   key: '分城市客资成本',
            //   defaultHidden: true,
            // },
            // {
            //   key: '分区域客资量占比',
            //   defaultHidden: true,
            // },
            // {
            //   key: '分区域客资成本',
            //   defaultHidden: true,
            // },
          ]),
        },
        {
          label: '用户到店',
          children: _getFields([
            '用户到店消耗',
            {
              key: '用户到店消耗占比',
              defaultHidden: true,
            },
            '广告有效到店量',
            '广告有效到店成本',
            '来电量',
          ]),
        },
      ],
    },
  ];
  if (options?.hasAiData) {
    otherData.push({
      label: '智能体数据',
      children: _getFields([
        '智能体曝光量',
        '智能体使用量',
        '智能体会话量',
        '平均有效对话轮次',
        '引导到店数',
        // '新客引导到店数',
        '智能体留资占比',
        '有效播报次数',
        {
          key: '智能体导购次数',
          children: ['留资卡片', '服务卡片'],
        },
      ]),
    });
  }
  return otherData;
};

export const getHiddenKeys = (isFood = false) => {
  if (isFood) {
    return [];
  }
  return getFieldKeys(
    [
      '广告日均曝光量',
      '广告点击率',
      '千次曝光成本',
      '客资线索消耗占比',
      '日均广告客资量',
      '电话客资占比',
      '平台客资占比',
      '订单客资占比',
      '在线咨询客资占比',
      '预约礼客资占比',
      '到店预约客资占比',
      '高德打车客资占比',
      '不可见客资占比',
      '用户到店消耗占比',
    ],
    false,
  );
};
