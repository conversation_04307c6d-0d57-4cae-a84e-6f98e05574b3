import { useRequest, useUnmount } from 'ahooks';
import DataCard from './card-list/card';
import { useDataState } from './store/useDataState';
import { Divider, Flex, Progress } from 'antd';
import RedSelect from './common/red-select';
import { IconDoctor } from '@/components/icons';
import styled from 'styled-components';
import HeatGraph from '@/components/charts/amap/heat-graph';
import numral from 'numeral';
import { queryData as getData } from '@/services';

import { useEffect, useState } from 'react';

const StyledProgress = styled(Progress)`
  .ant-progress-text {
    color: #fff;
    font-weight: 600;
    font-size: 12px;
  }
`;
const radiusOptions = [
  { label: '附近1km人流分布图', value: 1 },
  { label: '附近2km人流分布图', value: 2 },
  { label: '附近10km人流分布图', value: 10 },
  { label: '附近20km人流分布图', value: 20 },
];

export default function UserVector(props: {
  age: string;
  gender: string;
  education: string;
  has_car: string;
  percent: string;
}) {
  const {
    registerField,
    unregisterField,
    isShow,
    isChild,
    dateRange,
    shopList,
    pid,
    isMultiShops,
    shopIdList,
  } = useDataState();
  const { age, gender, education, has_car, percent } = props;

  const [radius, setRadius] = useState(radiusOptions[0].value);
  const { data: yuntuData } = useRequest(
    async () => {
      const [startDate, endDate] = dateRange || [];
      if (isMultiShops || !startDate || !endDate || !shopIdList.length) return {};
      const res = await getData(
        {
          shopIdList,
          pid,
          startDate: dateRange?.[0],
          endDate: dateRange?.[1],
          buffer: radius,
        },
        'xibao_report_yuntu_geo_dau_application',
      );
      const result = res.applicationDataList?.[0]?.ruleDataList?.[0]?.values?.[0];
      const center = result?.company_xy ? result.company_xy.split(',') : [];
      return {
        ...result,
        center,
      };
    },
    {
      refreshDeps: [shopList, pid, dateRange, radius],
    },
  );
  const isAllHidden = !isShow('user_vector_title');
  useEffect(() => {
    registerField({
      label: '潜在商机',
      key: 'user_vector',
      children: [
        {
          key: 'user_vector_title',
          title: '整体模块',
          defaultHidden: true,
        },
        {
          key: 'user_area',
          render: () => {
            return (
              <RedSelect
                options={radiusOptions}
                value={radius}
                onChange={setRadius}
                placeholder="请选择人流分布范围"
                style={{ marginRight: 12 }}
              />
            );
          },
        },
        {
          title: '人流分布图',
          key: 'user_area_graph',
        },
        {
          title: '目标客群占比分析',
          key: 'user_vector_percent',
        },
      ],
    });
  }, [radius]);
  useUnmount(() => {
    unregisterField('user_vector');
  });
  const textValue = [age, education, has_car, gender].filter(Boolean).join(',');
  const shownFields = isShow(['user_vector_title', 'user_area_graph', 'user_vector_percent']);
  if (!isChild || isAllHidden || !shownFields.some(Boolean)) {
    return null;
  }
  return (
    <DataCard data={{ label: '潜在商机' }}>
      <div style={{ padding: '0 1.2em 1.2em' }}>
        {shownFields[0] && (
          <div style={{ position: 'relative' }}>
            <HeatGraph
              radius={radius}
              data={yuntuData?.geo_dau_graph}
              center={yuntuData?.center || []}
            />
            <div
              style={{
                position: 'absolute',
                left: 14,
                top: 5,
                color: '#09b0c1',
                background: '#fff',
                padding: '2px 10px',
                borderRadius: 4,
                fontSize: '1.2em',
              }}
            >
              附近{radius}km人流分布图
            </div>
          </div>
        )}

        {shownFields[1] && textValue ? (
          <>
            <Divider style={{ marginBottom: 16 }} />
            <Flex align="center" style={{ color: '#F38638', marginBottom: 12 }}>
              <IconDoctor style={{ fontSize: '3.6em', marginRight: 14, flexShrink: 0 }} />
              <div style={{ fontSize: '1.4em' }}>
                您当前门店的主力画像为: {textValue}人群。目前商圈内符合主力画像的到店客群占比如下:
              </div>
            </Flex>
            <StyledProgress
              // eslint-disable-next-line @typescript-eslint/no-shadow
              format={(percent) => {
                let number: number | string;
                if (!percent) {
                  return '0%';
                }
                if (percent <= 0.1) {
                  number = `${numral(percent * 10).format('0.00')}‰`;
                } else {
                  number = `${numral(percent).format('0.00')}%`;
                }
                return number;
              }}
              percent={+(percent || 0) * 100}
              percentPosition={{ align: 'end', type: 'inner' }}
              size={{ height: 20 }}
              strokeColor="linear-gradient(270deg, #F38638 0%, #FFAD72 100%)"
            />
            <Flex
              justify="space-between"
              style={{ color: '#00000099', fontSize: '1.2em', marginTop: 6 }}
            >
              <div>目标客群</div>
              <div>客群总量</div>
            </Flex>
          </>
        ) : null}
      </div>
    </DataCard>
  );
}
