import React from 'react';
import style from '../index.module.less';
import { useDataState } from '../store/useDataState';

const carouselList = [
  {
    picture: '老李',
    title: '老李家衢州菜',
    subTitle: '高德上导航寻找饭店的人，马上能看到我们店，预定电话和业绩都增长了不少',
    imgSrc:
      'https://img.alicdn.com/imgextra/i1/O1CN01kiIrVQ1jyB5U3EDtV_!!6000000004616-0-tps-1368-864.jpg',
  },
  {
    picture: '小高',
    title: '小高口腔门诊部',
    subTitle: '附近需要进行口腔护理的顾客，都能找到我家，现在的收入稳定多了',
    imgSrc:
      'https://img.alicdn.com/imgextra/i2/O1CN011xtslg1jGD41HpVzK_!!6000000004520-0-tps-1368-840.jpg',
  },
];
const AdData = () => {
  const { isFood } = useDataState();

  const carouse = carouselList[isFood ? 0 : 1];

  return (
    <div className={style.carousel_content}>
      <div className={style.picture}>{carouse.picture}</div>
      <div className={style.shopTitle}>{carouse.title}</div>
      <div className={style.shopSubTitle}>{`"${carouse.subTitle}。"`}</div>
      <img style={{ width: '100%' }} src={carouse.imgSrc} alt="" />
    </div>
  );
};

export default React.memo(AdData);
