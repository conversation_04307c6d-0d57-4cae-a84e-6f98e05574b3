import { GlobalProvider } from '@/context/global-store';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import { Outlet } from 'ice';
import { Robot } from '@/components/robot';
import { isAgent, isOuterPage } from '@/common/utils';
import { useAmap } from '@/components/charts/amap/useAmap';
import { StyleSheetManager } from 'styled-components';

export default function Index() {
  useAmap();
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1a66ff',
          borderRadius: 4,
        },
      }}
    >
      {/* styled-components偶发因为微前端导致样式加载失败，所以用这个来消除bug */}
      <StyleSheetManager disableCSSOMInjection>
        {/* 包括切换视角和服务端控制禁用部分功能的逻辑 */}
        <GlobalProvider>
          <Outlet />
        </GlobalProvider>
        {isAgent() || isOuterPage() ? null : <Robot />}
      </StyleSheetManager>
    </ConfigProvider>
  );
}
