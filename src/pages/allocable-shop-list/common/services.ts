import { gdRequest } from '@/services/request';
import { fetch } from '@alife/amap-fetch';

export const queryShopRelationSelectableList = () => {
  return fetch({
    apiKey: 'alsc-kbt-leads-center.RelationQueryGatewayService.queryShopRelationSelectableList',
    params: [],
    options: {
      customResponse: true,
    },
  });
};

export const allocateRelation = async (params) => {
  return fetch({
    apiKey: 'alsc-kbt-leads-center.ShopStaffRelationManageGatewayService.allocateRelation',
    params,
    options: {
      customResponse: true,
    },
  });
};

export const batchAllocateRelation = () => {};

export const searchShop = (params) => {
  return gdRequest('amap-sales-operation.OptShopPoolQueryHsf.queryWaitDispatchShopList', params);
};
