import React, { useEffect, useState } from 'react';
import { Form, Input, Modal, Select } from 'antd';
import { useUserInfo } from '@alife/kb-biz-util';
import BdSelect from '@alife/mo-bd-select';
import { queryShopRelationSelectableList } from '../../common/services';

export default ({
  open,
  onCancel,
  onOk,
  item,
  checkShopInfo,
}: {
  open: boolean;
  onCancel: () => void;
  onOk: (v: any) => void;
  item: Array<string | number>;
  checkShopInfo: { storeName: string; shopId: string | Number };
}) => {
  const [form] = Form.useForm();
  const user = useUserInfo();

  const [shopStaffRelationInfoVOList, setShopStaffRelationInfoVOList] = useState([]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onOk({
        relationSubType: values.relationSubType,
        ownerId: values.user?.value ?? values.user,
      });
    } catch (e) {
      //
    }
  };
  const queryShopRelationSelectable = async () => {
    try {
      const {
        model: { selectableRelationType },
      } = await queryShopRelationSelectableList();
      console.log(selectableRelationType);
      setShopStaffRelationInfoVOList(
        selectableRelationType.map((item) => ({
          ...item,
          label: item.relationDesc,
          value: item.relationSubType,
        })),
      );
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (open) {
      form.resetFields();
    }
  }, [open, form]);

  useEffect(() => {
    if (item) queryShopRelationSelectable();
  }, [item]);

  return (
    <Modal
      title="门店关系分配"
      // title="门店权限分配"
      open={open}
      onCancel={onCancel}
      onOk={handleSubmit}
      destroyOnClose
      width={600}
    >
      <Form
        form={form}
        style={{ margin: '40px auto 0' }}
        labelCol={{ span: 8 }}
        initialValues={{
          user: { type: user.isInner ? 'SALES' : 'PROVIDER' },
        }}
      >
        <Form.Item label="目标门店">{`${checkShopInfo?.storeName}(${checkShopInfo?.shopId})`}</Form.Item>
        <Form.Item name="relationSubType" hidden initialValue="ADVERTISE_OPERATE">
          <Input value="ADVERTISE_OPERATE" />
        </Form.Item>
        <Form.Item label="门店关系" required>
          运维关系
        </Form.Item>
        <Form.Item name="user" label="被分配人" rules={[{ required: true, message: '此处必填' }]}>
          <BdSelect type="ALL" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
