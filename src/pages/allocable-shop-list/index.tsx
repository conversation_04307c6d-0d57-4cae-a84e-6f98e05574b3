import React, { useRef, useState } from 'react';
import { v4 as uuid } from 'uuid';
import { Button, Card, Col, Form, Input, message, Row, Select, Space, Table } from 'antd';
import { usePermissions } from '@alife/kb-biz-util';
import { Header } from '@/components/header';
import CategorySelect from '@alife/mo-common-category-select';
import style from './index.module.less';
import ModalAllocate from './components/ModalAllocate';
import { allocateRelation, searchShop } from './common/services';
import { definePageConfig } from 'ice';
import { useAntdTable } from 'ahooks';
import { isSome } from '@alife/amap-mp-utils';
import { render } from 'react-dom';

export function openEgoPage(url: string) {
  window.location.href = `/sale-pc/ego-qiankun-subapp/${url}`;
}

export default () => {
  const bizParam = useRef('');
  const [allocateItem, setAllocateItem] = useState<Array<string | number>>();
  const [checkShopInfo, setCheckShopInfo] = useState<any>();
  const permissions = usePermissions([
    'KBSHOP_RELATION_CREATE',
    'BATCH_SHOP_STAFF_SALES_RELATION_ALLOCATE',
    'SHOP_MANUAL_ASSIGN',
    'SHOP_AUTH',
    'SHOP_DETAIL',
    'gddd_privilege_manage',
  ]);

  const tableColumns = [
    // table
    {
      title: (
        <>
          <div>门店名称</div>
          <div>门店ID</div>
        </>
      ),
      dataIndex: 'shopId',
      fixed: 'left',
      width: 240,
      render(_, item) {
        return (
          <>
            <div>{item.shopName}</div>
            <div>{item.shopId}</div>
          </>
        );
      },
      hideInSearch: true,
      order: 100,
    },
    {
      title: 'poiId',
      width: 200,
      dataIndex: 'poiId',
    },
    {
      title: '含代运营任务',
      dataIndex: 'needDoOptTask',
      width: 120,
      hideInSearch: true,
      render: (needDoOptTask) => {
        // eslint-disable-next-line no-nested-ternary
        return isSome(needDoOptTask, 'Boolean') ? (needDoOptTask ? '是' : '否') : '—';
      },
    },
    {
      width: 120,
      title: '商家分',
      dataIndex: 'qualityScore',
      hideInSearch: true,
      render: (qualityScore) => {
        return qualityScore || '-';
      },
    },
    {
      title: '地址',
      dataIndex: 'address',
      hideInSearch: true,
      width: 300,
      render: (address) => {
        return address || '-';
      },
    },
    {
      title: '类目',
      width: 120,
      dataIndex: 'atagName',
      hideInSearch: true,
      render: (name) => {
        return name || '-';
      },
    },
    {
      title: '人店关系',
      dataIndex: 'merchantStaffRelationInfoVOList',
      hideInSearch: true,
      width: 200,
      render: (_, item) => {
        return <div>运维关系: {item.executorName}</div>;
      },
    },
    {
      title: '商户PID',
      dataIndex: 'merchantId',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '门店状态',
      dataIndex: 'shopState',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 100,
      hideInSearch: true,
      render: (_, item) => {
        return (
          <Space size="middle">
            {permissions.SHOP_MANUAL_ASSIGN && (
              <Button
                type="link"
                onClick={() => {
                  setAllocateItem([item.shopId]);
                  setCheckShopInfo({
                    storeName: item.shopName,
                    shopId: item.shopId,
                  });
                }}
              >
                分配
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  const [form] = Form.useForm();

  const { tableProps, search } = useAntdTable(
    async ({ current, pageSize }, formData) => {
      const { atagIds, ...data } = formData || {};

      const searchParams = {
        ...data,
        page: {
          pageNo: current,
          pageSize,
        },
        atagId: atagIds?.length ? atagIds[atagIds.length - 1] : undefined,
        requestId: uuid(),
      };

      bizParam.current = JSON.stringify(searchParams);
      try {
        const res = await searchShop(searchParams);

        return {
          success: process.env.NODE_ENV === 'development' || res.success,
          list: res?.dataList || [],
          total: res?.pageInfo?.totalCount,
        };
      } catch (error) {
        return {
          success: false,
          list: [],
          total: 0,
        };
      }
    },
    {
      defaultPageSize: 10,
      form,
    },
  );

  return (
    <div className={style.container}>
      <Header title="可分配门店" showTips={false} />
      <Card style={{ marginTop: 24 }}>
        <Form layout="horizontal" form={form}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item labelCol={{ span: 6 }} label="门店名称" name="shopName">
                <Input placeholder="输入门店名称搜索" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item labelCol={{ span: 6 }} label="门店id" name="shopId">
                <Input placeholder="输入门店id搜索" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item labelCol={{ span: 6 }} label="poiId" name="poiId">
                <Input placeholder="输入poi搜索" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item labelCol={{ span: 6 }} label="商户pid" name="merchantId">
                <Input placeholder="输入商户id搜索" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item labelCol={{ span: 6 }} label="经营品类" name="atagIds">
                <CategorySelect dataRange="all" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item labelCol={{ span: 6 }} label="含代运营任务" name="needDoOptTask">
                <Select
                  placeholder="请选择"
                  allowClear
                  options={[
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row justify="end">
            <Space>
              <Button size="large" type="primary" onClick={search.submit}>
                查询
              </Button>
              <Button size="large" onClick={search.reset}>
                重置
              </Button>
            </Space>
          </Row>
        </Form>
      </Card>
      <Card style={{ marginTop: 24 }}>
        <Table
          className={style.table}
          columns={tableColumns}
          scroll={{ x: 1500 }}
          cardBordered
          rowKey="tbStoreId"
          search={{
            labelWidth: 'auto',
            searchText: '搜索',
            resetText: '清除条件',
            defaultCollapsed: false,
          }}
          options={false}
          {...tableProps}
          pagination={{
            ...(tableProps.pagination || {}),
            showQuickJumper: true,
            showSizeChanger: true,
          }}
          headerTitle="门店列表"
        />
      </Card>

      <ModalAllocate
        open={!!allocateItem?.length}
        onCancel={() => {
          setAllocateItem(undefined);
          setCheckShopInfo(undefined);
        }}
        item={allocateItem}
        checkShopInfo={checkShopInfo}
        onOk={(values) => {
          if (allocateItem.length === 1) {
            const params = {
              tbStoreId: allocateItem[0],
              ...values,
            };
            setAllocateItem(undefined);
            setCheckShopInfo(undefined);
            allocateRelation(params)
              .then((res) => {
                if (res.success) {
                  message.success('分配成功');
                  search.submit();
                } else {
                  message.error(res.errorMsg);
                }
              })
              .catch(() => {
                //
              });
          }
        }}
      />
    </div>
  );
};

export const pageConfig = definePageConfig(() => ({
  title: '待分配门店',
  spm: {
    spmB: 'allocable-shop-list',
  },
}));
