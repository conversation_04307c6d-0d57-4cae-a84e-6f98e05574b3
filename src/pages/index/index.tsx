import { Flex } from 'antd';
import DoubleTable from './components/double-table';
import TargetDashboard from './components/target-dashboard';
import TodoList from './components/todo-list';
import SwitchViewer from './components/switch-viewer';
import TimedTaskModule from './components/timed-task-module';
import { IfButtonShow } from '@/components/server-controller/useAction';
import { ActionButtonType } from '@/constants';
import { CoreTaskCompletion } from '@/components/completion-rate-card/core-task';
import { definePageConfig } from 'ice';

export default function Index() {
  return (
    <Flex gap={12} style={{ padding: 12, background: '#f5f5f5' }} vertical>
      <CoreTaskCompletion />

      {/* <IfButtonShow buttonType={ActionButtonType.核心任务完成率}>
        <CoreTaskCompletion />
      </IfButtonShow> */}
      {/* <IfButtonShow buttonType={ActionButtonType.绩效区块}>
        <BusinessTarget />
      </IfButtonShow> */}
      <IfButtonShow buttonType={ActionButtonType.柱状图区块}>
        <TargetDashboard />
      </IfButtonShow>
      {/* <BusinessTarget /> */}
      <TodoList />
      <TimedTaskModule />
      <SwitchViewer />
      <DoubleTable />
    </Flex>
  );
}
export const pageConfig = definePageConfig({
  spm: {
    spmB: 'xy-task-pc-home',
  },
});
