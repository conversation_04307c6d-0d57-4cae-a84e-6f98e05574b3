import React, { useEffect, useState } from 'react';
import { Button, Modal, Flex, Space, Table, message, Spin } from 'antd';
import { useAntdTable, useCountDown, useLocalStorageState, useMount, useRequest } from 'ahooks';
import { getTaskInfo, getUndoTaskList, checkoutTask } from '@/services';
import dayjs from 'dayjs';
import './index.less';
import { CheckOutlined, FieldTimeOutlined } from '@ant-design/icons';
import useModal from '@/hooks/useModal';
import { WeeklyReportDrawer } from '@/pages/business-news/components/weekly-report-drawer';
import { TIMED_TASK_TITLE_NAME } from '@/common/const';
import { unique } from 'radash';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

interface TaskConfirmedObj {
  confirmedList: string[];
  date: string;
}
const nowDate = dayjs().format('YYYY-MM-DD');
const TimedTaskModule = () => {
  const [loading, setLoading] = useState(false);
  const [countdown] = useCountDown({
    targetDate: dayjs().hour(12).minute(0).second(0),
    onEnd: () => setIsTimeOver(true),
  });
  const [chooseTask, setChooseTask] = useState<{
    scene: string;
    waitReachPidNum: number;
  }>({});
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [merchantData, setMerchantData] = useState<any>({});
  const [unCheckMap, setUnCheckMap] = useState<Record<number, any[]>>({});
  const [checkMap, setCheckMap] = useState<Record<number, any[]>>({});
  const [isTimeOver, setIsTimeOver] = useState<boolean>(false);
  const [taskConfirmedObj, setTaskConfirmedObj] = useLocalStorageState<TaskConfirmedObj>(
    'task_confirmed_obj',
    {
      defaultValue: {
        date: '',
        confirmedList: [],
      },
    },
  );
  const clearTaskConfirmedObj = () => {
    setTaskConfirmedObj({
      date: '',
      confirmedList: [],
    });
  };

  const [waitForSendNum, setWaitForSendNum] = useState<number>(chooseTask.waitReachPidNum || 0);

  useEffect(() => {
    setWaitForSendNum(chooseTask.waitReachPidNum || 0);
  }, [chooseTask]);

  const { modalProps, openModal, closeModal } = useModal({
    width: 850,
    title: (
      <Flex align="center">
        <div style={{ marginRight: 10 }}>{`${
          TIMED_TASK_TITLE_NAME[chooseTask.scene]
        }内容明细`}</div>
        <div style={{ fontSize: 13, fontWeight: 400 }}>待发送商户数: {waitForSendNum}</div>
      </Flex>
    ),
    footer: isTimeOver ? null : undefined,
    onOk: async () => {
      await confirmTaskSend();
      setLoading(true);
      setTimeout(() => {
        getTaskList().finally(() => {
          setLoading(false);
        });
      }, 2000);
    },
    onCancel: () => {
      setCheckMap({});
      setUnCheckMap({});
    },
  });

  const { data: taskList = [], runAsync: getTaskList } = useRequest(
    async (_taskConfirmedObj: TaskConfirmedObj = taskConfirmedObj) => {
      const res = await getTaskInfo();
      const list = res?.waitReachMerchantSummaryDTOList || [];
      const filteredList = list.filter((item) => {
        if (item.waitReachPidNum === 0 && !_taskConfirmedObj.confirmedList.includes(item.scene)) {
          return false;
        }
        return true;
      });

      return filteredList;
    },
    {
      manual: true,
      onSuccess: (data) => {
        if (data.length > 0) {
          data.forEach((task) => {
            traceExp(PageSPMKey.首页, ModuleSPMKey['企微任务.任务'], {
              scene: task.scene,
            });
          });
        }
      },
    },
  );

  const {
    run: getMerchantList,
    tableProps,
    pagination,
  } = useAntdTable(
    async ({ current, pageSize }, scene) => {
      if (!scene) {
        return {
          list: [],
          total: 0,
        };
      }
      const res = await getUndoTaskList({
        page: {
          pageSize,
          pageNo: current,
        },
        scene,
      });
      const dataList = res?.dataList || [];
      const selectList = [];
      dataList.forEach((item) => {
        if (!item.staffConfirmNoReachTag) selectList.push(item.merchantId);
      });
      setCheckMap({
        ...checkMap,
        [current]: selectList,
      });
      return {
        list: dataList,
        total: res?.pageInfo?.totalCount || 0,
      };
    },
    {
      manual: true,
      defaultPageSize: 10,
    },
  );

  const formatTime = (ms: number) => {
    if (ms <= 0) {
      return '00 : 00 : 00';
    }
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    const remainingSeconds = String(seconds % 60).padStart(2, '0');
    const remainingMinutes = String(minutes % 60).padStart(2, '0');
    const remainingHours = String(hours % 24).padStart(2, '0');

    return `${remainingHours} : ${remainingMinutes} : ${remainingSeconds}`;
  };

  async function confirmTaskSend() {
    const noNeedSendPidList = [];
    const needSendPidList = [];
    Object.keys(unCheckMap).forEach((key) => {
      noNeedSendPidList.push(...unCheckMap[key]);
    });
    Object.keys(checkMap).forEach((key) => {
      needSendPidList.push(...checkMap[key]);
    });
    await checkoutTask({
      needSendPidList,
      noNeedSendPidList,
    }).then(() => {
      setTaskConfirmedObj({
        date: nowDate,
        confirmedList: unique([...taskConfirmedObj.confirmedList, chooseTask.scene]),
      });
      message.success('操作成功');
    });
  }
  useMount(() => {
    if (nowDate !== taskConfirmedObj.date) {
      clearTaskConfirmedObj();
      getTaskList({ confirmedList: [], date: '' });
    } else {
      getTaskList();
    }
  });

  const handleTaskClick = (task: any) => {
    if (isTimeOver) return;

    // 点击埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['企微任务.自动发送'], {
      scene: task.scene,
    });

    getMerchantList(
      {
        pageSize: pagination.pageSize,
        current: 1,
      },
      task.scene,
    );
    setChooseTask(task);
    setWaitForSendNum(task.waitReachPidNum);
    openModal();
  };

  if (!taskList.length) {
    return null;
  }

  return (
    <div>
      <Spin spinning={loading}>
        <div className="task-container">
          <Space size="large" style={{ marginBottom: 8 }}>
            <div className="task-title">企微自动发送限时任务</div>
            <div className="count-down">
              <FieldTimeOutlined style={{ marginRight: 4 }} /> {formatTime(countdown)}
            </div>
            <div>
              {isTimeOver
                ? '倒计时已结束，关注群内商户回复'
                : '倒计时结束前请完成确认, 否则默认自动发送'}
            </div>
          </Space>

          <Space size="large" wrap>
            {taskList.map((item) => (
              <div className="task-card" key={item.scene}>
                {taskConfirmedObj.confirmedList.includes(item.scene) ? (
                  <CheckOutlined
                    style={{ color: isTimeOver ? '#d9d9d9' : '#1a66ff' }}
                    className="task-has-send-icon"
                  />
                ) : null}
                <p>{TIMED_TASK_TITLE_NAME[item.scene]}</p>
                <Flex justify="space-between" align="center">
                  <p className="wait-send-count">待发送商户数: {item.waitReachPidNum}</p>
                  <Button
                    size="middle"
                    style={{
                      backgroundColor: isTimeOver ? '#d9d9d9' : '#1a66ff',
                      color: '#ffffff',
                    }}
                    onClick={() => handleTaskClick(item)}
                  >
                    查看
                  </Button>
                </Flex>
              </div>
            ))}
          </Space>
        </div>
      </Spin>
      <Modal {...modalProps}>
        <Table
          columns={[
            {
              title: '商户PID',
              dataIndex: 'merchantId',
              render: (val, record) => {
                return (
                  <div>
                    <div>{val}</div>
                    {record.greyTag && !isTimeOver ? (
                      <div style={{ fontSize: '.7em', color: '#590c0c' }}>
                        近3日已发送, 本次不支持再发
                      </div>
                    ) : null}
                  </div>
                );
              },
            },
            {
              title: '商户名称',
              dataIndex: 'merchantName',
            },
            {
              title: '主门店名称',
              dataIndex: 'mainShopName',
            },
            isTimeOver
              ? {}
              : {
                  title: '操作',
                  dataIndex: 'merchantId',
                  render: (_, row) => (
                    <a
                      onClick={() => {
                        closeModal();
                        setMerchantData({
                          pid: row.merchantId,
                          merchantName: row.merchantName,
                        });
                        setDrawerVisible(true);
                      }}
                    >
                      查看喜报
                    </a>
                  ),
                },
          ]}
          rowKey={'merchantId'}
          {...tableProps}
          rowSelection={{
            selectedRowKeys: checkMap[pagination.current] || [],
            onChange: (selectedRowKeys) => {
              setCheckMap({
                ...checkMap,
                [pagination.current]: selectedRowKeys,
              });
              setUnCheckMap({
                ...unCheckMap,
                [pagination.current]: tableProps.dataSource
                  .filter((item) => !selectedRowKeys.includes(item.merchantId))
                  .map((item) => item.merchantId),
              });
              const defaultCheckedLength = tableProps.dataSource.filter(
                (item) => !item.staffConfirmNoReachTag,
              ).length;
              const checkedDelta = selectedRowKeys.length - defaultCheckedLength;
              setWaitForSendNum((chooseTask.waitReachPidNum || 0) + checkedDelta);
            },
            getCheckboxProps: (record) => ({
              disabled: isTimeOver || record.greyTag,
            }),
          }}
        />
      </Modal>
      {drawerVisible && (
        <WeeklyReportDrawer visible onClose={() => setDrawerVisible(false)} {...merchantData} />
      )}
    </div>
  );
};

export default TimedTaskModule;
