.outbound-detail-drawer {
  .ant-drawer-content-wrapper {
    min-width: 950px !important;
    width: 85% !important;
  }
  .ant-drawer-body {
    height: calc(100% - 56px) !important;
    display: flex;
    flex-direction: column;
  }
}
.outbound-detail-header {
  padding: 15px 0 0 15px;
  flex-shrink: 0;
}
.outbound-detail-content {
  margin: 15px 0 0 15px;
  display: flex;
  overflow: hidden;
  flex-grow: 1;

  .outbound-detail-left-panel {
    background-color: #fff;
    margin-right: 8px;
    width: 38%;
    min-width: 350px;
    border-radius: 8px;
    height: 100%;
    .left-panel-position {
      position: relative;
      height: 100%;
    }
  }

  .outbound-detail-right-panel {
    background-color: #fff;
    flex: 1;
    width: 0;
    padding: 12px;
  }

  .kb-visit-antd-table-tbody > tr > td {
    padding: 10px !important;
  }
}
