import { C33_CALL_ENUM, OUTBOUND_VISIT_RECORD_TARGET_TYPE, TEL_VISIT } from '@/common/const';
import { hidePhoneNum, getKpFlag } from '@/common/utils';
import { showVisitRecordFormPanel } from '@/components/visit-plan-agency/visit-record-form';
import { uuid } from '@alife/kb-biz-util';
import { CaretDownOutlined, PhoneFilled, PhoneOutlined, ShopOutlined } from '@ant-design/icons';
import { Flex, Dropdown, Tag, Modal, message } from 'antd';
import React from 'react';
import styled from 'styled-components';
import service from '@/_docplus/target/service/alsc-kbt-intergration-toolkit-client/CallCenterQueryGatewayService';
import { useDebounceFn } from 'ahooks';
import { callStatusChangeKey, echoWorkbench, TeleStatusEnum } from '@/common/echo';
import { useEventHelper } from '@/common/event';

interface IProps {
  shopInfo: {
    shopId: string;
    shopName: string;
    kbShopId: string;
    contactInfo?: Array<{
      contactNo: number;
      contactName: string;
      contactId: string;
      channel: string;
      roleDesc: string;
      isKp: string;
      role: string;
      supportCorrect: boolean;
    }>;
    address: string;
  };
  onCall: (value) => void;
}
const Container = styled.div`
  background: #fef7f2;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 7px;
  line-height: 25px;
`;
const ContactList = styled.div`
  user-select: none;
  margin-right: 5px;
  gap: 4px;
  display: flex;
  align-items: center;
`;
export default function ShopCard(props: IProps) {
  const { shopInfo, onCall } = props;
  const { shopId, shopName, contactInfo, address } = shopInfo;
  const contactList = contactInfo || [];
  const [selectedNo, setSelectedNo] = React.useState<any>(contactList?.[0]?.contactNo || null);
  useEventHelper(
    callStatusChangeKey,
    (status) => {
      if (status === TeleStatusEnum.RINGING) {
        showVisitRecordFormPanel({
          callRecordId: echoWorkbench?.echo?.recordId,
          contactScene: TEL_VISIT,
        });
      }
    },
    echoWorkbench,
  );
  /**
   * 拜访计划拨打
   * 1.先记拜访校验
   * 2.才是走外呼
   */
  const { run: call } = useDebounceFn(
    async () => {
      await service
        .getCallRecordMissingVisit({
          bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
          requestId: uuid(),
        })
        .then((res) => {
          if (res?.success) {
            if (res?.data?.length > 0) {
              // 表示有未记拜访的record，需要拦截
              Modal.warning({
                title: '你有未填写的记拜访，请填写完成后再继续拨打',
                width: 438,
                okText: '记拜访',
                onOk() {
                  showVisitRecordFormPanel({
                    callRecordId: res.data[0],
                    contactScene: TEL_VISIT,
                  });
                },
              });
            } else {
              const nextContact = contactList.find((item) => item.contactNo === selectedNo);
              const { contactId, contactName, supportCorrect, isKp, contactNo, roleDesc } =
                nextContact || {};
              onCall({
                phoneNumber: selectedNo,
                bizInfo: {
                  targetId: shopId,
                  bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
                  kpContactId: contactId,
                  targetType: OUTBOUND_VISIT_RECORD_TARGET_TYPE.AMAP_SHOP,
                  contactPerson: {
                    cpName: contactName,
                    supportCorrect,
                    cpPosition: roleDesc,
                    keyPersonFlag: getKpFlag(nextContact),
                    contactNo,
                  },
                },
              });
            }
          } else {
            // 报错
            message.error(res?.resultMessage);
          }
        })
        .catch((error) => {
          message.error(error?.errorMessage);
        });
    },
    {
      wait: 600,
      leading: true,
      trailing: false,
    },
  );
  return (
    <Container>
      <div>{shopName}</div>
      <div>门店 ID: {shopId}</div>
      <Flex align="center" gap={4}>
        <ShopOutlined /> {address}
      </Flex>
      <Flex
        align="center"
        gap={4}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Dropdown
          trigger={['click']}
          menu={{
            items: contactList.map((item) => ({
              label: (
                <span>
                  {hidePhoneNum(item.contactNo)}
                  {/* {item.roleDesc && <Tag style={{ marginLeft: 5 }}>{item.roleDesc}</Tag>} */}
                  {item.channel && <Tag style={{ marginLeft: 5 }}>{item.channel}</Tag>}
                </span>
              ),
              key: item.contactNo,
            })),
            onClick: (item) => {
              setSelectedNo(item.key);
            },
          }}
        >
          <ContactList>
            <PhoneOutlined />
            <div>{hidePhoneNum(selectedNo)}</div>
            <CaretDownOutlined style={{ marginLeft: 4 }} />
          </ContactList>
        </Dropdown>
        <PhoneFilled style={{ cursor: 'pointer' }} onClick={call} />
      </Flex>
    </Container>
  );
}
