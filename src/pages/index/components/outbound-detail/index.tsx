import React, { useState } from 'react';
import { But<PERSON>, Drawer, Flex } from 'antd';
import { debounce } from 'lodash';
import { OutBoundDetail } from './outbound-detail-content';
import './index.less';
import { Echo } from '@/components/echo';

interface IProps {
  visible: boolean;
  pid: string;
  merchantName: string;
  visitVisible?: boolean;
  boundDetailTabKey?: string;
  onClose: () => void;
  shopInfo?: any;
}

export const OutBoundDetailDrawer: React.FC<IProps> = ({
  visible,
  pid,
  merchantName,
  visitVisible,
  boundDetailTabKey,
  onClose,
  shopInfo,
}) => {
  const [drawerInit, setDrawerInit] = useState(true);

  const closeDrawer = () => {
    onClose();
    reloadDrawer();
  };

  /**
   * 强制刷新Drawer组件
   */
  const reloadDrawer = debounce(() => {
    setDrawerInit(false);
    setDrawerInit(true);
  }, 800);

  return (
    <>
      {drawerInit && (
        <Drawer
          title={
            <Flex align="center" justify="space-between">
              {boundDetailTabKey ? (
                '记拜访'
              ) : (
                <Flex align="center">
                  <div style={{ marginRight: 12 }}>电话沟通</div>
                  <Echo />
                </Flex>
              )}
              <Button size="large" type="text" onClick={closeDrawer}>
                关闭
              </Button>
            </Flex>
          }
          placement="right"
          size="large"
          width="100%"
          className="outbound-detail-drawer"
          styles={{
            body: {
              padding: 0,
              backgroundColor: '#f5f5f5',
              minWidth: 1110,
            },
          }}
          onClose={closeDrawer}
          open={visible}
          destroyOnClose
        >
          <OutBoundDetail
            pid={pid}
            merchantName={merchantName}
            visitVisible={visitVisible}
            boundDetailTabKey={boundDetailTabKey}
            shopInfo={shopInfo}
          />
        </Drawer>
      )}
    </>
  );
};
