/*
 * @Date: 2024-04-16 11:34:47
 * @LastEditTime: 2024-06-26 17:27:09
 * @LastEditors: 何赣湘
 */
import { useRequest } from 'ahooks';
import { message } from 'antd';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import { useStore } from '@/context/global-store';

export const useMerchantDetail = () => {
  const { viewer } = useStore();
  const {
    run: loadMerchantDetail,
    loading,
    data: merchantDetail,
  } = useRequest(
    async (params) => {
      const res = await service.queryMerchantDetail({
        ...params,
        viewOperatorId: viewer ? viewer : undefined,
      });
      if (!res.success) {
        message.error(res?.msgInfo || '系统异常，请稍后再试～');
        return {};
      }
      return res?.data || {};
    },
    {
      manual: true,
      onError: () => message.error('数据获取失败，请重试！'),
    },
  );

  return {
    loading,
    merchantDetail,
    loadMerchantDetail,
  };
};
