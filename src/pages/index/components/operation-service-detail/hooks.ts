import { useState } from 'react';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import { MerchantTaskDetailDTO } from '@/_docplus/target/types/amap-sales-operation-client';
import { OPERATING_TASK_DETAIL_STATUS_ENUM } from '@/common/const';
import { useStore } from '@/context/global-store';

export const useOperationServiceDetail = () => {
  const [finishedList, setFinishedList] = useState<MerchantTaskDetailDTO[] | null>([]);
  const [unfinishedList, setUnfinishedList] = useState<MerchantTaskDetailDTO[] | null>([]);
  const { viewer } = useStore();
  const {
    run: loadOperationServiceDetail,
    loading,
    error,
    data: operationServiceDetail,
  } = useRequest(
    async (params) => {
      const res = await service.queryMerchantTaskDetail({
        ...params,
        viewOperatorId: viewer ? viewer : undefined,
      });
      const { success, data, msgInfo: resultMessage } = res || {};
      if (!success) {
        message.error(resultMessage || '系统异常，请稍后再试～');
        setFinishedList([]);
        setUnfinishedList([]);
        return null;
      }
      const { merchantTaskDetailDTOS = [] } = data || {};
      if (merchantTaskDetailDTOS.length > 0) {
        const finishedData = merchantTaskDetailDTOS.filter(
          (item) => item.taskDetailStatus === OPERATING_TASK_DETAIL_STATUS_ENUM.FINISH,
        );
        const unfinishedData = merchantTaskDetailDTOS.filter(
          (item) => item.taskDetailStatus === OPERATING_TASK_DETAIL_STATUS_ENUM.WORKING,
        );
        setFinishedList(finishedData);
        setUnfinishedList(unfinishedData);
      }
      return data;
    },
    {
      manual: true,
    },
  );

  return {
    loading,
    error,
    operationServiceDetail,
    finishedList,
    unfinishedList,
    loadOperationServiceDetail,
  };
};
