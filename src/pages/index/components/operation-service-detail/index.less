.operation-service-detail-drawer {
  .ant-drawer-content-wrapper {
    min-width: 950px !important;
    width: 85% !important;
  }
  .ant-drawer-body {
    height: calc(100% - 57px);
  }

  .operation-service-detail-wrapper {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .operation-service-detail {
      padding: 0 15px;
    }
    .create-weekly-report-drawer {
      background-color: #fff;
      padding: 0 0 24px 24px;
      .ant-calendar-picker {
        width: 100%;
      }
    }
  }
  
  .operation-service-result {
    margin-top: 60px;
    .anticon-exclamation-circle {
      color: #fa5555 !important;
    }
  }
    
  .ant-drawer-wrapper-body {
    overflow: hidden;
  }
  
  .operation-service-title, .operation-service-subtitle {
    font-size: 16px;
    color: #000;
  }
  .operation-service-title-link {
    cursor: pointer;
    color: rgba(0, 0, 0, 0.45);
  }
  .operation-service-title-link:hover {
    color: #fa5555;
  }

  .empty {
    margin-top: 40px;
  }
}