import { Form } from 'antd';
import BdSelect from '@alife/mo-bd-select';
import React from 'react';
import { useStore } from '@/context/global-store';
import ServerController, { IPropsWithController } from '@/components/server-controller';
import { ActionButtonType } from '@/constants';
import Card from '@/components/card';
import { traceClick, PageSPMKey, ModuleSPMKey, traceExp } from '@/utils/trace';
import { useMount } from 'ahooks';

function Index(props: IPropsWithController) {
  const { viewer, setViewer, updateActions } = useStore() || {};
  useMount(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey.视角切换, {});
  });
  return (
    <Card style={{ marginBottom: 8 }}>
      <Form.Item label="切换视角" style={{ margin: 0 }}>
        <BdSelect
          // bizChannel="EVE_PC"
          type="ALL"
          placeholder="请输入小二名称、花名或工号"
          value={viewer}
          onChange={(val) => {
            traceClick(PageSPMKey.首页, ModuleSPMKey.视角切换, {
              viewType: val,
            });
            setViewer(val || undefined);
            updateActions(val);
          }}
          style={{ width: 260 }}
          disabled={props.greyButton}
        />
      </Form.Item>
    </Card>
  );
}

const SwitchViewer = ServerController(ActionButtonType.切换视角)(Index);
export default SwitchViewer;
