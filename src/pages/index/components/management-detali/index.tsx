/*
 * @Date: 2024-06-17 11:17:04
 * @LastEditTime: 2024-06-26 17:27:00
 * @LastEditors: 何赣湘
 */
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Drawer, Card, TableProps, Table, Spin, message } from 'antd';
import { useMerchantDetail } from '@/pages/index/components/outbound-detail/hooks';
import { getOperatingStateIconNew } from '@/common/utils';
import { apiShopStateDetail } from './services';
import './index.less';
import { useRequest } from 'ahooks';

interface DataType {
  name: string;
  age: number;
  address: string;
  tags: string[];
}

enum Direction {
  RED_LAMP = '(红灯)',
  YELLOW_LAMP = '(黄灯)',
  GREEN_LAMP = '(绿灯)',
  NOT_MATCH = '未匹配',
}

interface MerchantDetail {
  shopOpenNum: number;
  shopOptStateGreenNum: number;
  shopOptStateYellowNum: number;
  shopOptStateRedNum: number;
  shopOptStateNotMatchNum: number;
}

function ManagementDetail(props) {
  const [pageParams, setPageParams] = useState({ pageNo: 1, pageSize: 10 });
  const { pid, setOpenManagementDetali } = props;

  const onChange = useCallback((page, pageSize) => {
    setPageParams({ pageNo: page, pageSize });
  }, []);

  const onClose = () => {
    setOpenManagementDetali(false);
  };
  const { loading, merchantDetail = {}, loadMerchantDetail } = useMerchantDetail();

  const {
    shopOpenNum,
    shopOptStateGreenNum,
    shopOptStateYellowNum,
    shopOptStateRedNum,
    shopOptStateNotMatchNum,
  } = merchantDetail as MerchantDetail;

  useEffect(() => {
    loadMerchantDetail({ pid, shopStateCountQuery: true });
  }, [loadMerchantDetail, pid]);

  const {
    loading: shopStateDetailLoading,
    data: tableData,
    run: getTableData,
  } = useRequest(
    async (params) => {
      const res = await apiShopStateDetail(params);
      return res?.data ?? {};
    },
    {
      manual: true,
      onError: () => message.error('数据获取失败，请重试！'),
    },
  );
  const { dataList = [], pageInfo } = tableData || {};
  useEffect(() => {
    const params = {
      pid,
      page: pageParams,
    };
    getTableData(params);
  }, [pid, pageParams]);

  const lightArr = useMemo(() => {
    const arr = [
      { num: shopOptStateGreenNum, code: 'GREEN_LAMP' },
      { num: shopOptStateYellowNum, code: 'YELLOW_LAMP' },
      { num: shopOptStateRedNum, code: 'RED_LAMP' },
      { num: shopOptStateNotMatchNum, code: 'NOT_MATCH' },
    ].filter((item) => item.num);
    return arr;
  }, [shopOptStateGreenNum, shopOptStateYellowNum, shopOptStateRedNum, shopOptStateNotMatchNum]);

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '门店ID',
      dataIndex: 'shopId',
      width: 200,
    },
    {
      title: '门店名称',
      dataIndex: 'shopName',
      width: 200,
      render: (value) => value || '--',
    },
    {
      title: '门店类型',
      dataIndex: 'shopCategory',
      render: (value) => value || '--',
    },
    {
      title: '门店经营状态',
      dataIndex: 'shopOperationState',
      render: (value) => {
        return (
          <>
            {value ? (
              <>
                <img style={{ width: 20 }} src={getOperatingStateIconNew(value)} />
                {Direction[value]}
              </>
            ) : (
              '-'
            )}
          </>
        );
      },
    },
    {
      title: '门店商家分',
      dataIndex: 'shopQualityScore',
      render: (value) => (value ? `${value}分` : '--'),
    },
    {
      title: '日均凭证数',
      dataIndex: 'shopPingzheng',
      render: (value) => value || '--',
    },
    {
      title: '日均流量',
      dataIndex: 'shopTraffic',
      render: (value) => value || '--',
    },
  ];

  return (
    <Drawer
      title="经营状态详情"
      onClose={onClose}
      open
      drawerStyle={{ backgroundColor: '#f5f5f5' }}
      width="80%"
    >
      <Spin spinning={loading || shopStateDetailLoading}>
        <Card title="概览" size="small">
          <div>PID: {pid}</div>
          <div className="shop-num-wrap">
            <div>{shopOpenNum && <>门店总数：{shopOpenNum} 家</>}</div>
            <div className="shop-num">
              {lightArr.map((item) => (
                <div className="num">
                  {item.code !== 'NotMatchNum' ? (
                    <>
                      <img
                        className="operating-state-img"
                        src={getOperatingStateIconNew(item.code)}
                      />
                      <span>
                        {Direction[item.code]}:{item?.num} 家
                      </span>
                    </>
                  ) : (
                    <span>未匹配: {item?.num} 家</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        </Card>
        <Card title="明细" style={{ marginTop: 10 }} size="small">
          <Table
            rowKey="shopId"
            columns={columns}
            dataSource={dataList}
            pagination={{
              pageSize: pageParams.pageSize,
              current: pageParams.pageNo,
              onChange,
              total: pageInfo?.totalCount,
              showSizeChanger: true,
              onShowSizeChange: onChange,
            }}
          />
        </Card>
      </Spin>
    </Drawer>
  );
}

export default memo(ManagementDetail);
