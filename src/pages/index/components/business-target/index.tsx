import Card from '@/components/card';
import { Flex, Tooltip } from 'antd';
import './index.less';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { getQueryTargetsCluster } from './service';
import { useState } from 'react';
import CompletionRateCard from '@/components/completion-rate-card';

interface IProps {}
export enum MerchantStatus {
  ANNUAL_FEE = '年费续签率',
  IN_COME = '收入完成率',
  AD_RENEW = '广告续充率',
}

export default function BusinessTarget(props: IProps) {
  const [updateDsDesc, setUpdateDsDesc] = useState<string>('');
  const [tips, SetTips] = useState<string>('');
  const { data: dataList = [] } = useRequest(async () => {
    const res = await getQueryTargetsCluster({});
    if (res) {
      setUpdateDsDesc(res?.updateDsDesc);
      SetTips(res?.tips);
    }
    return res?.dataList || [];
  });

  if (!dataList.length) return null;
  return (
    <div className="business-target">
      <div className="business-target-title">
        <div className="business-target-title-text">目标仪表盘</div>
        {updateDsDesc && (
          <div className="business-target-title-sub">
            <span>{updateDsDesc?.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')}</span>
            <Tooltip title={tips || ''}>
              <QuestionCircleOutlined style={{ marginLeft: 8, color: '#999' }} />
            </Tooltip>
          </div>
        )}
      </div>
      <Card>
        <Flex>
          {dataList?.map((item) => (
            <CompletionRateCard
              key={item.code}
              title={MerchantStatus[item?.code] || '-'}
              rate={item?.rate}
              tips={item?.tips}
              targetValue={item?.targetValue}
            />
          ))}
        </Flex>
      </Card>
    </div>
  );
}
