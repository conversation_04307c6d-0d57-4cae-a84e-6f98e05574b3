import React from 'react';
import { Drawer } from 'antd';
import AlertTaskTable from './alert-task-table';
import { IModalProps } from '@/hooks/useModal';

export const AlertTaskDrawer = (
  props: IModalProps<{
    pid: string;
    defaultTab?: string;
  }>,
) => {
  return (
    <Drawer {...props} title="预警任务" placement="right" width={830}>
      <AlertTaskTable pid={props.data.pid} defaultTab={props.data.defaultTab} />
    </Drawer>
  );
};
