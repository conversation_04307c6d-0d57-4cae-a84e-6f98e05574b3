import React, { useState } from 'react';
import { Space, Table, TableColumnType, Tabs, Badge, Tooltip, Divider } from 'antd';
import Link from '@/components/Link';
import { useAntdTable } from 'ahooks';
import { queryWarningTaskList, warningTaskColorMap, WarningTaskStatusMap } from '@/services';
import FormModal from './form-modal';
import useModal from '@/hooks/useModal';
import { IfButtonShow } from '@/components/server-controller/useAction';
import { IAction } from '@/types';
import Checkmodal from './check-modal';
import dayjs from 'dayjs';

interface IProps {
  pid: string;
  defaultTab: string;
}

interface TabItem {
  key: string;
  label: string;
}
const items: TabItem[] = [
  {
    key: 'EXTREME_VALUE_WARNING',
    label: '极值预警',
  },
  {
    key: 'NEGATIVE_FEEDBACK_WARNING',
    label: '负向反馈预警',
  },
];

const AlertTaskTable = (props: IProps) => {
  const [activeTab, setActiveTab] = useState(props.defaultTab || items[0].key);
  const { modalProps: modalPropsForComplete, updateAndOpen: updateAndOpenForComplete } = useModal<{
    warningTaskNo?: any;
  }>();
  const { modalProps: modalPropsForView, updateAndOpen: updateAndOpenForView } = useModal<{
    warningTaskHandleRecords?: any[];
  }>();
  const { loading, data, pagination, run } = useAntdTable(
    async ({ current, pageSize }) => {
      if (!props.pid) {
        return {
          total: 0,
          list: [],
        };
      }
      const res = await queryWarningTaskList({
        pid: props.pid,
        warningTaskType: activeTab,
        page: { pageNo: current, pageSize },
      });
      return {
        total: res?.pageInfo?.totalCount || 0,
        list: res?.dataList || [],
      };
    },
    {
      refreshDeps: [props.pid, activeTab],
    },
  );

  const handleActionClick = (action: IAction, record: any) => {
    switch (action.buttonCode) {
      case 'TO_COMPLETE':
        updateAndOpenForComplete({ warningTaskNo: record.warningTaskNo });
        break;
      case 'VIEW':
        updateAndOpenForView({ warningTaskHandleRecords: record.warningTaskHandleRecords });
        break;
      default:
        break;
    }
  };

  const columns: TableColumnType[] = [
    {
      title: '预警时间',
      dataIndex: 'warningTime',
      key: 'warningTime',
      align: 'center',
      width: 140,
      render: (value: string) => <div>{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</div>,
    },
    {
      title: '预警原因',
      dataIndex: 'warningContent',
      key: 'warningContent',
      width: 440,
      render: (value: string[] = []) => {
        return (
          <div>
            {value.map((item, index) => (
              <>
                {index > 0 && <Divider style={{ margin: '12px 0' }} />}
                <Tooltip
                  title={activeTab === 'NEGATIVE_FEEDBACK_WARNING' ? item : ''}
                  overlayInnerStyle={{
                    maxHeight: 400,
                    overflowY: 'auto',
                    wordBreak: 'break-all',
                    whiteSpace: 'pre-wrap',
                    textWrap: 'wrap',
                  }}
                >
                  <div
                    style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                    }}
                  >
                    {item || '暂无数据'}
                  </div>
                </Tooltip>
              </>
            ))}
          </div>
        );
      },
    },
    {
      title: '任务状态',
      dataIndex: 'warningTaskStatus',
      key: 'warningTaskStatus',
      align: 'center',
      width: 140,
      render: (value: WarningTaskStatusMap) => {
        return (
          <>
            <Badge color={warningTaskColorMap[value]} text={WarningTaskStatusMap[value]} />{' '}
          </>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'actions',
      key: 'actions',
      width: 140,
      align: 'center',
      render: (actions: IAction[] = [], record: any) => (
        <Space>
          {actions.map((action) => {
            return (
              <IfButtonShow button={action} key={action.buttonCode}>
                <Link
                  disabled={action.greyButton}
                  onClick={() => handleActionClick(action, record)}
                  to=""
                >
                  {action.buttonName}
                </Link>
              </IfButtonShow>
            );
          })}
        </Space>
      ),
    },
  ];

  const onTabChange = (key: string) => {
    setActiveTab(key);
  };
  const reloadData = () => {
    run({ ...pagination, current: 1 });
  };
  return (
    <>
      <Tabs activeKey={activeTab} items={items} onChange={onTabChange} />
      <Table
        bordered
        columns={columns}
        dataSource={data?.list || []}
        loading={loading}
        pagination={pagination}
      />
      <FormModal {...modalPropsForComplete} pid={props.pid} reloadData={reloadData} />
      <Checkmodal {...modalPropsForView} pid={props.pid} />
    </>
  );
};

export default AlertTaskTable;
