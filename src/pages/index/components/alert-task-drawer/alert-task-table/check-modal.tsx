import { IModalProps } from '@/hooks/useModal';
import { warningTaskColorMap } from '@/services';
import { List, Modal, Image, Flex } from 'antd';
import dayjs from 'dayjs';

export default function Checkmodal(
  props: IModalProps<{ warningTaskHandleRecords?: any[] }, { pid: string }>,
) {
  return (
    <Modal
      title="查看任务"
      styles={{
        body: {
          maxHeight: 600,
          maxWidth: 500,
          overflowY: 'auto',
        },
      }}
      {...props}
    >
      <List
        size="large"
        dataSource={props.data?.warningTaskHandleRecords || []}
        renderItem={(record: any) => {
          return (
            <List.Item style={{ lineHeight: '30px', display: 'block' }}>
              <Flex justify="space-between">
                <div> {dayjs(record.handleTime).format('YYYY-MM-DD HH:mm:ss')} </div>
                <div style={{ color: warningTaskColorMap[record.warningTaskStatus] }}>
                  {record.warningTaskStatusDesc}
                </div>
              </Flex>
              <div>原因分析：{record.warningReason}</div>
              <div>解决方案：{record.solution}</div>
              <div>附件: </div>
              <Flex gap={10}>
                {record.attachment?.map((item: any, index: number) => (
                  <div key={index}>
                    {item.type === 'PICTURE' && item.content ? (
                      <Image src={item.content} width={80} height={80} />
                    ) : null}
                  </div>
                ))}
              </Flex>
            </List.Item>
          );
        }}
      />
    </Modal>
  );
}
