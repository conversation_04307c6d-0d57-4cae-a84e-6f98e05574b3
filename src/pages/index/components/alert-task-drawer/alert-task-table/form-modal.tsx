import { IModalProps } from '@/hooks/useModal';
import { submitWarningTask, WarningTaskStatusMap } from '@/services';
import { useRequest } from 'ahooks';
import { Form, Input, message, Modal, Select } from 'antd';
import UploadOss from '@/components/upload-oss';
import { useState } from 'react';

type FormModalProps = IModalProps<
  { warningTaskNo?: string },
  { pid: string; reloadData?: () => void; onClose?: () => void }
>;

const FormModal: React.FC<FormModalProps> = (props) => {
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);

  const { loading, runAsync } = useRequest(
    async (params) => {
      const res = await submitWarningTask({
        ...params,
        // eslint-disable-next-line react/prop-types
        pid: props.pid,
        // 调试入参
        // commonOperatorInfo: {
        //   operatorType: 'SALES',
        //   class: 'com.amap.sales.operation.client.common.CommonOperatorInfo',
        //   operatorId: '3022000000002401',
        // },
      });
      return res;
    },
    {
      debounceWait: 2000,
      debounceLeading: true,
      manual: true,
      onSuccess: () => {
        message.success('提交成功');
        // eslint-disable-next-line react/prop-types
        props.reloadData?.();
        form.resetFields();
        // eslint-disable-next-line react/prop-types
        props.onClose?.();
      },
    },
  );

  const submit = async () => {
    const values = await form.validateFields();
    await runAsync({
      ...values,
      attachment: values.attachment.map((item: any) => ({
        type: 'PICTURE',
        content: item.url,
      })),
      // eslint-disable-next-line react/prop-types
      warningTaskNo: props.data.warningTaskNo,
    });
  };
  return (
    <Modal
      title="预警任务处理"
      {...props}
      onOk={submit}
      confirmLoading={loading || uploading}
      okText="提交"
    >
      <Form form={form} onFinish={submit}>
        <Form.Item
          label="原因分析"
          name="warningReason"
          labelCol={{ span: 4 }}
          rules={[{ required: true, message: '请输入原因分析' }]}
        >
          <Input.TextArea maxLength={500} style={{ height: '100px' }} />
        </Form.Item>
        <Form.Item
          label="解决方案"
          name="solution"
          labelCol={{ span: 4 }}
          rules={[{ required: true, message: '请输入解决方案' }]}
        >
          <Input.TextArea maxLength={500} style={{ height: '100px' }} />
        </Form.Item>
        <Form.Item
          label="任务状态"
          name="warningTaskStatus"
          labelCol={{ span: 4 }}
          rules={[{ required: true, message: '请选择任务状态' }]}
        >
          <Select
            options={[
              {
                value: WarningTaskStatusMap.未处理,
                label: WarningTaskStatusMap[WarningTaskStatusMap.未处理],
              },
              {
                value: WarningTaskStatusMap.处理中,
                label: WarningTaskStatusMap[WarningTaskStatusMap.处理中],
              },
              {
                value: WarningTaskStatusMap.已完成,
                label: WarningTaskStatusMap[WarningTaskStatusMap.已完成],
              },
              {
                value: WarningTaskStatusMap.无法处理,
                label: WarningTaskStatusMap[WarningTaskStatusMap.无法处理],
              },
            ]}
            style={{ width: '50%' }}
          />
        </Form.Item>
        <Form.Item
          label="附件"
          name="attachment"
          valuePropName="fileList"
          labelCol={{ span: 4 }}
          initialValue={[]}
        >
          <UploadOss
            setUploading={setUploading}
            multiple
            fileSizeLimit={20}
            imgSize={50}
            maxCount={5}
          >
            <a>上传图片</a>
          </UploadOss>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default FormModal;
