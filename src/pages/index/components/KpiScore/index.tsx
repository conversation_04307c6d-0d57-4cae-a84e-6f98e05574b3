import React, { memo } from 'react';
import { withKpiReportPlanInfoFetcher, KpiScoreDetail } from '@alife/amap-kpi-shared-components';
import { jumpOtherUrl } from '@/common/utils';
import './index.less';
import Card from '@/components/card';
/**
 * 绩效概览
 */
const KpiScore: React.FC = () => {
  const KpiScoreDetailWithData = withKpiReportPlanInfoFetcher(KpiScoreDetail);
  const onClick = () => {
    jumpOtherUrl({
      url: 'xy-kbjxzb',
      newUrl: 'sale-pc/amap-kpi-report-pc/',
    });
  };
  return (
    <Card title="绩效概览">
      <KpiScoreDetailWithData onClick={onClick} />
    </Card>
  );
};
export default memo(KpiScore);
