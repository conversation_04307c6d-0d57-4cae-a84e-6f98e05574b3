import React, { PropsWithChildren, useState, useRef } from 'react';
import { TASK_TABPAN_ENUM } from '@/common/const';
import { uuid } from '@alife/kb-biz-util';

interface TaskItem {
  id: string;
  type: string; // MERCHANT 或 SHOP
  execute: () => Promise<void>;
}

interface IDoubleTableStore {
  curTab: string;
  setCurTab: React.Dispatch<React.SetStateAction<string>>;
  isExpanded: boolean;
  setIsExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  taskQueue: TaskItem[];
  setTaskQueue: React.Dispatch<React.SetStateAction<TaskItem[]>>;
  addTask: (type: string, task: () => Promise<void>) => void;
  clearTaskQueue: (type?: string) => void;
  merchantRef: React.MutableRefObject<any>;
  shopRef: React.MutableRefObject<any>;
  tabsRef: React.MutableRefObject<HTMLDivElement | null>;
  scrollToTabs: () => void;
}

const DoubleTableContext = React.createContext<IDoubleTableStore>({} as any);

export const DoubleTableProvider = (props: PropsWithChildren<any>) => {
  const [curTab, setCurTab] = useState(TASK_TABPAN_ENUM.MERCHANT);
  const [isExpanded, setIsExpanded] = useState(false);
  const [taskQueue, setTaskQueue] = useState<TaskItem[]>([]);

  const merchantRef = useRef<any>(null);
  const shopRef = useRef<any>(null);
  const tabsRef = useRef<HTMLDivElement>(null);

  const scrollToTabs = () => {
    if (tabsRef.current) {
      tabsRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  const addTask = (type: string, task: () => Promise<void>) => {
    const taskId = uuid();
    // 包装任务，执行完后自动清除当前任务
    const wrappedTask = async () => {
      setTaskQueue((prev) => prev.filter((t) => t.id !== taskId));
      await task();
    };

    const taskItem: TaskItem = {
      id: taskId,
      type,
      execute: wrappedTask,
    };

    // 先清除同类型的旧任务，再添加新任务，确保只执行最新的任务
    setTaskQueue((prev) => {
      const filteredQueue = prev.filter((existingTask) => existingTask.type !== type);
      return [...filteredQueue, taskItem];
    });
  };

  const clearTaskQueue = (type?: string) => {
    if (type) {
      // 清除指定类型的任务
      setTaskQueue((prev) => prev.filter((task) => task.type !== type));
    } else {
      // 清除所有任务
      setTaskQueue([]);
    }
  };

  return (
    <DoubleTableContext.Provider
      value={{
        curTab,
        setCurTab,
        isExpanded,
        setIsExpanded,
        taskQueue,
        setTaskQueue,
        addTask,
        clearTaskQueue,
        merchantRef,
        shopRef,
        tabsRef,
        scrollToTabs,
      }}
    >
      {props.children}
    </DoubleTableContext.Provider>
  );
};

export const useDoubleTableStore = () => React.useContext<IDoubleTableStore>(DoubleTableContext);
