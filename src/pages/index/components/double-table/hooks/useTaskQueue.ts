import { useEffect, useMemo } from 'react';
import { useDoubleTableStore } from '../double-table-store';

/**
 * 处理特定类型任务队列的 hooks
 * @param taskType 任务类型 (MERCHANT 或 SHOP)
 */
export const useTaskQueue = (taskType: string) => {
  const { taskQueue } = useDoubleTableStore();
  const tasks = useMemo(
    () => taskQueue.filter((task) => task.type === taskType),
    [taskQueue, taskType],
  );

  useEffect(() => {
    if (tasks.length > 0) {
      // 执行对应类型的任务
      tasks.forEach((task) => task.execute());
    }
  }, [taskQueue, taskType]);
  return {
    hasTask: tasks.length > 0,
    tasks,
  };
};
