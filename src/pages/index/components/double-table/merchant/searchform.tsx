import React, { useMemo } from 'react';
import { Button, Input, Select, Form, FormInstance, Flex } from 'antd';
import { createPortal } from 'react-dom';
import {
  MERCHANT_POTENTIAL_OPTIONS,
  MERCHANT_TAG_OPTIONS,
  SERVICE_SATISFACTION_OPTIONS,
  TASK_LABEL_OPTIONS,
  TASK_TYPE_MAP,
  TASK_TYPE_ENUM,
} from '@/common/const';
import { isAgent } from '@/common/utils';
import { useMount } from 'ahooks';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';

const FormItem = Form.Item;

export interface IMerchantData {
  pid: string;
  merchantName: string;
}

interface IProps {
  onSearch: () => void;
  form: FormInstance;
  isShow?: boolean;
  loading?: boolean;
}

export default function MerchantSearchForm(props: IProps) {
  const { onSearch, form, isShow = true, loading = false } = props;

  // 获取预警任务筛选项，直营用户过滤门店留存选项
  const warningTaskOptions = useMemo(() => {
    const isChannelUser = isAgent();
    return TASK_TYPE_MAP.filter((item) => {
      // 如果是直营用户，过滤掉“门店留存未达标商户”选项
      if (!isChannelUser && item.value === TASK_TYPE_ENUM.CONSUME_RETENTION_WARING) {
        return false;
      }
      return true;
    });
  }, []);

  const handleReset = () => {
    form.resetFields();
    onSearch();
    // 发送清除选择事件
    emitter.emit(EmitterEventMap.ClearSelection);
  };

  useMount(() => {
    // 组件挂载完成
  });

  // Portal内容 - 使用useMemo优化性能
  const portalContent = useMemo(() => {
    const portalContainer = document.getElementById('search-form-portal');
    if (!portalContainer) return null;
    return createPortal(
      <Flex align="center" gap={8}>
        <Form.Item name="merchantName" label="商户名称" style={{ marginBottom: 0 }}>
          <Input placeholder="请输入商户名称" allowClear />
        </Form.Item>
        <Form.Item
          label="PID"
          name="pid"
          style={{ marginBottom: 0 }}
          normalize={(value) => {
            if (value) {
              return value.replace(/，/g, ',');
            }
          }}
          rules={[
            {
              validator: (rule, value, callback) => {
                const pidArray = value && value.split(/\s|,/);
                if (pidArray?.length > 200) {
                  callback('最多支持200个pid输入');
                } else {
                  callback();
                }
              },
            },
          ]}
        >
          <Input placeholder="支持多个pid输入,用,或空格分割" allowClear />
        </Form.Item>
        {!isShow && (
          <>
            <Button onClick={handleReset} disabled={loading}>
              重置
            </Button>
            <Button type="primary" onClick={() => onSearch()} loading={loading}>
              查询
            </Button>
          </>
        )}
      </Flex>,
      portalContainer,
    );
  }, [isShow, handleReset, onSearch, loading]);

  if (!isShow) return <>{portalContent}</>;

  return (
    <>
      {portalContent}
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: '5px 12px',
        }}
      >
        <FormItem hidden name="pid" />
        <FormItem label="商户推荐分析" name="highPotentialValues">
          <Select
            mode="multiple"
            allowClear
            placeholder="请选择"
            options={MERCHANT_POTENTIAL_OPTIONS.map((item) => ({
              value: item.value,
              label: item.title,
            }))}
          />
        </FormItem>
        <FormItem label="商户标签" name="merchantLevels" initialValue={[]}>
          <Select
            mode="multiple"
            allowClear
            placeholder="请选择"
            options={MERCHANT_TAG_OPTIONS.map((item) => ({
              value: item.key,
              label: item.title,
            }))}
          />
        </FormItem>
        <FormItem label="服务满意度" name="serviceSatisfaction">
          <Select
            mode="multiple"
            allowClear
            placeholder="请选择"
            options={SERVICE_SATISFACTION_OPTIONS.map((item) => ({
              value: item.key,
              label: item.title,
            }))}
          />
        </FormItem>
        <FormItem label="广告任务" name="adTaskLabels" initialValue={[]}>
          <Select
            placeholder="请选择"
            mode="multiple"
            allowClear
            options={TASK_LABEL_OPTIONS.map((item) => ({
              value: item.value,
              label: item.title,
            }))}
          />
        </FormItem>
        <FormItem label="预警任务" name="warningTaskLabels">
          <Select
            options={warningTaskOptions.map((item) => ({
              value: item.value,
              label: item.title,
            }))}
            placeholder="请选择"
            allowClear
          />
        </FormItem>
      </div>

      <Flex justify="flex-end" align="center" gap={8}>
        <Button onClick={() => handleReset()} disabled={loading}>
          重置
        </Button>
        <Button type="primary" onClick={() => onSearch()} loading={loading}>
          查询
        </Button>
      </Flex>
    </>
  );
}
