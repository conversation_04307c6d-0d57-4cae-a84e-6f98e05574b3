.task-list-content {
  // background-color: #f5f5f5;
  border-radius: 2px;

  .task-operation {
    a:first-child {
      margin-right: 16px;
    }
  }

  .shop-labels-tag {
    margin-top: 6px;
  }

  .pay-btn {
    display: flex;
    .copy-text {
      cursor: pointer;
      user-select: none;
      color: #1890ff;
      img {
        width: 18px;
        height: 18px;
      }
    }
    .copy-text:first-child {
      margin-right: 12px;
    }
  }

  .merchant-intention {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    cursor: pointer;
  }

  .suggestion-btn {
    border-radius: 4px;
    color: #108ee9;
    border: 1px solid #108ee9;
    text-align: center;
    cursor: pointer;
    user-select: none;
  }
  .suggestion-btn:not(:first-child) {
    margin-top: 6px;
  }

  .select-highlight-row {
    background-color: #fa555524;
  }
}

.ant-table {
  z-index: 0;
}
