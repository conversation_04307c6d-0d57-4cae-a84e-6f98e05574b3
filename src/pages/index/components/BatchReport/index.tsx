import BdTextTpl from '@/components/bd-text-tpl';
import { <PERSON><PERSON>, Drawer, Form, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { FROM_PAGE } from '@/common/const';
import ServerController, { IPropsWithController } from '@/components/server-controller';
import { ActionButtonType } from '@/constants';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';

type IPidList = Array<{ id: string; name: string }>;
function MerchantList(props: { list: IPidList }) {
  const { list = [] } = props;
  if (!list.length) return <p style={{ opacity: 0.7 }}>请先在商户列表选择商户</p>;
  return (
    <Tooltip
      title={
        <div style={{ maxHeight: 3000, overflowY: 'auto', lineHeight: '16px' }}>
          {list.map((item) => (
            <div>{item.name}</div>
          ))}
        </div>
      }
    >
      <span>您将发送消息给{list.length}个商户</span>
    </Tooltip>
  );
}

const ReportDrawer = (props: {
  open: boolean;
  onClose: () => void;
  pidList: IPidList;
  disabled?: boolean;
}) => {
  const { open, onClose, pidList, disabled } = props;
  const [hasSended, setHasSended] = useState(false);
  const handleClose = (_hasSended?: boolean) => {
    onClose();
    if (_hasSended || hasSended) {
      emitter.emit(EmitterEventMap.AfterBatchReport);
    }
  };

  useEffect(() => {
    if (open) {
      setHasSended(false);
    }
  }, [open]);
  if (!open) {
    return null;
  }

  return (
    <Drawer open={open} onClose={handleClose} width="80%" destroyOnClose>
      <Form>
        <Form.Item label="发送范围" required>
          <MerchantList list={pidList} />
        </Form.Item>
        <Form.Item label="消息内容" required>
          <BdTextTpl
            disabled={disabled}
            isBatch
            showSend
            afterSend={() => {
              setHasSended(true);
            }}
            sendParams={{
              from: FROM_PAGE.MERCHANT_LIST,
              merchantNewsReachModelList: pidList.map((item) => ({
                pid: item.id,
              })),
            }}
            onClose={handleClose}
          />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
interface IProps {
  pidList: IPidList;
}
function Index(props: IPropsWithController<IProps>) {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          setOpen(true);
        }}
        disabled={!!props.greyButton}
      >
        {props.buttonText || '一键发送企微群'}
      </Button>
      <ReportDrawer
        open={open}
        pidList={props.pidList}
        onClose={() => {
          setOpen(false);
        }}
        disabled={!props.pidList.length}
      />
    </>
  );
}
const BatchReport = ServerController<IProps>(ActionButtonType.一键发送企微群)(Index);
export default BatchReport;
