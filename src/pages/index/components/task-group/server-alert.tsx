import Card from '@/components/card';
// import SimpleCard from './components/simple-card';
import { getTaskData } from '@/services';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { TASK_TABPAN_ENUM } from '@/common/const';
import { TaskCard } from './components/taskCard';

interface IProps {
  style?: React.CSSProperties;
}
export default function ServerAlert(props: IProps) {
  const { style = {} } = props;
  const getData = async () => {
    const res = await getTaskData('预警任务');
    const list = res.warningNums || [];
    return list.map((item) => ({
      ...item,
      label: `${item.property}${item.rightSymbol || ''}${item.rightBoundary || ''} ${
        item.unit || ''
      }`,
      value: item.value,
      desc: item.desc,
    }));
  };
  return (
    <Card title="预警任务" style={style}>
      <TaskCard
        column={1}
        getData={getData}
        onItemClick={(data) => {
          if (data.codes?.length) {
            emitter.emit(EmitterEventMap.TaskDataClick, {
              type: TASK_TABPAN_ENUM.MERCHANT,
              params: {
                filterOptRelation: true,
                warningTaskLabels: data.codes[0],
              },
            });
          }
        }}
      />
    </Card>
  );
}
