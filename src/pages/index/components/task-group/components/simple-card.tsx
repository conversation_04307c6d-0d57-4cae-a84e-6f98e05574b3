import { sendEvent } from '@/common/utils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Flex, List, Tooltip } from 'antd';
import styled from 'styled-components';

interface IProps {
  onItemClick?: (data: any) => void;
  column?: number;
  getData: () => Promise<
    Array<{
      label: string;
      value: string;
      desc?: string;
      codes?: string[];
    }>
  >;
}
const ListItem = styled.div`
  padding: 5px 10px;
  text-align: center;
  border: 1px solid #e7e7e7;
  border-radius: 2px;
  &:hover {
    cursor: pointer;
    opacity: 0.8;
    background: #f5f5f5;
  }
`;
export default function SimpleCard(props: IProps) {
  const { getData, column } = props;
  const { error, refresh, data: cardData, loading } = useRequest(getData);
  const handleItemClick = (data) => {
    props.onItemClick?.(data);
    sendEvent('TO_DO_TASK', 'CLK', {
      c1: data.label,
    });
  };
  return (
    <List
      dataSource={error ? [] : cardData || []}
      split={false}
      itemLayout="horizontal"
      locale={{
        emptyText: error ? (
          <span>
            数据请求失败, <a onClick={refresh}>点击重试</a>
          </span>
        ) : (
          <span>暂无数据</span>
        ),
      }}
      grid={{ gutter: 10, column }}
      loading={loading}
      renderItem={(data) => {
        return (
          <List.Item onClick={() => handleItemClick?.(data)} style={{ width: 170 }}>
            <ListItem>
              <Flex align="center" justify="center" gap={7} style={{ marginBottom: 7 }}>
                <div>{data.label}</div>
                {data.desc && (
                  <Tooltip title={data.desc}>
                    <QuestionCircleOutlined />
                  </Tooltip>
                )}
              </Flex>
              <div>{data.value}</div>
            </ListItem>
          </List.Item>
        );
      }}
    />
  );
}
