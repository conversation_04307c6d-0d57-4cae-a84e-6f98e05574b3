import React, { useEffect, useState } from 'react';
import { Modal } from 'antd';
import './index.less';
import { getStorage, setStorage } from '@/common/utils';

const NOVICE_GUIDANCE_SHOWN = 'NOVICE_GUIDANCE_SHOWN';

interface IProps {}

export const NoviceGuidance: React.FC<IProps> = () => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const shown = getStorage(NOVICE_GUIDANCE_SHOWN);
    if (!shown) {
      setVisible(true);
    }
  }, []);

  const handleClick = () => {
    setVisible(false);
    setStorage(NOVICE_GUIDANCE_SHOWN, true);
  };

  return (
    <Modal
      title="代运营管理上线"
      okText="去体验"
      cancelText="我知道了"
      open={visible}
      maskClosable={false}
      width={680}
      onOk={handleClick}
      onCancel={handleClick}
    >
      <img
        className="novice-guidance-image"
        src="https://img.alicdn.com/imgextra/i3/O1CN01BbMUlm1u3LipP9I2V_!!6000000005981-2-tps-1224-454.png"
        alt=""
      />
    </Modal>
  );
};
