/* eslint-disable no-nested-ternary */
import { IModalProps } from '@/hooks/useModal';
import { <PERSON><PERSON>, <PERSON><PERSON>, Flex, <PERSON><PERSON>ip, Spin } from 'antd';
import styled from 'styled-components';
import TaskCard from './card';
import { useAntdTable } from 'ahooks';
import { getQueryMerchantPriorityTaskInfo, pushTodoState } from '@/services';
import TaskCountdown from '../todo-list/TaskCountdown';
import { jumpExternalUrl } from '@/common/utils';
import { useAiAnalysis } from './useAiAnalysis';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

const TaskTitleContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 12px;
`;

const TaskSubtitle = styled.div`
  font-size: 12px;
  color: #666;
`;

const StyleTime = styled.span`
  color: #1a66ff;
`;

const StyledAISpeech = styled(Flex)`
  display: inline-flex !important;
  background: linear-gradient(
    90deg,
    rgba(55, 165, 255, 0.1) 0%,
    rgba(0, 209, 209, 0.1) 48%,
    rgba(85, 222, 140, 0.1) 100%
  );
  border-radius: 20px;
`;

const RecommendationText = styled.div`
  font-weight: 500;
  margin-bottom: 8px;
`;

const CommunicationText = styled.div`
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
`;

const CardWrap = styled.div`
  border-radius: 4px;
  background: #fff;
  padding: 24px;
  background: #ffffff99;
  border-color: #e6f2ff;
  margin-bottom: 12px;
  .title {
    font-weight: 500;
    font-size: 16px;
  }
`;

export default function TaskDetailDrawer(
  props: IModalProps<{ pid: string; highPotentialValues?: string[] }>,
) {
  const { pid, highPotentialValues } = props.data;
  const { aiSummary } = useAiAnalysis({
    pid,
    bizSource: 'merchant_page',
  });
  const {
    loading,
    tableProps,
    data,
    run: getData,
    refresh: refreshData,
  } = useAntdTable(
    async (page) => {
      if (!pid) return;
      aiSummary.createAiSummary();
      const res = await getQueryMerchantPriorityTaskInfo({
        page: {
          pageNo: page.current,
          pageSize: page.pageSize,
        },
        pid,
      });
      return {
        list: res.taskPageList?.dataList || [],
        total: res.taskPageList?.pageInfo?.totalCount,
        maxPriorityTask: res?.maxPriorityTask,
      };
    },
    {
      refreshDeps: [pid],
      onSuccess: (res) => {
        const list = res?.list || [];
        if (list.length > 0) {
          list.forEach((task) => {
            traceExp(PageSPMKey.首页, ModuleSPMKey['商户详情.任务曝光'], {
              pid,
              taskNo: task.taskNo,
            });
          });
        }
        if (res?.maxPriorityTask?.taskNo) {
          traceExp(PageSPMKey.首页, ModuleSPMKey['商户详情.高优任务曝光'], {
            pid,
            taskNo: res?.maxPriorityTask?.taskNo,
          });
        }
      },
    },
  );
  const maxPeriorityTask = data?.maxPriorityTask;

  // 每次都从列表补充 highPotentialValues，确保AI潜力主标签传递
  if (maxPeriorityTask) {
    if (highPotentialValues) {
      maxPeriorityTask.highPotentialValues = highPotentialValues;
    } else {
      const fromList = data?.list?.find((item) => item.taskNo === maxPeriorityTask.taskNo);
      // 如果商户列表有 highPotentialValues 字段，则补充到任务详情
      if (fromList && fromList.highPotentialValues) {
        maxPeriorityTask.highPotentialValues = fromList.highPotentialValues;
      } else {
        maxPeriorityTask.highPotentialValues = [];
      }
    }
  }

  const handleTaskClick = async () => {
    // 去完成点击埋点
    if (maxPeriorityTask?.taskNo && pid) {
      traceClick(PageSPMKey.首页, 'todo_task_click', {
        pid,
        taskNo: maxPeriorityTask.taskNo,
      });
    }

    if (!maxPeriorityTask?.redirectUrl) return;
    if (maxPeriorityTask?.taskStatus === 'INIT') {
      await pushTodoState(maxPeriorityTask.taskNo);
      refreshData();
    }
    jumpExternalUrl(maxPeriorityTask.redirectUrl);
  };

  const renderCommunicationText = () => {
    if (!maxPeriorityTask?.recommendSpeech) {
      return <CommunicationText>暂无推荐沟通话术</CommunicationText>;
    }
    return <CommunicationText>{maxPeriorityTask.recommendSpeech}</CommunicationText>;
  };
  return (
    <Drawer
      {...props}
      title="商户任务详情"
      width={931}
      styles={{
        body: {
          background: '#FAFAFA',
        },
      }}
    >
      <CardWrap
        style={{
          border: '1px solid #E6F2FF',
          background:
            'linear-gradient(to bottom, rgba(240, 252, 255, 1), rgba(230, 248, 255, 0.5))',
        }}
      >
        <Spin spinning={aiSummary.loading}>
          <Flex align="center" style={{ marginBottom: 10 }}>
            <img
              src="https://img.alicdn.com/imgextra/i4/O1CN01HT4TB51FdAdgpz0NN_!!6000000000509-2-tps-72-72.png"
              alt=""
              style={{ width: 20, height: 20 }}
            />
            <div className="title" style={{ marginLeft: 4, marginRight: 8 }}>
              商户分析
            </div>
            <TaskSubtitle>平台为您推荐以下数据，辅助您完成运维任务</TaskSubtitle>
          </Flex>
          <div
            style={{
              background: 'rgba(255, 255, 255, 0.6)',
              padding: 16,
              borderRadius: 4,
              maxHeight: 300,
              overflowY: 'auto',
            }}
          >
            {/* <div style={{ fontWeight: 500 }}>数据概览</div> */}
            {aiSummary.content && aiSummary.enable ? (
              <div style={{ padding: '0 0 0 16px', lineHeight: '24px', opacity: 0.7 }}>
                {/* eslint-disable-next-line react/no-danger */}
                <div dangerouslySetInnerHTML={{ __html: aiSummary.content }} />
              </div>
            ) : aiSummary.error ? (
              <div style={{ textAlign: 'center', marginTop: 7 }}>
                请求失败, <a onClick={() => aiSummary.createAiSummary()}>点击重试</a>
              </div>
            ) : (
              <div style={{ marginTop: 7, opacity: 0.7 }}>暂无数据</div>
            )}
          </div>
        </Spin>
      </CardWrap>

      <CardWrap>
        <div className="title" style={{ marginBottom: 10 }}>
          商户任务
        </div>
        <div
          style={{
            background: 'rgba(230, 242, 255, 0.6)',
            borderRadius: 4,
            padding: 16,
          }}
        >
          <Flex align="center" justify="space-between" style={{ marginBottom: 16 }}>
            <div style={{ flex: 1, width: 0, marginRight: 8 }}>
              <TaskTitleContainer>
                <div style={{ fontWeight: 500, fontSize: 16 }}>{maxPeriorityTask?.taskName}</div>
                <StyleTime
                  style={{ fontWeight: 500, fontSize: 12, marginRight: 16, marginLeft: 6 }}
                >
                  {maxPeriorityTask?.taskDesc}
                </StyleTime>
                <TaskCountdown
                  style={{ background: 'rgba(0, 0, 0, 0.04)' }}
                  expireTime={maxPeriorityTask?.manageLimit}
                />
              </TaskTitleContainer>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {maxPeriorityTask?.highPotentialValues?.[0] === '1' && (
                  <StyledAISpeech align="center" style={{ marginLeft: 8 }}>
                    <img
                      src="https://img.alicdn.com/imgextra/i4/O1CN01HT4TB51FdAdgpz0NN_!!6000000000509-2-tps-72-72.png"
                      alt=""
                      style={{
                        width: 24,
                        height: 24,
                        marginRight: 3,
                        flexShrink: 0,
                      }}
                    />
                    <div
                      style={{
                        flex: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        marginRight: 8,
                      }}
                    >
                      潜力广告主
                    </div>
                  </StyledAISpeech>
                )}
                {maxPeriorityTask?.recommendAnalysis && (
                  <Tooltip title={maxPeriorityTask?.recommendAnalysis}>
                    <StyledAISpeech align="center">
                      {console.log(
                        maxPeriorityTask?.highPotentialValues?.[0],
                        '11111',
                        maxPeriorityTask,
                      )}

                      {maxPeriorityTask?.highPotentialValues?.[0] !== '1' && (
                        <img
                          src="https://img.alicdn.com/imgextra/i4/O1CN01HT4TB51FdAdgpz0NN_!!6000000000509-2-tps-72-72.png"
                          alt=""
                          style={{
                            width: 24,
                            height: 24,
                            marginRight: 3,
                            flexShrink: 0,
                          }}
                        />
                      )}
                      <div
                        style={{
                          flex: 1,
                          overflow: 'hidden',
                          height: 24,
                          lineHeight: '24px',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          padding: '0 5px',
                        }}
                      >
                        {maxPeriorityTask?.recommendAnalysis}
                      </div>
                    </StyledAISpeech>
                  </Tooltip>
                )}
              </div>
            </div>
            {maxPeriorityTask?.redirectUrl ? (
              <Button type="primary" onClick={handleTaskClick}>
                去完成
              </Button>
            ) : null}
          </Flex>
          <RecommendationText>推荐沟通话术</RecommendationText>
          {renderCommunicationText()}
        </div>
        {/* 卡片 任务卡片 */}
        <TaskCard
          pid={props.data.pid}
          tasks={data?.list || []}
          pagination={tableProps.pagination}
          getData={getData}
          loading={loading}
        />
      </CardWrap>
    </Drawer>
  );
}
