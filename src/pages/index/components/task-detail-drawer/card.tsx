import React from 'react';
import { RightOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { Pagination, PaginationProps, Spin, Tooltip } from 'antd';
import { IListTask } from './types';
import { pushTodoState } from '@/services';
import { jumpExternalUrl } from '@/common/utils';
import { traceClick, PageSPMKey } from '@/utils/trace';

const CardContainer = styled.div`
  border-radius: 8px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
  min-width: 300px;
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
const CardTitle = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  width: 0;
`;

const CardArrow = styled.span`
  font-size: 16px;
  color: #888888;
  margin-left: auto;
  flex-shrink: 0;
`;

const ContentLine = styled.p`
  margin-top: 6px;
  color: rgba(0, 0, 0, 0.35);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const ContentBlock = styled.p`
  background: linear-gradient(
    90deg,
    rgba(55, 165, 255, 0.1) 0%,
    rgba(0, 209, 209, 0.1) 48%,
    rgba(85, 222, 140, 0.1) 100%
  );
  display: inline-block;
  border-radius: 8px;
  padding: 1px 8px;
  margin-top: 6px;
  color: rgba(0, 0, 0, 0.9);
  max-width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
`;

interface IProps {
  tasks: IListTask[];
  pid: string;
  pagination: PaginationProps;
  getData: any;
  loading?: boolean;
}

const TaskCard: React.FC<IProps> = (props) => {
  const { tasks, pagination, getData, loading, pid } = props;

  const handleTaskClick = async (task: IListTask) => {
    // 任务卡片点击埋点
    if (pid && task.taskNo) {
      traceClick(PageSPMKey.首页, 'todo_task_click', {
        pid,
        taskNo: task.taskNo,
      });
    }

    await pushTodoState(task.taskNo);
    jumpExternalUrl(task.redirectUrl);
  };
  return (
    <Spin spinning={!!loading}>
      <div style={{ display: 'grid', gridTemplateColumns: '2fr 2fr', gap: 16, marginTop: 12 }}>
        {tasks.map((task) => (
          <CardContainer key={task?.taskNo}>
            <CardHeader>
              {/* 任务 */}
              <Tooltip title={task?.taskName}>
                <CardTitle>{task?.taskName}</CardTitle>
              </Tooltip>
              {task?.redirectUrl ? (
                <CardArrow onClick={() => handleTaskClick(task)}>
                  <a style={{ color: '#888888' }} rel="noreferrer">
                    <RightOutlined />
                  </a>
                </CardArrow>
              ) : null}
            </CardHeader>
            {/* 任务描述 */}
            {task?.taskDesc && (
              <Tooltip title={task.taskDesc}>
                <ContentLine>{task.taskDesc}</ContentLine>
              </Tooltip>
            )}
            {/* 推荐分析 */}
            {task?.recommendAnalysis && (
              <Tooltip title={task.recommendAnalysis}>
                <ContentBlock>{task.recommendAnalysis}</ContentBlock>
              </Tooltip>
            )}
          </CardContainer>
        ))}
      </div>
      <Pagination
        {...pagination}
        style={{ marginTop: 8 }}
        align="end"
        size="small"
        onChange={(current, pageSize) => getData({ current, pageSize })}
      />
    </Spin>
  );
};

export default TaskCard;
