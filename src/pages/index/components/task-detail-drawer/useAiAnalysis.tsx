import { startDiagnose, queryDiagnoseResult } from '@/services';
import { useRequest, useUnmount } from 'ahooks';
import { notification } from 'antd';
import { Dayjs } from 'dayjs';
import { useState, useRef } from 'react';

let times = 0;
const duration = 120000;
const intervalDelay = 2000;
const maxTimes = duration / intervalDelay;
let endSignal = false;
export const useAiAnalysis = (props: {
  pid: string;
  shopIdList?: string[];
  dateRange?: Dayjs[];
  bizSource: string;
}) => {
  const { pid, shopIdList, dateRange, bizSource } = props;
  const [aiSummaryLoading, setAiSummaryLoading] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  function endInterval() {
    updateAiAnalysisTaskNo?.(null);
    if (intervalRef.current) {
      clearTimeout(intervalRef.current);
      intervalRef.current = null;
    }
    setAiSummaryLoading(false);
    updateAiSummaryText?.(null);
    endSignal = true;
    times = 0;
  }

  const {
    run: createAiSummary,
    data: aiAnalysisTaskNo,
    error: createSummaryError,
    loading: creatingAiSummary,
    mutate: updateAiAnalysisTaskNo,
  } = useRequest(
    async (params = {}) => {
      endInterval();
      const reqParams = {
        pid,
        ...params,
      };

      if (shopIdList?.length) {
        reqParams.shopIdList = shopIdList.join(',');
      }

      if (dateRange?.length) {
        reqParams.startDate = dateRange[0];
        reqParams.endDate = dateRange[1];
      }

      const res = await startDiagnose(reqParams);

      return res.taskId;
    },
    {
      manual: true,
      onSuccess(taskNo) {
        if (taskNo && taskNo !== '0') {
          endSignal = false;
          setAiSummaryLoading(true);
          startInterval(taskNo);
        }
      },
    },
  );
  const {
    run: getAiSummaryResult,
    data: aiSummaryText,
    error: aiSummaryError,
    mutate: updateAiSummaryText,
  } = useRequest(
    async (taskNo) => {
      if (endSignal || !taskNo) {
        setAiSummaryLoading(false);
        return;
      }
      const res = await queryDiagnoseResult(taskNo);
      if (res?.status === 'DONE' && typeof res?.result === 'string') {
        setAiSummaryLoading(false);
        times = 0;
        if (res.result && bizSource === 'business-news') {
          notification.success({ message: '商家经营小结创建成功!' });
        }
        return res.result;
      } else {
        startInterval(taskNo);
      }
    },
    {
      manual: true,
      onError: () => {
        times = 0;
        setAiSummaryLoading(false);
      },
    },
  );

  function startInterval(taskNo: string) {
    if (times <= maxTimes) {
      intervalRef.current = setTimeout(() => {
        times += 1;
        getAiSummaryResult(taskNo);
      }, intervalDelay);
    } else {
      setAiSummaryLoading(false);
      times = 0;
      notification.error({ message: '商家经营小结获取超时!' });
    }
  }

  useUnmount(() => {
    endInterval();
  });
  return {
    aiSummary: {
      createAiSummary,
      content: aiSummaryText,
      loading: aiSummaryLoading || creatingAiSummary,
      error: aiSummaryError || createSummaryError,
      enable: aiAnalysisTaskNo && aiAnalysisTaskNo !== '0',
      taskNo: aiAnalysisTaskNo,
      endInterval,
      updateAiSummaryText,
    },
  };
};
