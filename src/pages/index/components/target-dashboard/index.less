.Select-Container {
  margin-left: 18px;
}

.data-source-text {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
  font-size: 14px;
  padding: 16px 36px;
  margin-top: 20px;
}
.data-source-Chart{
    overflow-x: auto;
    margin: -20px -24px;
    min-width: 100%;
    
}
.Select-elect{
    font-size: 14px;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.85);
}
.data-source-chart{

}
// .postion-right{
//   position: absolute;
//   right: 10 !important;
//   top: 50%;
//   transform: translateY(-50%);
//   z-index: 1;
//   background: rgba(255,255,255,0.8);
//   border-radius: 50%;
// }