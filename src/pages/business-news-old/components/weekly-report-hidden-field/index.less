.weekly-report-hidden-field {
  margin-right: 10px;
  &-title {
    display: flex;
    .title {
      flex-shrink: 0;
      margin-right: 16px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }
    span {
      font-size: 10px;
    }
  }
  &-content {
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    padding: 10px;
    color: rgba(0,0,0,0.85);
    margin-top: 8px;
    .item-subtitle {
      margin-bottom: 6px;
    }
    .item-subcontent {
      font-size: 12px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-left: 10px;
      .item {
        margin-left: 8px;
        border: 1px solid #f5222d;
        border-radius: 4px;
        padding: 4px 0;
        width: 100px;
        text-align: center;
        margin-bottom: 8px;
        cursor: pointer;
        user-select: none;
      }
      .item-checkable {
        border: 1px solid #000;
        background-color: rgba(0,0,0,0.25);
      }
    }
  }
}
