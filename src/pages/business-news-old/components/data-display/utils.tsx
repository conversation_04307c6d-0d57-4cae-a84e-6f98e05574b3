import { ReactElement } from 'react';
import { DataDisplayItem } from '../handle-data';

export const formatCompareValue = (
  value: DataDisplayItem['compareValue'],
): [string | null, ReactElement | string | null, string] => {
  if (value === '--') {
    return [null, null, '--'];
  }
  const val = parseFloat(value as any);
  // eslint-disable-next-line no-restricted-globals
  if (!val) {
    return [null, null, '--'];
  }
  if (Math.round(val * 100) === 0) {
    return [null, null, '--'];
  }

  const resultVal = `${(Math.abs(val) * 100).toFixed(0)}%`;
  if (val > 0) {
    return ['#f00', '+', resultVal];
  }
  if (val < 0) {
    return ['#69bf47', '-', resultVal];
  }

  return [null, null, resultVal];
};
