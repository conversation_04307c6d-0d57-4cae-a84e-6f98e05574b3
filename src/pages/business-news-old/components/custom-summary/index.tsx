import React from 'react';
import style from '../index.module.less';
import { Flex } from 'antd';

const CustomSummary = (props) => {
  const { customSummary } = props;

  if (!customSummary) {
    return null;
  }

  function createHtml() {
    return { __html: customSummary };
  }

  return (
    <div
      className={style.container}
      style={{
        border: '1px solid #E6F2FF',
        background: 'linear-gradient(to bottom, rgba(240, 252, 255, 1), rgba(230, 248, 255, 0.5))',
      }}
    >
      <div className={style.wrap} style={{ padding: 12, background: 'unset' }}>
        <Flex className={style.title} align="center">
          <img
            src="https://img.alicdn.com/imgextra/i4/O1CN01HT4TB51FdAdgpz0NN_!!6000000000509-2-tps-72-72.png"
            style={{ marginRight: 4, width: '1.4em', height: '1.4em' }}
          />
          AI智能分析
        </Flex>
        <div
          className={style.cardWrap}
          style={{
            lineHeight: '2em',
            background: 'rgba(255, 255, 255, 0.6)',
            borderRadius: 12,
            fontSize: '1.2em',
          }}
        >
          <span dangerouslySetInnerHTML={createHtml()} />
        </div>
      </div>
    </div>
  );
};
// eslint-disable-next-line no-restricted-syntax
export default React.memo(CustomSummary);
