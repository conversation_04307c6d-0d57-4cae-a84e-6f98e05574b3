import { Flex } from 'antd';
import styled from 'styled-components';
import { formatterTargetValue, MONEY_UNIT } from '../utils';
import { useDataState } from '../store/useDataState';
import { CaretUpFilled, CaretDownFilled } from '@ant-design/icons';

const Container = styled.div`
  border-radius: 6px;
  width: 0;
`;
const CommonHeader = styled.div`
  padding: 0 1.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 3.1em;
  line-height: 3.1em;
  white-space: nowrap;
  font-weight: 500;
  font-size: 1.4em;
`;
const MyShopHeader = styled(CommonHeader)`
  padding-right: 2em;
`;

const OtherShopHeader = styled(CommonHeader)`
  padding-left: 2.5em;
`;

const ContentWrap = styled.div`
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  padding: 0.6em 1.2em;
  line-height: 2em;
  font-size: 1.4em;
  height: calc(100% - 3.1em);
`;

export function MyShop(props: { fields: any[]; data: any; compareData?: any }) {
  const { fields, data = {}, compareData = {} } = props;
  const { shopName } = useDataState();
  // 对比函数
  const renderCompare = (key: string) => {
    const myValue = data[key];
    const otherValue = compareData[key];
    if (myValue === undefined || myValue === null || myValue === '-' || myValue === '--')
      return null;
    if (
      otherValue === undefined ||
      otherValue === null ||
      otherValue === '-' ||
      otherValue === '--'
    )
      return null;
    const myNum = Number(myValue);
    const otherNum = Number(otherValue);
    if (isNaN(myNum) || isNaN(otherNum)) return null;
    if (myNum > otherNum) {
      return <CaretUpFilled style={{ color: '#FF4B4B', fontSize: 14, marginLeft: 4 }} />;
    } else if (myNum < otherNum) {
      return <CaretDownFilled style={{ color: '#2AB352', fontSize: 14, marginLeft: 4 }} />;
    } else {
      return <span style={{ color: '#666', fontSize: 14, marginLeft: 4 }}>-</span>;
    }
  };
  return (
    <Container style={{ flex: 5 }}>
      <MyShopHeader>{shopName}</MyShopHeader>
      <ContentWrap style={{ background: 'linear-gradient(120deg, #FFF8F5 18%, #FDF1EC 86%)' }}>
        {fields.map((item) => (
          <Flex gap={10} justify="space-between" key={item.key}>
            <div style={{ opacity: 0.6 }}>{item.title}</div>
            <div style={{ textAlign: 'left', minWidth: 60, display: 'flex', alignItems: 'center' }}>
              {formatterTargetValue(
                data[item.key],
                item.title.endsWith(MONEY_UNIT) || item.title === '商家分',
                item.title.endsWith('率'),
              ) || '-'}
              {renderCompare(item.key)}
            </div>
          </Flex>
        ))}
      </ContentWrap>
    </Container>
  );
}

export function OtherShop(props: {
  fields: any[];
  shop: {
    label: string;
    value: string;
  };
  data: any;
}) {
  const { fields, data = {} } = props;
  return (
    <Container style={{ flex: 4 }}>
      <OtherShopHeader>周边同业门店 A</OtherShopHeader>
      <ContentWrap
        style={{
          paddingLeft: 28,
          background:
            'linear-gradient(183deg, rgba(22, 119, 254, 0.08) 10%, rgba(22, 119, 254, 0.08) 93%)',
        }}
      >
        {fields.map((item) => (
          <div>
            {formatterTargetValue(
              data[item.key],
              item.title.endsWith(MONEY_UNIT) || item.title === '商家分',
              item.title.endsWith('率'),
            ) || '-'}
          </div>
        ))}
      </ContentWrap>
    </Container>
  );
}
