import React from 'react';
import TaskList from './components/taskList';
import style from '../index.module.less';
import { Flex } from 'antd';
import styled from 'styled-components';
import TaskSummaryV3 from './components/v3';
import Card from '../card-list/card';
import { useDataState } from '../store/useDataState';

const ContentWrap = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1.4em;
  justify-content: flex-end;
  padding: 0 1.4em;
  align-items: center;
  &::after {
    content: '';
    flex: '0 0 auto';
  }
`;

const DiagnosisData = (props) => {
  const { goodsNewsInfo } = props;
  const { isChild } = useDataState();
  const { taskSummary = {}, dataResult = {}, taskSummaryV3 } = goodsNewsInfo || {};
  const { poi_rank, poi_cnt } = dataResult;

  // 获取待优化总数
  const getToDoCount = (list) => {
    let sum = 0;
    list.forEach((val: any) => {
      const count = +val?.undoCount;
      if (!Number.isNaN(count)) {
        sum += count;
      }
    });
    return sum;
  };
  if (!isChild || !goodsNewsInfo || (!taskSummaryV3 && !taskSummary)) {
    return null;
  }

  return (
    <Card title="成长中心">
      {taskSummaryV3 ? (
        <TaskSummaryV3 data={taskSummaryV3} />
      ) : (
        <>
          <ContentWrap>
            <>
              <Flex align="center" style={{ flex: 1, maxWidth: '100%' }}>
                <div className={style.card_content_bg} style={{ flexShrink: 0 }}>
                  {taskSummary?.diagnoseHomeResultDTO?.merchantScore ?? '--'}
                </div>
                <div
                  className={style.catd_result}
                  style={{ width: 'calc(100% - 57px)', fontSize: '1.2em' }}
                >
                  <Flex align="center" style={{ marginRight: 7 }}>
                    <div className={style.result_state_label}>诊断结果：</div>
                    <div className={style.result_state_msg}>
                      {taskSummary?.diagnoseHomeResultDTO?.diagnoseResult ?? ''}
                    </div>
                  </Flex>
                  <div className={style.result_rank_title}>同行排名</div>
                  <div className={style.result_state}>
                    <span className={style.result_rank}>{poi_rank ?? '--'}</span>/
                    <span>{poi_cnt ?? '--'}</span>
                  </div>
                  <div className={style.result_subTitle}>
                    {taskSummary?.diagnoseHomeResultDTO?.diagnoseDesc ?? ''}
                  </div>
                </div>
              </Flex>
              <div className={style.card_content_toDoBtn} style={{ marginLeft: 7, flexShrink: 0 }}>
                {getToDoCount(taskSummary?.diagnoseHomeResultDTO?.list ?? [])}项待优化
              </div>
            </>
          </ContentWrap>
          {/* 任务卡片  */}
          <TaskList data={taskSummary?.list ?? []} />
        </>
      )}
    </Card>
  );
};
export default React.memo(DiagnosisData);
