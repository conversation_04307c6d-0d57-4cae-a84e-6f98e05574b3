.card_task_wrap {
  padding: 1.6em;
  background-color: #fafafa;
  border-radius: 1.6em;
  margin: 1.2em;
  .card_task {
    display: flex;
    justify-content: space-between;
    .card_task_bg {
      width: 4.8em;
      height: 4.8em;
      background-size: 4.8em;
      background-repeat: no-repeat;
      margin-right: 2em;
      flex-shrink: 0;
    }
    .card_task_content {
      flex: 1;
      color: #333;
      width: 0;
      .card_task_title {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-line-clamp: 2;
        font-weight: 600;
        font-size: 1.4em;
      }
      .task_content_subTitle {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-top: 0.8em;
        font-size: 1.2em;
        color: #999;
        font-weight: 400;
      }
    }

    .card_task_score {
      margin-top: 2.2em;
      flex-shrink: 0;
      .task_score {
        font-size: 1.3em;
        padding-left: 1em;
        color: #333;
        margin-bottom: 1em;
        .score {
          color: #ff5f33;
        }
      }
    }
  }
}
.card_task_wrap:last-child {
  margin-bottom: 0;
}
