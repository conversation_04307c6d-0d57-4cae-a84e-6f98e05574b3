import React, { useState } from 'react';
// import { Toast } from '@alipay/cook';
import ProgressBar from '../progressBar';

import style from './index.module.less';

const TaskList = ({ data }) => {
  return (
    <div>
      {data.map((item, index) => {
        return (
          <div className={style.card_task_wrap} key={index}>
            <div className={style.card_task}>
              <img
                src={
                  item.icon ||
                  'https://img.alicdn.com/imgextra/i3/O1CN01N1YK3d1r7ytZsX43p_!!6000000005585-2-tps-64-64.png'
                }
                style={{
                  flexShrink: 0,
                  marginRight: '1.2em',
                  width: '4.5em',
                  height: '4.5em',
                }}
              />
              <div className={style.card_task_content}>
                <div className={style.card_task_title}>{item.title}</div>
                <div className={style.task_content_subTitle}>{item.subTitle}</div>
              </div>
              <div className={style.card_task_score}>
                <div className={style.task_score}>
                  <span className={style.score}>+{item.score}</span>
                  {item.label}
                </div>
              </div>
            </div>
            {item.scoreProcessBar && <ProgressBar scoreBar={item.scoreProcessBar} />}
          </div>
        );
      })}
    </div>
  );
};

export default TaskList;
