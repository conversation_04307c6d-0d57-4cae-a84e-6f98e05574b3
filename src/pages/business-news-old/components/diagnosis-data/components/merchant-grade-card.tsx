import { DiagnoseResultDTO } from './types';
import './grade-card.less';

/* eslint-disable no-nested-ternary */
const prefixCls = 'merchant-grade-card';

export const GradeType = {
  DEFAULT: 0,
  PRIMARY_POTENTIAL: 1,
  SECONDARY_POTENTIAL: 2,
  TERTIARY_POTENTIAL: 3,
  SILVER: 4,
  GOLD: 5,
  B<PERSON><PERSON>K_GOLD: 6,
};
const gradeStyleMap = {
  [GradeType.DEFAULT]: {
    gradeIcon:
      'https://gw.alicdn.com/imgextra/i3/O1CN01geMNs726BqdxzCQHf_!!6000000007624-2-tps-60-63.png',
    gradeSmallBackgroundImage:
      'https://gw.alicdn.com/imgextra/i4/O1CN01pLC7ba1DoxbLGbsQ3_!!6000000000264-2-tps-1005-198.png',
    gradeTextImage:
      'https://gw.alicdn.com/imgextra/i2/O1CN01glv2a31wVrndwU4yt_!!6000000006314-2-tps-360-54.png',
    gradeColor: '#5c91fd',
  },
  [GradeType.PRIMARY_POTENTIAL]: {
    gradeIcon:
      'https://img.alicdn.com/imgextra/i3/O1CN01egFu2T1RjpmhfYnUD_!!6000000002148-2-tps-318-318.png',
    gradeSmallBackgroundImage:
      'https://img.alicdn.com/imgextra/i2/O1CN01LaNStl1YobDXDGet1_!!6000000003106-2-tps-1029-383.png',
    gradeTextImage:
      'https://gw.alicdn.com/imgextra/i4/O1CN01CX8krB1HE6Bc9OPy6_!!6000000000725-2-tps-375-66.png',
    gradeColor: 'rgba(103, 40, 13, 0.7)',
  },
  [GradeType.SECONDARY_POTENTIAL]: {
    gradeIcon:
      'https://img.alicdn.com/imgextra/i3/O1CN01egFu2T1RjpmhfYnUD_!!6000000002148-2-tps-318-318.png',
    gradeSmallBackgroundImage:
      'https://img.alicdn.com/imgextra/i2/O1CN01LaNStl1YobDXDGet1_!!6000000003106-2-tps-1029-383.png',
    gradeTextImage:
      'https://gw.alicdn.com/imgextra/i2/O1CN01fNMNyR1lWoqdYxWAW_!!6000000004827-2-tps-375-66.png',
    gradeColor: 'rgba(103, 40, 13, 0.7)',
  },
  [GradeType.TERTIARY_POTENTIAL]: {
    gradeIcon:
      'https://img.alicdn.com/imgextra/i3/O1CN01egFu2T1RjpmhfYnUD_!!6000000002148-2-tps-318-318.png',
    gradeSmallBackgroundImage:
      'https://img.alicdn.com/imgextra/i2/O1CN01LaNStl1YobDXDGet1_!!6000000003106-2-tps-1029-383.png',
    gradeTextImage:
      'https://gw.alicdn.com/imgextra/i4/O1CN01AoJJgZ22xhYzXXZaX_!!6000000007187-2-tps-375-66.png',
    gradeColor: 'rgba(103, 40, 13, 0.7)',
  },
  [GradeType.SILVER]: {
    gradeIcon:
      'https://img.alicdn.com/imgextra/i2/O1CN01AvD5wh1H3Z4bZg3mZ_!!6000000000702-2-tps-318-318.png',
    gradeSmallBackgroundImage:
      'https://img.alicdn.com/imgextra/i3/O1CN01r2Sk3r1mqLLYbZxxD_!!6000000005005-2-tps-1029-383.png',
    gradeTextImage:
      'https://gw.alicdn.com/imgextra/i2/O1CN01DSCVyJ22i865jnprr_!!6000000007153-2-tps-375-66.png',
    gradeColor: '#1F2F4F',
  },
  [GradeType.GOLD]: {
    gradeIcon:
      'https://img.alicdn.com/imgextra/i4/O1CN01C8xmQC1QcDa4HIJ2o_!!6000000001996-2-tps-318-318.png',
    gradeSmallBackgroundImage:
      'https://img.alicdn.com/imgextra/i1/O1CN01BFgOQ01bzzvD1mJZU_!!6000000003537-2-tps-1029-383.png',
    gradeTextImage:
      'https://gw.alicdn.com/imgextra/i4/O1CN013vMYiL1hahkE4HN6q_!!6000000004294-2-tps-375-66.png',
    gradeColor: '#803906',
  },
  [GradeType.BLACK_GOLD]: {
    gradeIcon:
      'https://img.alicdn.com/imgextra/i1/O1CN01216INL25oURS3c8Cg_!!6000000007573-2-tps-318-318.png',
    gradeSmallBackgroundImage:
      'https://img.alicdn.com/imgextra/i3/O1CN01e77NTC21YgJE2bKqp_!!6000000006997-2-tps-1032-386.png',
    gradeTextImage:
      'https://gw.alicdn.com/imgextra/i2/O1CN01OaNMpq1W5b4SQltCo_!!6000000002737-2-tps-375-66.png',
    gradeBigBackgroundImage:
      'https://img.alicdn.com/imgextra/i2/O1CN015oiJ9a1nzn8SV5Z1x_!!6000000005161-0-tps-750-860.jpg',
    gradeColor: '#FFE8CF',
  },
};

export function MerchantGradeCard(props: { data: DiagnoseResultDTO; isNew: boolean }) {
  const { isNew: isNewScore, data: context } = props || {};

  const {
    merchantScore = '-',
    nextGradeScoreGap,
    protected: isProtected,
    protectedDays,
  } = context || {};

  let showGrade = context?.showGrade;
  if (!isNewScore || !gradeStyleMap[showGrade]) {
    showGrade = GradeType.DEFAULT;
  }
  return (
    <div
      className={`${prefixCls}_container`}
      style={{
        backgroundImage: `url(${gradeStyleMap[showGrade].gradeSmallBackgroundImage})`,
      }}
    >
      <div>
        <div className={`${prefixCls}_grade_container`}>
          <img
            src={gradeStyleMap[showGrade].gradeTextImage}
            alt=""
            className={
              showGrade === GradeType.DEFAULT
                ? `${prefixCls}_lv_img_default`
                : `${prefixCls}_lv_img`
            }
          />
          <img
            src={gradeStyleMap[showGrade].gradeIcon}
            alt=""
            className={`${prefixCls}_grade_img`}
          />
        </div>
        <div className={`${prefixCls}_score_container`}>
          <div className={`${prefixCls}_score`}>
            <span style={{ color: gradeStyleMap[showGrade].gradeColor }}>
              商家分{merchantScore}分
            </span>
          </div>
          <div className={`${prefixCls}_next_level_score`}>
            <span style={{ color: gradeStyleMap[showGrade].gradeColor }}>
              ｜
              {isProtected
                ? `还有${protectedDays || '-'}天降级`
                : nextGradeScoreGap
                ? `还需${nextGradeScoreGap || '-'}分到下一等级`
                : '恭喜您已是最高等级'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
