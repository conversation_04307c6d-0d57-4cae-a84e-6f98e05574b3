import React from 'react';
import { Drawer } from 'antd';
import { IDefaultShop } from '../weekly-report-form/form';
import WeeklyReportForm from '../weekly-report-form';

interface IProps {
  visible: boolean;
  pid: string;
  merchantName: string;
  defaultShop?: IDefaultShop;
  onClose: () => void;
  hasOptGroup?: boolean;
}

export const WeeklyReportDrawer: React.FC<IProps> = ({
  visible,
  pid,
  merchantName,
  defaultShop,
  onClose,
  hasOptGroup,
}) => {
  const closeDrawer = () => {
    onClose();
  };

  return (
    <Drawer
      destroyOnClose
      title="生成喜报"
      key="right"
      placement="right"
      size="large"
      width={950}
      onClose={closeDrawer}
      open={visible}
      styles={{
        body: {
          height: '90%',
          padding: '0 0 0 24px',
        },
      }}
    >
      <WeeklyReportForm
        merchantName={merchantName}
        defaultShop={defaultShop}
        closeDrawer={closeDrawer}
        hasOptGroup={hasOptGroup}
        pid={pid}
      />
    </Drawer>
  );
};
