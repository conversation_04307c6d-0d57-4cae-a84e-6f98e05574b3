/* eslint-disable react-hooks/rules-of-hooks */
import { useMount, useUnmount } from 'ahooks';
import { IField, useDataState } from './useDataState';

const flattenFields = (fields: IField[]): IField[] => {
  return fields.flatMap((item) => {
    return item.children ? [item, ...flattenFields(item.children)] : [item];
  });
};
export default function ControllerHOC(Component: React.ComponentType<any>, field: IField) {
  return function (props: any) {
    const { registerField, unregisterField, isShow } = useDataState();
    const flattenFieldList = flattenFields([field]).slice(1);
    const shownFields = isShow(flattenFieldList.map((item) => item.key));
    useMount(() => {
      registerField(field);
    });
    useUnmount(() => {
      unregisterField(field.key);
    });
    if (!shownFields.some(<PERSON><PERSON><PERSON>)) {
      return null;
    }
    return <Component {...props} shownFields={shownFields} />;
  };
}

export type IControllerProps<T = unknown> = T & {
  shownFields: boolean[];
};
