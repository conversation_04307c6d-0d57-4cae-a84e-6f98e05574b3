import { Divider, Flex } from 'antd';
import styled from 'styled-components';
import { useDataState } from './store/useDataState';
import { useEffect } from 'react';

const Container = styled.div`
  background: #fff;
  border-radius: 1.2em;
  width: 100%;
  padding: 1.6em;
  margin-bottom: 1.4em;
`;
// 银标
const url =
  'https://img.alicdn.com/imgextra/i2/O1CN01rXcRR11QN6V7hNTEh_!!6000000001963-2-tps-510-144.png';
const goldUrl =
  'https://img.alicdn.com/imgextra/i1/O1CN01XHzdf720Om76QsDJv_!!6000000006840-2-tps-510-144.png';
const LabelContainer = (props: { value: any; isGold: boolean }) => {
  const { value, isGold } = props;
  return (
    <div
      style={{
        backgroundRepeat: 'no-repeat',
        backgroundImage: `url('${isGold ? goldUrl : url}')`,
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        width: 100,
        fontWeight: 500,
        color: isGold ? '#8b5e2a' : '#4e689e',
        paddingLeft: 32,
        height: 37,
        lineHeight: '37px',
        fontSize: 16,
        flexShrink: 0,
      }}
    >
      {value}
    </div>
  );
};

const TargetWrapper = styled.div`
  text-align: center;
  &:first-child {
    justify-self: left;
  }
  &:nth-child(2) {
    justify-self: center;
  }
  &:nth-child(3) {
    justify-self: right;
  }
`;
const TargetItem = ({ label, value }) => {
  return (
    <TargetWrapper>
      <div style={{ color: '#999', fontSize: '1.4em' }}>{label}</div>
      <div
        style={{
          fontSize: '1.8em',
          fontWeight: 500,
          color: '#1a66ff',
        }}
      >
        {value || '-'}
      </div>
    </TargetWrapper>
  );
};
function ShopHeaderCard(props: {
  competivence: string;
  duration: string;
  right_label: string;
  situation: string;
}) {
  const { duration, right_label, situation, competivence } = props;
  const { shopName, shopList, isMultiShops, unregisterField, registerField, isShow } =
    useDataState();
  const isGold = right_label === 'HIGH_V';
  const shownFields = isShow?.(['competition', 'operatingStatus', 'potentialOpportunity']) || [
    true,
    true,
    true,
  ];

  // 多门店展示逻辑
  let displayShopName = shopName;
  if (shopList && shopList.length > 1) {
    const firstName = shopList[0]?.label || '';
    displayShopName = `${firstName}等 ${shopList.length}家门店`;
  }

  useEffect(() => {
    if (isMultiShops) {
      unregisterField('shopHeaderCard');
    } else {
      registerField({
        label: '店铺概览',
        key: 'shopHeaderCard',
        children: [
          {
            title: '同行竞争',
            key: 'competition',
          },
          {
            title: '经营现状',
            key: 'operatingStatus',
          },
          {
            title: '潜在商机',
            key: 'potentialOpportunity',
          },
        ],
      });
    }
  }, [isMultiShops]);

  return (
    <Container>
      <Flex wrap={false} align="center">
        <div style={{ fontSize: '1.6em', fontWeight: 500, marginRight: '1em' }}>
          {displayShopName}
        </div>
        {right_label && duration && !isMultiShops ? (
          <LabelContainer value={duration} isGold={isGold} />
        ) : null}
      </Flex>
      {isMultiShops ? null : (
        <>
          <Divider style={{ marginTop: '1em', marginBottom: '1em' }} />
          <div
            style={{ padding: '4px 12px', display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)' }}
          >
            {shownFields?.[0] && <TargetItem label="同行竞争" value={competivence || '-'} />}
            {shownFields?.[1] && <TargetItem label="经营现状" value={situation} />}
            {shownFields?.[2] && <TargetItem label="潜在商机" value="空间大" />}
          </div>
        </>
      )}
    </Container>
  );
}

export default ShopHeaderCard;
