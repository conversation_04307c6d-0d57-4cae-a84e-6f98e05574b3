import React, { useRef, useState } from 'react';
import { Modal, Form, Checkbox, message, Flex, Space } from 'antd';
import MaterialModal from '../../../components/material-modal';
import { ModuleSPMKey, PageSPMKey, trace, traceClick } from '@/utils/trace';
import { infraContentCollectReachOut } from '@/services/ai-material';
import { useRequest } from 'ahooks';
import { IconFontQiWei } from '@/components/icons';
import copy from 'copy-to-clipboard';
import BdTextTpl from '@/components/bd-text-tpl';
import { ModuleType } from './constants';

export interface SendQwMsgModalProps {
  pid: string;
  merchantName: string;
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  options: Array<{ moduleType: string; moduleName: string }>; // 新增
  shopId: string;
  groupName: string;
}

// 白名单，目前只包括手艺人
const MODULE_TYPE_WHITELIST = [ModuleType['手艺人']];

const SendQwMsgModal: React.FC<SendQwMsgModalProps> = ({
  pid,
  merchantName,
  visible,
  onOk,
  onCancel,
  options = [],
  shopId,
  groupName,
}) => {
  const [form] = Form.useForm();
  // scopeId -> 选中素材id数组
  const [scopeMaterialMap, setScopeMaterialMap] = useState<Record<string, string[]>>({});
  // 当前弹窗相关
  const [materialModalOpen, setMaterialModalOpen] = useState(false);
  const [currentScopeId, setCurrentScopeId] = useState<string | null>(null);
  const bdWordsRef = useRef(null);

  const { runAsync: runSend, loading } = useRequest(infraContentCollectReachOut, { manual: true });

  // 由素材map自动推导勾选状态
  const checkedScopes = Object.keys(scopeMaterialMap).filter(
    (key) => scopeMaterialMap[key] && scopeMaterialMap[key].length > 0,
  );

  // 点击checkbox时弹出素材选择弹窗
  const handleCheckboxClick = (moduleType: string) => {
    setCurrentScopeId(moduleType);
    setMaterialModalOpen(true);
    trace('send-qw-msg-modal-scope-select', { scopeId: moduleType, pid });
  };

  // 素材弹窗确认
  const handleMaterialOk = (selectedIds: string[]) => {
    if (currentScopeId) {
      setScopeMaterialMap((prev) => {
        const newMap = { ...prev, [currentScopeId]: selectedIds };
        // 只有有素材才勾选checkbox，否则不勾选
        return newMap;
      });
      setMaterialModalOpen(false);
      trace('send-qw-msg-modal-material-ok', { scopeId: currentScopeId, selectedIds });
      setCurrentScopeId(null);
    }
  };

  // 素材弹窗取消
  const handleMaterialCancel = () => {
    setMaterialModalOpen(false);
    setCurrentScopeId(null);
  };

  const handleOk = async () => {
    // draftNoList为所有选中素材id的合并列表，转为number[]
    const draftNoList = Object.values(scopeMaterialMap)
      .flat()
      .map((id) => Number(id))
      .filter((id) => !isNaN(id));
    if (!draftNoList.length) {
      message.error('请选择至少一个素材');
      return;
    }

    trace('send-qw-msg-modal-send', { pid, merchantName, draftNoList });
    traceClick(PageSPMKey.首页, ModuleSPMKey.企微自动发送任务, {
      pid,
    });
    const { materialId } = await bdWordsRef.current.saveTemplate();
    const params = {
      merchantId: pid,
      shopId,
      draftNoList,
      materialId,
    };
    await runSend(params);
    message.success('发送成功');
    form.resetFields();
    setScopeMaterialMap({});
    onOk();
  };

  return (
    <>
      <Modal
        title="发送消息至企微群"
        open={visible}
        onOk={handleOk}
        confirmLoading={loading}
        onCancel={() => {
          form.resetFields();
          setScopeMaterialMap({});
          onCancel();
        }}
        destroyOnClose
        afterOpenChange={(open) => {
          if (open) trace('send-qw-msg-modal-open', { pid, merchantName });
        }}
        okButtonProps={{
          disabled: !checkedScopes.length,
        }}
      >
        <Form form={form} initialValues={{ collectScope: [], content: '' }}>
          <Form.Item label="商户名称" required>
            <Flex align="center" gap={10}>
              <div>{merchantName}</div>
              {groupName && (
                <a
                  onClick={() => {
                    copy(groupName);
                    message.success('复制成功');
                  }}
                >
                  <Flex align="center">
                    <IconFontQiWei style={{ fontSize: 15 }} /> 点击复制群名
                  </Flex>
                </a>
              )}
            </Flex>
          </Form.Item>
          <Form.Item label="信息收集范围" required>
            <Space>
              {options
                .filter((item) => MODULE_TYPE_WHITELIST.includes(item.moduleType as ModuleType))
                .map((item) => (
                  <Checkbox
                    key={item.moduleType}
                    checked={checkedScopes.includes(item.moduleType)}
                    onClick={() => handleCheckboxClick(item.moduleType)}
                  >
                    {item.moduleName}
                  </Checkbox>
                ))}
            </Space>
          </Form.Item>
          <Form.Item
            label="信息内容"
            name="materialId"
            rules={[{ required: true, message: '请输入信息内容' }]}
          >
            <BdTextTpl ref={bdWordsRef} scene="DRAFT_CONTENT_COLLECT" noCheckbox hideScenePicker />
          </Form.Item>
        </Form>
      </Modal>
      <MaterialModal
        title="选择素材"
        open={materialModalOpen}
        onOk={handleMaterialOk}
        onCancel={handleMaterialCancel}
        shopId={shopId}
        moduleType={currentScopeId || ''}
        defaultSelectedKeys={currentScopeId ? scopeMaterialMap[currentScopeId] || [] : []}
      />
    </>
  );
};

export default SendQwMsgModal;
