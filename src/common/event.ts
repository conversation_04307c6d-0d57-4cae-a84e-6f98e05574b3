import { useEffect, useRef } from 'react';
import EventEmitter from 'eventemitter3';

export const eventEmitter = new EventEmitter();

export type Callback = (...args: any) => void;

export function useEventHelper(eventName: string, callback: Callback, emitter?: EventEmitter) {
  const callbackRef = useRef<any>(callback);
  callbackRef.current = callback;
  useEffect(() => {
    const listener = (...args: any) => {
      return callbackRef?.current?.(...args);
    };
    if (eventName) {
      const em = emitter || eventEmitter;
      em.on(eventName, listener);
      return () => {
        em.off(eventName, listener);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventName]);
}
