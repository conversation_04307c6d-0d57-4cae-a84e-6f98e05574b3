import {
  Echo,
  pcFailModal,
  EchoEventType,
  CustomEchoEventEnum,
  IEventData,
} from '@alife/echo-client';

import { message } from 'antd';
import EventEmitter from 'eventemitter3';

export enum TeleStatusEnum {
  /** 振铃中 */
  RINGING = 'RINGING',
  /** 未接通 */
  NOT_CONNECTED = 'NOT_CONNECTED',
  /** 已接通 */
  CONNECTED = 'CONNECTED',
}
export const callStatusChangeKey = 'teleStatusChange';

export class EchoWorkBench extends EventEmitter {
  echo = null as unknown as Echo;
  telStatus: TeleStatusEnum;
  private cacheCall: null | (() => void) = null;
  init() {
    this.echo.on(EchoEventType.failCall, this.onCallFail.bind(this));
    this.echo.on(EchoEventType.hangUp, this.onHangup.bind(this));
    this.echo.on(EchoEventType.customerPick, this.onCustomerPick.bind(this));
    this.echo.on(EchoEventType.agentRinging, this.onAgentRinging.bind(this));
    this.echo.on(CustomEchoEventEnum.ECHO_ON_ERROR, this.onError.bind(this));
    if (this.cacheCall) {
      this.cacheCall?.();
      this.cacheCall = null;
    }
  }
  async call(callInfo: any) {
    const { echo } = this;
    if (!echo) {
      this.cacheCall = () => {
        this.call(callInfo);
      };
      return;
    }
    if (echo.isLogin) {
      // @ts-ignore 透传参数
      return echo.call(callInfo);
    } else {
      message.error('通话组件离线, 请恢复为空闲状态或刷新页面重试');
      return new Error('通话组件离线, 请恢复为空闲状态或刷新页面重试');
      // return new Promise((resolve, reject) => {
      //   const loginFun = (data) => {
      //     echo.off(EchoEventType.login, loginFun);
      //     if (data?.success) {
      //       // @ts-ignore 透传参数
      //       echo.call(callInfo);
      //       resolve(data);
      //     } else {
      //       reject(data);
      //     }
      //   };
      //   this.echo.on(EchoEventType.login, loginFun);
      // });
    }
  }

  onAgentRinging() {
    this.changeTeleStatus(TeleStatusEnum.RINGING);
  }

  onError(data: IEventData) {
    pcFailModal(data, () => {
      message.error({
        content: `通话异常: ${data.message || '未知原因，请联系技术排查'}`,
      });
    });
  }

  onCallFail() {
    this.changeTeleStatus(TeleStatusEnum.NOT_CONNECTED);
  }

  onHangup() {
    if (this.telStatus === TeleStatusEnum.CONNECTED) {
      // CloudCallEventCenter.notify('onCallRelease', {});
    } else {
      this.changeTeleStatus(TeleStatusEnum.NOT_CONNECTED);
    }
  }

  onCustomerPick() {
    this.changeTeleStatus(TeleStatusEnum.CONNECTED);
  }

  destroy() {
    this.echo.destroy();
  }

  private changeTeleStatus(status: TeleStatusEnum, msg?: string) {
    // 从响铃变成终态或者从任何状态变成响铃才是可以修改的
    if (TeleStatusEnum.RINGING === this.telStatus || status === TeleStatusEnum.RINGING) {
      this.telStatus = status;
    }
    this.emit(callStatusChangeKey, status, msg);
  }
}

export const echoWorkbench = new EchoWorkBench();
