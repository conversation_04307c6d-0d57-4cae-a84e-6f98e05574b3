export class Msg2Promise {
  promise: Promise<any>;
  isFinished = false;
  constructor() {
    this.init();
  }

  start() {
    this.init();
    setTimeout(() => {
      if (!this.isFinished) {
        this.reject('超时');
      }
    }, 8000);
  }

  init() {
    this.isFinished = false;
    this.promise = new Promise((resolve, reject) => {
      this._resolve = resolve;
      this._reject = reject;
    });
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _resolve(v: any) {}
  resolve(v: any) {
    this.isFinished = true;
    this._resolve(v);
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _reject(error: any) {}
  reject(error: any) {
    this.isFinished = true;
    this._reject(error);
  }
}
