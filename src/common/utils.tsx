import { uuid, openParentPage } from '@alife/kb-biz-util';
import { Env, SHOP_LABEL_MAP, TASK_STATUS, TASK_STATUS_KEY } from './const';
import { ReactElement } from 'react';
import NP from 'number-precision';
import Cookies from 'js-cookie';
import { isAmapXy } from '@/utils';
import { BusinessDataType } from '@/pages/business-news/components/const';
import { parse } from 'query-string';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';

const saveKey = `xyTaskPCStorage${window.APP?.uvUserId}`;

export function getUriParam(param: string, url?: string) {
  let uri;
  if (url) {
    uri = url;
  } else {
    uri = location.hash.indexOf(param) > -1 ? location.hash : location.search;
  }
  const basePageUrl = `${uri
    .replace(/[#\?].*/, '')
    .replace(/[^\/]+$/, (part) => {
      return /[^\/]$/.test(part) ? '' : part;
    })
    .replace(/\/+$/, '')}/`;

  const reg = new RegExp(`(^|&?)${param}=([^&|#]*)(&|#|$)`);
  const r = uri.replace(basePageUrl, '').substr(1).match(reg);
  if (r !== null) {
    return decodeURIComponent(r[2]);
  }
  return null;
}

export const getBaseRequestParam = () => {
  return {
    appSource: 'xy-client',
    requestId: uuid(),
  };
};

export const domainEndsWith = (...ending: string[]) => {
  const domain = window.location.hostname.toString().toLowerCase();
  return ending.some((x) => domain.endsWith(x.toLowerCase()));
};

export const domainStartsWith = (...ending: string[]) => {
  const domain = window.location.hostname.toString().toLowerCase();
  return ending.some((x) => domain.startsWith(x.toLowerCase()));
};

/**
 * 从当前页面 url 识别环境
 */
export const getEnv = () => {
  const host = window.location.hostname.toLowerCase();
  if (
    domainEndsWith('.koubei.test') ||
    domainStartsWith('daily-') ||
    host === 'kbservcenter.faas.daily.elenet.me'
  ) {
    return Env.Dev;
  }

  return host.includes('pre-') || host.includes('ppe-') ? Env.Pre : Env.Prod;
};

export const formatResultDateRange = (dateRange: any[]) => {
  if (dateRange?.length === 2) {
    let dateFormat;
    if (dateRange[0].year() === dateRange[1].year()) {
      dateFormat = 'M月D日';
    } else {
      dateFormat = 'YYYY年M月D日';
    }
    const start = dateRange[0].format(dateFormat);
    const end = dateRange[1].format(dateFormat);
    if (start === end) {
      return start;
    } else {
      return `${start}至${end}`;
    }
  }
  return '';
};

/**
 * 获取值，如果过期则进行删除并返回undefined
 * @param {string} key
 * @return value
 */
export function getStorage(key: any) {
  try {
    const storage = JSON.parse(localStorage.getItem(saveKey));
    const { value, expired, time } = storage[key];
    if (expired === 0 || time + expired > Date.now()) {
      // 没有过期
      return value;
    }
    delete storage[key]; // 过期的删除
    localStorage.setItem(saveKey, JSON.stringify(storage));
    return undefined;
  } catch (e) {
    return undefined;
  }
}

/**
 * 本地存储
 * @param {string} key
 * @param {*} value
 * @param {[number]} expired 过期ms值，expired为0则永不过期，设置为-1则删除
 *
 */
export function setStorage(key: any, value: any, expired = 0) {
  const storage = JSON.parse(localStorage.getItem(saveKey)) || {};
  if (expired === -1) {
    delete storage[key]; // 过期的删除
  } else {
    storage[key] = { value, expired, time: Date.now() };
  }
  try {
    localStorage.setItem(saveKey, JSON.stringify(storage));
  } catch (e) {
    // 可能是存储满了
    console.error(e);
    localStorage.removeItem(saveKey);
    // localStorage.clear(saveKey)
  }
}

/**
 * 跳转其他业务域
 * @param originUrl 跳转地址
 */
export const jumpExternalUrl = (url: string) => {
  if (url.startsWith('?')) {
    const urlObj = parse(url);
    if (urlObj?.openDrawer) {
      emitter.emit(EmitterEventMap.OpenDrawer, urlObj);
      return;
    }
  }
  window.open(url, '_blank');
};

/**
 * 商户标签
 * @param  value 商户code
 */
export const getMerchantLabels = (value: string) => {
  if (value === 'alipay_touch') {
    return (
      <img
        src="https://img.alicdn.com/imgextra/i2/O1CN01hXSM5J1yyq7IRAoNj_!!6000000006648-2-tps-1898-788.png"
        alt="支付宝碰一下"
        style={{
          height: '23px',
          marginRight: '4px',
          verticalAlign: 'middle',
          borderRadius: '4px',
          border: '1px solid #1A66FF',
        }}
      />
    );
  }
  let merchantLabels = {
    color: '',
    value: '',
  };
  switch (value) {
    case 'has_online_wpT5':
      merchantLabels = {
        color: '#FA5555',
        value: '商户通',
      };
      break;
    case 'has_ad_serving_30d':
      merchantLabels = {
        color: '#108ee9',
        value: '广告在投',
      };
      break;
    case 'has_shop_complete':
      merchantLabels = {
        color: '#87d068',
        value: '基建达标',
      };
      break;
    case 'trade_high_value':
      merchantLabels = {
        color: '#FA5555',
        value: SHOP_LABEL_MAP.trade_high_value,
      };
      break;
    case 'has_wang_pu':
      merchantLabels = {
        color: '#2f56b7',
        value: '高德旺铺',
      };
      break;
    case 'has_online_wang_pu':
      merchantLabels = {
        color: '#2f56b7',
        value: '高德旺铺',
      };
      break;
    default:
      merchantLabels = {
        color: '',
        value: '',
      };
      break;
  }
  return merchantLabels;
};

/*
 * 自定义事件埋点
 */
export const sendEvent = (
  eventId: string,
  eventType: 'EXP' | 'CLK' | 'SLD' | 'INPUT' | 'SYS' | 'OTHER',
  params = {},
) => {
  window.microAppUtils?.aes?.sendEvent?.(eventId, {
    et: eventType,
    ...params,
  });
};

/**
 * 功能推荐跳转其他业务域、策略建议带pid跳转广告伪登陆后台
 * @param originUrl 跳转地址
 */
export const jumpOtherUrl = (
  params: Partial<{
    url: string;
    pid: string;
    newUrl: string;
    isOut: boolean;
    prefix: string;
  }>,
) => {
  const { url: _url, pid, newUrl, isOut, prefix } = params;
  if (_url.startsWith('?')) {
    const urlObj = parse(_url);
    if (urlObj?.openDrawer) {
      emitter.emit(EmitterEventMap.OpenDrawer, urlObj);
      return;
    }
  }
  let url = newUrl || _url;
  if (prefix && getEnv() === Env.Pre) {
    const regex = /^(https?:\/\/)(.*)$/i;
    url = url.replace(regex, `$1${prefix}$2`);
  }
  if (isOut) {
    window.open(`${url}${pid ? `?pid=${pid}` : ''}`);
    return;
  }
  // eslint-disable-next-line no-nested-ternary
  const { origin } = location;
  window.open(`${origin}/${url}${pid ? `?pid=${pid}` : ''}`);
};

export enum BusinessNewType {
  TRADE = 'TRADE',
  CONSULT = 'CONSULT',
  FOOD = 'FOOD',
}

export const formatCompareValue = (
  value: any,
): [string | null, ReactElement | string | null, string] => {
  if (value === '--') {
    return ['', '', '--'];
  }
  const val = parseFloat(value as any);
  // eslint-disable-next-line no-restricted-globals
  if (!val) {
    return ['', '', '--'];
  }
  if (Math.round(val * 100) === 0) {
    return ['', '', '--'];
  }

  const resultVal = `${(Math.abs(val) * 100).toFixed(0)}%`;

  if (val > 0) {
    return ['#f00', '+', resultVal];
  }
  if (val < 0) {
    return ['#69bf47', '-', resultVal];
  }

  return ['', '', resultVal];
};

export const getBusinessNewsData = (data: any, key: string, isCompareValue = false) => {
  const item = data[key];
  const value = item?.value;
  const isEmptyValue = typeof value === 'undefined' || value === null;
  if (isCompareValue) {
    const compareValue = item?.compareValue;
    if (!isEmptyValue && typeof compareValue !== 'undefined' && compareValue !== null) {
      const [compareColor, arrow, parsedCompareValue] = formatCompareValue(compareValue);
      // 环比为空和'--'文案都不展示环比
      if (parsedCompareValue === '--') {
        return '';
      }
      return `（<span style="color: ${compareColor};">${arrow}${parsedCompareValue}</span>）`;
    }
    return '';
  } else {
    return isEmptyValue ? '--' : `<strong>${value}</strong>`;
  }
};

/**
 * 数据汇总-话术模板
 */
export const getSummarizedTemplate = ({ dataList, dataDescList, businessNewType, timeInfo }) => {
  const data = {};
  (dataList || []).forEach((rowData) => {
    rowData.forEach((colData) => {
      data[colData.key] = colData;
    });
  });

  const { startTime, endTime } = timeInfo || {};
  const timeStr = startTime && endTime ? `在${startTime} 到 ${endTime} 期间，` : '';
  const title = `<blockquote>${timeStr}您的店铺经营情况如下：</blockquote>`;

  let amapAndBusinessSummary = '';
  let adDataSummary = '';

  const adIndex = (dataDescList || []).findIndex((desc) => {
    return desc.type === BusinessDataType.AD_DATA;
  });
  const hasAdData = adIndex !== -1 && dataList[adIndex]?.length > 0;

  switch (businessNewType) {
    case BusinessNewType.TRADE:
      amapAndBusinessSummary = `${title}<p>在高德地图上，获得了${getBusinessNewsData(
        data,
        'gd_exp_pv',
      )}次导航搜索量${getBusinessNewsData(data, 'gd_exp_pv', true)}、${getBusinessNewsData(
        data,
        'gd_total_consult_pv',
      )}次咨询量${getBusinessNewsData(data, 'gd_total_consult_pv', true)}、${getBusinessNewsData(
        data,
        'gd_navi_pv',
      )}次导航到店量${getBusinessNewsData(
        data,
        'gd_navi_pv',
        true,
      )}</p><p>在高德地图、支付宝、口碑上，共${getBusinessNewsData(
        data,
        'ord_cnt',
      )}笔成交订单${getBusinessNewsData(data, 'ord_cnt', true)}</p>`;
      adDataSummary = hasAdData
        ? `<p>您通过广告投放，共消耗${getBusinessNewsData(data, 'ad_cost')}元${getBusinessNewsData(
            data,
            'ad_cost',
            true,
          )}，获得了${getBusinessNewsData(data, 'ad_exp_pv')}次门店展现${getBusinessNewsData(
            data,
            'ad_exp_pv',
            true,
          )}、${getBusinessNewsData(data, 'ad_clk_pv')}次门店访问${getBusinessNewsData(
            data,
            'ad_clk_pv',
            true,
          )}、${getBusinessNewsData(
            data,
            'ad_arrive_intention_cnt',
          )}个到店意向顾客${getBusinessNewsData(
            data,
            'ad_arrive_intention_cnt',
            true,
          )}、${getBusinessNewsData(data, 'ad_call_cnt')}个电话预订${getBusinessNewsData(
            data,
            'ad_call_cnt',
            true,
          )}</p>`
        : '';
      break;
    case BusinessNewType.CONSULT:
      amapAndBusinessSummary = `${title}<p>在高德地图上，获得了${getBusinessNewsData(
        data,
        'gd_exp_pv',
      )}次导航搜索量${getBusinessNewsData(data, 'gd_exp_pv', true)}、${getBusinessNewsData(
        data,
        'gd_navi_pv',
      )}次导航到店量${getBusinessNewsData(
        data,
        'gd_navi_pv',
        true,
      )}</p><p>在高德地图、支付宝、口碑上，共${getBusinessNewsData(
        data,
        'ord_cnt',
      )}笔成交订单${getBusinessNewsData(data, 'ord_cnt', true)}、${getBusinessNewsData(
        data,
        'cust_res_cnt',
      )}个客资量${getBusinessNewsData(data, 'cust_res_cnt', true)}、${getBusinessNewsData(
        data,
        'comment_cnt',
      )}个新增评论数${getBusinessNewsData(data, 'comment_cnt', true)}</p>`;
      adDataSummary = hasAdData
        ? `<p>您通过广告投放，共消耗${getBusinessNewsData(data, 'ad_cost')}元${getBusinessNewsData(
            data,
            'ad_cost',
            true,
          )}，客资成本${getBusinessNewsData(data, 'ad_cost_per_cust')}元/人${getBusinessNewsData(
            data,
            'ad_cost_per_cust',
            true,
          )}，获得了${getBusinessNewsData(data, 'ad_exp_pv')}次门店展现${getBusinessNewsData(
            data,
            'ad_exp_pv',
            true,
          )}、${getBusinessNewsData(data, 'ad_clk_pv')}次门店访问${getBusinessNewsData(
            data,
            'ad_clk_pv',
            true,
          )}、${getBusinessNewsData(data, 'visible_cust_res_cnt')}个门店客资${getBusinessNewsData(
            data,
            'visible_cust_res_cnt',
            true,
          )}，很可惜您错失了${getBusinessNewsData(
            data,
            'miss_cust_res_cnt',
          )}个客资${getBusinessNewsData(data, 'miss_cust_res_cnt', true)}</p>`
        : '';
      break;
    case BusinessNewType.FOOD:
      amapAndBusinessSummary = `${title}<p>在高德地图上，获得了${getBusinessNewsData(
        data,
        'gd_exp_pv',
      )}次曝光量${getBusinessNewsData(data, 'gd_exp_pv', true)}、${getBusinessNewsData(
        data,
        'shop_vst_pv',
      )}次访问量${getBusinessNewsData(data, 'shop_vst_pv', true)}、${getBusinessNewsData(
        data,
        'gd_navi_pv',
      )}次到店量${getBusinessNewsData(
        data,
        'gd_navi_pv',
        true,
      )}</p><p>在高德地图上，获得了${getBusinessNewsData(
        data,
        'ticket_order_cnt',
      )}个预约到店量${getBusinessNewsData(
        data,
        'ticket_order_cnt',
        true,
      )}，其中，${getBusinessNewsData(data, 'phone_order_cnt')}个电话预订量${getBusinessNewsData(
        data,
        'phone_order_cnt',
        true,
      )}、${getBusinessNewsData(data, 'online_order_cnt')}个在线总预订量${getBusinessNewsData(
        data,
        'online_order_cnt',
        true,
      )}、${getBusinessNewsData(data, 'all_order_cnt')}个团购总订单量${getBusinessNewsData(
        data,
        'all_order_cnt',
        true,
      )}</p>`;
      adDataSummary = hasAdData
        ? `<p>您通过广告投放，共消耗${getBusinessNewsData(data, 'ad_cost')}元${getBusinessNewsData(
            data,
            'ad_cost',
            true,
          )}，预约到店成本为${getBusinessNewsData(data, 'ad_cost_rate')}元${getBusinessNewsData(
            data,
            'ad_cost_rate',
            true,
          )}，获得了${getBusinessNewsData(data, 'ad_exp_pv')}次曝光量${getBusinessNewsData(
            data,
            'ad_exp_pv',
            true,
          )}、${getBusinessNewsData(data, 'ad_clk_pv')}次访问量${getBusinessNewsData(
            data,
            'ad_clk_pv',
            true,
          )}</p>`
        : '';
      break;
    default:
      break;
  }
  return amapAndBusinessSummary + adDataSummary;
};

/**
 * 广告建议-话术模板
 */
export const getAdTemplate = () =>
  '<blockquote>广告建议：</blockquote><p>续充：</p><p>提出价：<span style="color: rgb(187, 187, 187);">建议明确数值</span></p><p>提预算：<span style="color: rgb(187, 187, 187);">建议明确数值</span></p>';

/**
 * 数据汇总-话术模板
 */
export const getTaskTemplate = (taskList: any) => {
  if (taskList?.length > 0) {
    let taskHtml = '';
    for (let i = 0; i < taskList.length && i < 5; i++) {
      const task = taskList[i];
      taskHtml += `<li>${task.title}</li>`;
    }
    return `<blockquote>您的门店需要对以下几个方面进行优化：</blockquote><ol>${taskHtml}</ol>`;
  }
  return '';
};

/**
 * 判断小结与分析富文本内容是否为空
 */
export const isContentVisuallyEmpty = (htmlContent: any) => {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  // 移除所有换行符
  tempDiv.querySelectorAll('br').forEach((item) => item.remove());
  // 深度清理所有元素，删除空元素
  function deepClean(node) {
    for (let i = 0; i < node.childNodes.length; i++) {
      const child = node.childNodes[i];
      if (child.nodeType === Node.ELEMENT_NODE) {
        // 保留视觉上非空的特殊元素
        if (['img', 'ul', 'ol'].includes(child.tagName.toLowerCase())) {
          continue;
        }
        deepClean(child);
        if (!child.textContent.trim().length && !child.querySelector('*')) {
          node.removeChild(child);
          i--;
        }
      } else if (child.nodeType === Node.TEXT_NODE && !child.textContent.trim().length) {
        node.removeChild(child);
        i--;
      }
    }
  }

  deepClean(tempDiv);
  // 检查是否还有剩余元素或文本
  const hasText = tempDiv.textContent.trim().length > 0;
  const hasElements = tempDiv.querySelector('*') !== null;
  return !(hasText || hasElements);
};

// 除
export function safeDivide(value: any, multiple: number): number | null {
  try {
    const val = +value;
    if (window.isNaN(val)) {
      throw new Error('value must be a number');
    }
    const newNmu = NP.divide(val, multiple);
    return newNmu;
  } catch (error) {
    return value;
  }
}
// 乘
export function safeMultiply(value: any, dividend: number): any {
  try {
    const val = +value;
    if (window.isNaN(val)) {
      throw new Error('value must be a number');
    }
    const newNmu = NP.times(val, dividend);
    return newNmu;
  } catch (error) {
    return value;
  }
}

/**
 * 获取门店详情链接
 * @param shopId 门店id
 */
export const getShopDetailLink = (shopId: string) => {
  if (isAmapXy()) {
    return `https://${
      getEnv() === Env.Pre ? 'pre-' : ''
    }xy.amap.com/micro/app/alsc-merchants/kb-shop?hideMenu=true&from=telemarketing&spm=amap.telemarketing-workspace-home#/shop/detail/${shopId}`;
  } else {
    return `https://${
      getEnv() === Env.Pre ? 'ppe-' : ''
    }kbservcenter.faas.ele.me/micro/app/alsc-merchants/kb-shop?from=telemarketing#/shop/detail/${shopId}`;
  }
};

export const getWangpuShopDetailLink = (shopId: string) => {
  return `https://${
    getEnv() === Env.Pre ? 'pre-' : ''
  }xy.amap.com/sale-pc/mp-mono-shop-mng/app-my-shop-pc/shopDetails?shopType=SUCCESS_SHOP&shopId=${shopId}&hideMenu=true&from=telemarketing&spm=amap.telemarketing-workspace-home`;
};

/**
 * 获取经营状态灯icon
 * @param value 经营状态灯code
 */
export const getOperatingStateIcon = (value: string) => {
  let operatingStateIcon = '';
  switch (value) {
    case 'RED_YELLOW_LAMP':
      operatingStateIcon =
        'https://gw.alicdn.com/imgextra/i4/O1CN01K2aOwG28OKYjBT5QL_!!6000000007922-2-tps-64-64.png';
      break;

    case 'YELLOW_LAMP':
      operatingStateIcon =
        'https://gw.alicdn.com/imgextra/i3/O1CN01p4dU7H1jz5xSO1SLY_!!6000000004618-2-tps-64-64.png';
      break;
    case 'GREEN_YELLOW_LAMP':
      operatingStateIcon =
        'https://gw.alicdn.com/imgextra/i2/O1CN01lICQYy29mR1HXWIvI_!!6000000008110-2-tps-64-64.png';
      break;
    default:
      operatingStateIcon = '';
      break;
  }
  return operatingStateIcon;
};

/**
 * 获取经营状态灯icon
 * @param value 经营状态灯code
 */
export const getOperatingStateIconNew = (value: string) => {
  let operatingStateIcon = '';
  switch (value) {
    case 'RED_LAMP':
      operatingStateIcon =
        'https://gw.alicdn.com/imgextra/i4/O1CN01K2aOwG28OKYjBT5QL_!!6000000007922-2-tps-64-64.png';
      break;

    case 'YELLOW_LAMP':
      operatingStateIcon =
        'https://gw.alicdn.com/imgextra/i3/O1CN01p4dU7H1jz5xSO1SLY_!!6000000004618-2-tps-64-64.png';
      break;
    case 'GREEN_LAMP':
      operatingStateIcon =
        'https://gw.alicdn.com/imgextra/i2/O1CN01lICQYy29mR1HXWIvI_!!6000000008110-2-tps-64-64.png';
      break;
    default:
      operatingStateIcon = '';
      break;
  }
  return operatingStateIcon;
};

export const getNewBaseListRequestParam = (
  pageSize: number,
  pageNum: number,
  // taskStatusKey = TASK_STATUS_KEY.COMMON,
  // taskStatus = TASK_STATUS.ALL,
) => {
  return {
    page: {
      pageNo: pageNum,
      pageSize,
    },
    // [taskStatusKey]: taskStatus,
  };
};

// 下载
export const downloadUtil = (url: string) => {
  const linkNode = document.createElement('a');
  linkNode.style.display = 'none';
  linkNode.href = url;
  document.body.appendChild(linkNode);
  linkNode.click();
  document.body.removeChild(linkNode);
};

// 判断是否为代理商登陆
export const isAgent = () => {
  // COFFEE_TOKEN, 饿了么员工使用token
  if (/xy.amap/.test(location.host)) {
    return window?.APP?.operatorType === 'AMAP';
  } else {
    return !Cookies.get('COFFEE_TOKEN');
  }
};

// 判断是31服务商登陆
export const isAmapAgent = () => {
  /**
   * WANGPU: 31 代理商
   * SHANGHUTONG: 33 代理商
   */
  const role = (window?.APP?.sellProduct as Array<'WANGPU' | 'SHANGHUTONG'>) || [];
  return role.length === 1 && role.includes('WANGPU');
};

// 打开下载中心链接页面的方式
export function openDownLoadPage(bizCode: string) {
  const isAmap = isAmapXy();
  const route = isAmap
    ? 'sale-pc/ego-qiankun-subapp/aslc-xy-pc/exportList'
    : 'ego-launch-app/aslc-xy-pc/exportList';
  // eslint-disable-next-line no-nested-ternary
  const origin = isAmap
    ? location.origin
    : getEnv() === Env.Pre
    ? 'https://ppe-xy.ele.me'
    : 'https://xy.ele.me';
  const url = `${origin}/${route}?bizCode=${bizCode}`;
  jumpOtherUrl({
    url: `xy-kbxzzx?url=${encodeURIComponent(url)}`,
    newUrl: `sale-pc/ego-qiankun-subapp/aslc-xy-pc/exportList?bizCode=${bizCode}`,
  });
}

/**
 * 手机号脱敏展示
 * @param num
 * @returns
 */
export const hidePhoneNum = (num: string | number = '') => {
  if (!num) {
    return '--';
  }
  const $num = String(num);
  if (/[0-9]+/.test($num)) {
    if ($num.length === 11) {
      return `${$num.slice(0, 3)} **** ${$num.slice(7)}`;
    } else {
      return `${$num.slice(0, 3)} **** ****`;
      // const { length } = $num;
      // return `${$num.slice(0, length - 4)}****`;
    }
  } else {
    return $num;
  }
};

export const getKpFlag = (contact) => {
  const { isKp, kpFlag } = contact;
  if (isKp) {
    // eslint-disable-next-line no-nested-ternary
    return isKp === '1' ? 'YES' : isKp === '2' ? 'UNKNOWN' : 'NO';
  } else {
    return kpFlag === true ? 'YES' : 'NO';
  }
};

export const isOuterPage = () => {
  if (!window?.__POWERED_BY_QIANKUN__) {
    return true;
  }
  return false;
};
