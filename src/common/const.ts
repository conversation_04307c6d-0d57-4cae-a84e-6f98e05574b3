import { isAmapXy } from '@/utils';

export const TASK_STATUS = {
  ALL: 'ALL',
  COMPLETED: 'COMPLETED',
  INCOMPLETE: 'INCOMPLETE',
};

/**
 * 商户标签
 */
export const MERCHANT_TAG_OPTIONS = [
  {
    title: '新签',
    key: 'NEW_SIGN',
  },
  {
    title: '30天广告在投',
    key: 'AD_PRODUCT_IN_30D',
  },
  {
    title: '停投',
    key: 'STOP_DROPPING',
  },
  {
    title: 'CPS',
    key: 'CPS',
  },
  {
    title: '存量纯年费',
    key: 'ANNUAL_FEE',
  },
  {
    title: '新签纯年费',
    key: 'ANNUAL_FEE_NEW',
  },
];

/**
 * 服务进度
 */
export const SERVICE_PROGRESS_OPTIONS = [
  {
    title: '全部',
    key: TASK_STATUS.ALL,
  },
  {
    title: '已完成',
    key: TASK_STATUS.COMPLETED,
  },
  {
    title: '未完成',
    key: TASK_STATUS.INCOMPLETE,
  },
];

/**
 * 服务满意度
 */
export const SERVICE_SATISFACTION_OPTIONS = [
  {
    title: '急需提升',
    key: 'URGENT_NEED_PROMOTION',
  },
  {
    title: '需要提升',
    key: 'NEED_PROMOTION',
  },
  {
    title: '一般',
    key: 'GENERAL',
  },
  {
    title: '良好',
    key: 'FINE',
  },
  {
    title: '优秀',
    key: 'EXCELLENCE',
  },
];

/**
 * 经营状态
 */
export const MERCHANT_OPERATING_STATE_OPTIONS = [
  {
    title: '红灯',
    key: 'RED_YELLOW_LAMP',
  },
  {
    title: '黄灯',
    key: 'YELLOW_LAMP',
  },
  {
    title: '绿灯',
    key: 'GREEN_YELLOW_LAMP',
  },
];

/**
 * 运营建议
 */
export const OPERATING_SUGGESTIONS_OPTIONS = [
  {
    title: '优秀',
    key: 'EXCELLENCE',
  },
  {
    title: '关注商家数据',
    key: 'FOCUS_MERCHANT_DATA',
  },
  {
    title: '提升服务质量，关注商家数据',
    key: 'IMPROVE_SERVICE_QUALITY_FOCUS_MERCHANT_DATA',
  },
  {
    title: '提升服务质量',
    key: 'IMPROVE_SERVICE_QUALITY',
  },
];

/**
 * 商业化投放状态枚举
 */
export const COMMERCIALIZATION_TAGS_ENUM = {
  KBWANGPUT5: 'KB_WANGPU_T5',
  ADVERTISING: 'ADVERTISING_PRODUCT_30_DAYS',
  ANNUAl_FEE_ONLINE: 'ANNUAL_FEE_ONLINE',
};

/**
 * 商业化投放状态枚举
 */
export const COMMERCIALIZATION_TAGS_OPTIONS = [
  !isAmapXy()
    ? {
        title: '五端商户通在线',
        key: COMMERCIALIZATION_TAGS_ENUM.KBWANGPUT5,
      }
    : {
        title: '年费在线',
        key: COMMERCIALIZATION_TAGS_ENUM.ANNUAl_FEE_ONLINE,
      },
  {
    title: '30天广告在投',
    key: COMMERCIALIZATION_TAGS_ENUM.ADVERTISING,
  },
];

export const TASK_STATUS_KEY = {
  SHOP: 'shopTaskStatus',
  COMMON: 'taskStatus',
};

export enum PAGE_STATUS {
  EMPTY = 'EMPTY',
  ERROR = 'ERROR',
  SUCCESS = 'SUCCESS',
  LOADING = 'LOADING',
}

/**
 * 当前的环境
 */
export const enum Env {
  Dev = 'dev',
  Pre = 'pre',
  Prod = 'prod',
}

/**
 * 任务详情引导弹框类型枚举
 */
export enum JUMP_TYPE {
  LINK = 'LINK',
  PICTURE = 'PICTURE',
  TEXT = 'TEXT',
  PIC = 'PIC',
}

/**
 * 核心数据名称枚举
 */
export enum OVERVIEW_NAME {
  INCOME = 'income',
  ADMERCHANT = 'adMerchant',
  INFRASTRUCTURE = 'infrastructure',
}

export const ALL_SHOPS = '全部门店';

// 广告伪登录后台链接
export const TGMS_URL = 'https://tgms.amap.com/bs/home/<USER>';

/**
 * 功能推荐
 */
export const RECOMMENDATION_DATA = [
  {
    title: '商品管理',
    desc: '设置套餐、代金券等商品',
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01POXIsF1xH2RIEsJUV_!!6000000006417-2-tps-162-162.png',
    url: 'xy-kbspgl',
    newUrl: 'micro/app/alsc-merchants/kb-item-prod-pc#/',
  },
  {
    title: '内容装修',
    desc: '设置门店相册、视频等',
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01xLn6TT22Ah20u2FIo_!!6000000007080-2-tps-159-159.png',
    url: 'xy-kbnrzx',
    newUrl: 'micro/app/alsc-merchants/wangpu-dyy#/main',
  },
  {
    title: '我的门店',
    desc: '设置门店基础信息',
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN016VFeed25utpxkiu9H_!!6000000007587-2-tps-162-162.png',
    url: 'xy-wdmd',
    newUrl: 'micro/app/alsc-merchants/kb-shop#/shop/my',
  },
  // {
  //   title: '医生管理',
  //   desc: '设置医生',
  //   icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01oLnT1o1wcjZRGEeuz_!!6000000006329-2-tps-108-108.png',
  //   url: 'xy-kbysgl',
  //   newUrl: 'micro/app/alsc-merchants/doctormanage#/',
  // },
  // {
  //   title: '人物管理',
  //   desc: '设置手艺人',
  //   icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01do7tZi1j9nXfo5z0O_!!6000000004506-2-tps-84-84.png',
  //   url: 'xy-kbsyrgl',
  //   newUrl:
  //     'kb-pc/mp-moda/pc/manage?moduleTypeEnum=STYLIST&pid=2088531021318297&channel=KBSERVCENTER',
  // },
  {
    title: '美食订座',
    desc: '设置美食订座',
    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01Nw43If1rAGsJxBmxA_!!6000000005590-2-tps-168-168.png',
    url: 'xy-kbmsdzgl',
    newUrl: 'micro/app/alsc-merchants/alsc-store-booking#/seat-manage',
  },
  {
    title: '预约礼',
    desc: '设置预约礼',
    icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01VwAzbe240jdSDMLxA_!!6000000007329-2-tps-84-84.png',
    newUrl: 'micro/app/alsc-merchants/kb-market-v3#/create-activity',
    url: isAmapXy() ? '' : TGMS_URL,
    prefix: isAmapXy() ? '' : 'pre-',
    isOut: !isAmapXy(),
  },
];

/**
 * 任务管理首页tab枚举
 */
export enum TASK_TABPAN_ENUM {
  MERCHANT = 'merchant',
  SHOP = 'shop',
}

/**
 * 任务管理首页tab
 */
export const TASK_TAB_OPTIONS = [
  {
    title: '商户列表',
    key: TASK_TABPAN_ENUM.MERCHANT,
  },
  {
    title: '门店列表',
    key: TASK_TABPAN_ENUM.SHOP,
  },
];

/**
 * 门店商家分枚举
 */
export enum SHOP_SCORE_CONDITION_ENUM {
  BELOWTHREE = 'BELOW_THREE',
  BELOWFOUR = 'BELOW_FOUR',
  OVERSIX = 'OVER_SIX',
  ABNORMAL = 'ABNORMAL',
  DELINE = 'DELINE',
  FOURTOFIVE = 'FOUR_TO_FIVE',
  FIVETOSIX = 'FIVE_TO_SIX',
  BELOWSIX = 'BELOW_SIX',
}

/**
 * 门店商家分筛选
 */
export const SHOP_SCORE_CONDITION_OPTIONS = [
  {
    title: '3分以下',
    value: SHOP_SCORE_CONDITION_ENUM.BELOWTHREE,
  },
  {
    title: '4分以下',
    value: SHOP_SCORE_CONDITION_ENUM.BELOWFOUR,
  },
  {
    title: '6分以下',
    value: SHOP_SCORE_CONDITION_ENUM.BELOWSIX,
  },
  {
    title: '6分及以上',
    value: SHOP_SCORE_CONDITION_ENUM.OVERSIX,
  },
];

/**
 * 门店商家分多选框筛选
 */
export const SHOP_SCORE_OPTIONS = [
  {
    label: '商家分异动',
    value: SHOP_SCORE_CONDITION_ENUM.ABNORMAL,
  },
  {
    label: '商家分下降>0.5分',
    value: SHOP_SCORE_CONDITION_ENUM.DELINE,
  },
];
/**
 * 支付状态枚举
 */
export enum Payment_Status {
  INIT = '待支付',
  NOT_NEED = '无须支付',
  PAY_SUCCESS = '支付成功',
  PAY_FAILED = '支付失败',
}

/**
 * 排序字段枚举
 */
export enum SORT_BY_STATUS {
  MONTHCOST = 'AD_CURRENT_MONTH_COST', // 当月广告总消耗
  CURRENTBALANCE = 'AD_CURRENT_BALANCE', // 广告现金余额
  QUANTITYSCORE = 'QUANTITY_SCORE', // 门店商家分
  SHANGHUTONGENDTIME = 'SHANG_HU_TONG_END_TIME', // 商户通到期时间
  SIGN_EFFECTIVE_TIME = 'SIGN_EFFECTIVE_TIME', // 签约生效时间
}

/**
 * 商户列表升序降序枚举
 */
export enum MERCHANT_SORT_TYPE_STATUS {
  ASC = 'ASC', // 升序
  DESC = 'DESC', // 降序
}

/**
 * 门店列表升序降序枚举
 */
export enum SHOP_SORT_TYPE_STATUS {
  ASC = 'asc', // 升序
  DESC = 'desc', // 降序
}

/**
 * 续签状态枚举
 */
export enum MERCHANT_EXPIRE_CONDITION_ENUM {
  EXPIREINSEVENDAY = 'EXPIRE_IN_SEVEN_DAY',
  EXPIREINTHIRTYDAY = 'EXPIRE_IN_THIRTY_DAY',
  EXPIREBWTHIRTYANDSIXTYDAY = 'EXPIRE_BW_THIRTY_AND_SIXTY_DAY',
  EXPIREBWSIXTYANDNINETYDAY = 'EXPIRE_BW_SIXTY_AND_NINETY_DAY',
  EXPIREAFTERNINETYDAY = 'EXPIRE_AFTER_NINETY_DAY',
  EXPIRELTNINETYDAY = 'EXPIRE_LT_NINETY_DAY',
  EXPIREGTNINETYDAY = 'EXPIRE_GT_NINETY_DAY',
  RENEWALING = 'RENEWALING',
  RENEWALSUCCESS = 'RENEWAL_SUCCESS',
}

/**
 * 续签状态筛选
 */
export const MERCHANT_EXPIRE_CONDITION_ENUM_OPTIONS = [
  {
    title: '7日内到期',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.EXPIREINSEVENDAY,
  },
  {
    title: '30日内到期',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.EXPIREINTHIRTYDAY,
  },
  {
    title: '30-60日内到期',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.EXPIREBWTHIRTYANDSIXTYDAY,
  },
  {
    title: '60-90日内到期',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.EXPIREBWSIXTYANDNINETYDAY,
  },
  {
    title: '90日后到期',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.EXPIREAFTERNINETYDAY,
  },
  {
    title: '过期时间≤90日',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.EXPIRELTNINETYDAY,
  },
  {
    title: '过期时间＞90日',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.EXPIREGTNINETYDAY,
  },
  {
    title: '续签中',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.RENEWALING,
  },
  {
    title: '续签成功',
    value: MERCHANT_EXPIRE_CONDITION_ENUM.RENEWALSUCCESS,
  },
];

/**
 * 续签子状态枚举
 */
export const MERCHANT_SUB_STATUS = {
  AUDITING: '审核中',
  WAIT_PAY: '待商户确认并支付',
  WAIT_CHECK_PAY: '待认领款项',
};

/**
 * 数据看板
 */
export const TASK_BOARD = [
  {
    title: '广告看板入口：',
    child: [
      {
        title: '1、【到店商业化宽表_商户粒度】',
        url: 'https://fbi.alibaba-inc.com/dashboard/view/page.htm?spm=a2o1z.8190076.0.0.704b543fLiq3yy&id=1167449',
      },
      {
        title: ' 2、【到店商业化_门店粒度明细】',
        url: 'https://fbi.alibaba-inc.com/dashboard/view/page.htm?spm=a2o1z.8190076.0.0.3a45543fR607fL&id=1075149',
      },
      {
        title: '3、【预警&诊断看板】',
        url: 'https://fbi.alibaba-inc.com/dashboard/view/page.htm?spm=a2o1z.8190073.0.0.d7a0543fNgjN42&id=1194385#mll322lbf_gli0r4',
      },
      {
        title: '4、【本地客资通全量客资明细】',
        url: 'https://fbi.alibaba-inc.com/fbi/site/page.htm?id=5687&menuId=bidvqs71te8&tab=1&spm=a2o1z.8190076.0.0.4709543f2kTHME',
      },
      {
        title: '5、【平台客资看板】',
        url: 'https://fbi.alibaba-inc.com/fbi/site/page.htm?id=5687&menuId=uaftcrojjbo&tab=3&spm=a2o1z.8190076.0.0.4f95543fPj6jpR',
      },
    ],
  },
  {
    title: '基建看板入口：',
    child: [
      {
        title: '【高德经营数据分析平台】-（高德质量分）',
        url: 'https://fbi.alibaba-inc.com/fbi/site/page.htm?id=5687&menuId=2a3685q94eg&tab=3',
      },
    ],
  },
  {
    title: '管理看板入口：(仅主管及以上可使用)',
    child: [
      {
        title: '1、【广告业务】-管理者使用 ',
        url: 'https://fbi.alibaba-inc.com/fbi/site/page.htm?id=5687&menuId=tnj212j28s8&tab=3&spm=a2o1z.8190076.0.0.7610543fIVbtGa',
      },
      // {
      //   title: '2、【代运营任务监控看板】',
      //   url: 'https://fbi.alibaba-inc.com/dashboard/view/page.htm?spm=a2o1z.8189972.0.0.4a28543f4IXxVv&id=1294229',
      // },
    ],
  },
  {
    title: '查询工具：',
    child: [
      {
        title: '1、【商户客资标签】',
        url: 'https://fbi.alibaba-inc.com/fbi/site/page.htm?id=5687&menuId=uaftcrojjbo&tab=4',
      },
      {
        title: '2、【高德经营数据分析平台】-口碑挂接明细表 ',
        url: 'https://fbi.alibaba-inc.com/fbi/site/page.htm?id=5687&menuId=eg5tohels5&tab=3',
      },
    ],
  },
];

/**
 * 商户意向枚举
 */
export enum MERCHANT_INTENTION_ENUM {
  INTENTIONHIGH = 'INTENTION_HIGH',
  INTENTIONLOW = 'INTENTION_LOW',
  INTENTIONREFUSE = 'INTENTION_REFUSE',
  INTENTIONNOFEEDBACK = 'INTENTION_NO_FEEDBACK',
}

/**
 * 广告任务标签枚举枚举
 */
export enum TASK_LABEL_ENUM {
  /**
   * 应充预警
   */
  应充预警 = 'BALANCE_WARNING',
  /**
   * 停投召回
   */
  停投召回 = 'STOP_EXPOSURE_RECALL',
  /**
   * 新签上线
   */
  新签上线 = 'NEW_SIGN',
  /**
   * 无余额续签
   */
  无余额应充预警 = 'NO_BALANCE_CONTINUE',

  广告上线预警 = 'LAUNCH_WARNING',
  /**
   * 首续任务
   */
  首续任务 = 'AD_CONTINUED_CHARGING',
  /**
   * 提投放时长
   */
  提投放时长 = 'AD_RAISE_DURATION',
  /**
   * 提出价
   */
  提出价 = 'AD_RAISE_BID_PRICE',
}

/**
 * 预警任务类型枚举
 */
export enum TASK_TYPE_ENUM {
  /**
   * 极值预警
   */
  EXTREMUM = 'EXTREMUM',
  /**
   * 负向反馈预警
   */
  NEGATIVE_FEEDBACK = 'NEGATIVE_FEEDBACK',
  /**
   * 响应时效
   */
  RESPONSE_TIMEOUT = 'RESPONSE_TIMEOUT',
  /**
   * 门店留存未达标商户
   */
  CONSUME_RETENTION_WARING = 'CONSUME_RETENTION_WARING',

  STORE_RISK = 'SHOP_RISK_LABEL',
}
export const TASK_TYPE_MAP = [
  // {
  //   title: '极值预警',
  //   value: TASK_TYPE_ENUM.EXTREMUM,
  // },
  // {
  //   title: '负向反馈预警',
  //   value: TASK_TYPE_ENUM.NEGATIVE_FEEDBACK,
  // },
  {
    title: '响应时效',
    value: TASK_TYPE_ENUM.RESPONSE_TIMEOUT,
  },
  {
    title: '门店留存未达标商户',
    value: TASK_TYPE_ENUM.CONSUME_RETENTION_WARING,
  },
  {
    title: '门店风控商户',
    value: TASK_TYPE_ENUM.STORE_RISK,
  },
];
/**
 * 商户意向筛选
 */
export const MERCHANT_INTENTION_OPTIONS = [
  {
    title: '意向度高',
    value: MERCHANT_INTENTION_ENUM.INTENTIONHIGH,
  },
  {
    title: '意向度低',
    value: MERCHANT_INTENTION_ENUM.INTENTIONLOW,
  },
  {
    title: '明确拒绝',
    value: MERCHANT_INTENTION_ENUM.INTENTIONREFUSE,
  },
  {
    title: '商户未反馈',
    value: MERCHANT_INTENTION_ENUM.INTENTIONNOFEEDBACK,
  },
];

/**
 * 广告任务筛选
 */
export const TASK_LABEL_OPTIONS = [
  {
    title: '应充预警',
    value: TASK_LABEL_ENUM.应充预警,
  },
  {
    title: '停投召回',
    value: TASK_LABEL_ENUM.停投召回,
  },
  {
    title: '新签上线',
    value: TASK_LABEL_ENUM.新签上线,
  },
  {
    title: '无余额应充预警',
    value: TASK_LABEL_ENUM.无余额应充预警,
  },
  {
    title: '广告上线预警',
    value: TASK_LABEL_ENUM.广告上线预警,
  },
  {
    title: '首续任务',
    value: TASK_LABEL_ENUM.首续任务,
  },
  {
    title: '提投放时长',
    value: TASK_LABEL_ENUM.提投放时长,
  },
  {
    title: '提出价',
    value: TASK_LABEL_ENUM.提出价,
  },
];

/**
 * 素材场景枚举
 */
export const MATERIAL_SCENE_OPTIONS = [
  {
    label: '喜报',
    scene: 'BUSINESS_NEWS',
    content:
      '恭喜，经过我们一段时间的运营，各项数据都有了新突破，这是我们这一周的经营数据，您看什么时候方便，约您电话聊一下，看看怎么再继续帮助门店提升收入， 方便的话我打给您也行~',
  },
  {
    label: '余额预警',
    scene: 'BALANCE_WARN',
    content:
      '当前您的广告余额预计只能消耗10天，为了不影响您的门店收入，尽快充值吧，我们一起继续加油',
  },
  {
    label: '停投召回',
    scene: 'STOP_DROPPING',
    content: '最近我们平台流量又迎来一波高峰，可千万别错过了，有时间的话我再约您时间电话详细介绍下',
  },
  {
    label: '自定义',
    scene: 'REPLAY_CUSTOM',
  },
  {
    label: '广告投放',
    scene: 'AD_PLAN_CONFIG',
    hideInPicker: true,
    content: '我们结合您周边同行业商户的广告投放情况，为您定制专属投放方案，点击下图查看',
  },
];

export const ErrorMsgMap = {
  REACH_LIMIT: '商户复盘达到上限',
  MERCHANT_NEWS_DATA_EMPTY: '商户喜报数据为空',
  MERCHANT_HAVE_NOT_GROUP: '商户没有群',
  BD_NOT_IN_GROUP: 'BD未在群',
  BOT_NOT_AVAILABLE: '机器人不可用',
};

/**
 * 页面来源枚举
 */
export const FROM_PAGE = {
  /**
   * 商户列表页面
   */
  MERCHANT_LIST: 'MERCHANT_LIST',
  /**
   * 喜报页面
   */
  MERCHANT_NEWS: 'MERCHANT_NEWS',
};
/*
 * 门店标签映射
 */
export enum SHOP_LABEL_MAP {
  trade_high_value = '交易高价值',
}
/**
 * 门店标签筛选
 */
export const SHOP_LABELS = [
  {
    title: SHOP_LABEL_MAP.trade_high_value,
    value: 'trade_high_value',
  },
];

/**
 * 商家意向度来源枚举
 */
export enum MERCHANT_INTENTION_SOURCE_ENUM {
  BD = '拜访小记',
  AI = 'ai外呼',
}

/**
 * 策略标签枚举
 */
export const SUGGESTION_LABELS_ENUM = {
  QUALITY_SCORE: 'QUALITY_SCORE',
  BUDGET: 'BUDGET',
  PRICE: 'PRICE',
};

/**
 * 策略标签名称枚举
 */
export const SUGGESTION_LABELS_NAME_ENUM = {
  QUALITY_SCORE: '质量分调优',
  BUDGET: '提预算',
  PRICE: '提出价',
};

/**
 * 任务详情来源类型枚举，作为任务详情接口入参
 */
export const TASK_DETAIL_JUMP_SOURCE = {
  SHOP_LIST: 'SHOP_LIST', // 门店列表
  MERCHANT_LIST: 'MERCHANT_LIST', // 商户列表
};

/**
 * 任务详情来源类型枚举，前端区分场景用
 */
export const TASK_DETAIL_JUMP_FROM = {
  SHOP_LIST: 'SHOP_LIST', // 门店列表
  MERCHANT_LIST: 'MERCHANT_LIST', // 商户列表
  SERVICE_DETAIL_ANNUAL_FEE: 'SERVICE_DETAIL_ANNUAL_FEE', // 运维服务详情年费续约
  SERVICE_DETAIL_SHOP_INFRASTRUCT: 'SERVICE_DETAIL_SHOP_INFRASTRUCT', //  运维服务详情门店基建
};

export const TASK_DETAIL_TABS = {
  SHOP_INFRASTRUCT_TASK: 'SHOP_INFRASTRUCT_TASK', // 基建
  AD_TASK: 'AD_TASK', // 广告任务
  SHOP_SHANG_HU_TONG_TASK: 'SHOP_SHANG_HU_TONG_TASK', // 续签
};

/**
 * 任务详情tab名称
 */
export const TASK_DETAIL_TABS_NAME = {
  [TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK]: '基建任务',
  [TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK]: '续签任务',
  [TASK_DETAIL_TABS.AD_TASK]: '广告任务',
};

/**
 * 外呼详情页左侧列表tab枚举
 */
export enum OUTBOUND_LIST_PANEL_TAB_ENUM {
  MERCHANT_INFORMATION = 'MERCHANT_INFORMATION',
  VISIT_PLAN = 'VISIT_PLAN',
}

/**
 * 外呼详情页左侧列表tab名称
 */
export const OUTBOUND_LIST_PANEL_TAB_NAME = {
  MERCHANT_INFORMATION: '商户信息',
  VISIT_PLAN: '拜访计划',
};

/**
 * 外呼详情页右侧详情tab枚举
 */
export enum OUTBOUND_DETAIL_PANEL_TAB_ENUM {
  MERCHANT_DETAIL = 'MERCHANT_DETAIL',
  SHOP_DETAIL = 'SHOP_DETAIL',
  VISIT_RECORD = 'VISIT_RECORD',
  REPLAY_DATA = 'REPLAY_DATA',
}

/**
 * 复盘数据tab枚举
 */
export enum REPLAY_DATA_TAB_ENUM {
  WEEKLY_REPORT = 'WEEKLY_REPORT',
  DUPLICATE_TEMPLATE = 'DUPLICATE_TEMPLATE',
}

/**
 * 外呼打电话场景码
 * 外呼详情页小黄条拦截场景码、记拜访bizScene电话场景C33_AGENT_OUT_CALL
 * 记拜访bizScene非电话场景C33_PC_DEFAULT
 */
export enum C33_CALL_ENUM {
  C33_AGENT_OUT_CALL = 'C33_AGENT_OUT_CALL',
  C33_PC_DEFAULT = 'C33_PC_DEFAULT',
}

/**
 * 外呼详情页查询拜访记录targetType
 */
export enum OUTBOUND_VISIT_RECORD_TARGET_TYPE {
  MERCHANT = 'MERCHANT',
  STORE = 'STORE',
  AMAP_SHOP = 'AMAP_SHOP',
}

/**
 * 外呼详情页PID维度场景码
 */
export const PID_VISIT = 'PID_VISIT';

/**
 * 外呼详情页门店维度场景码
 */
export const TEL_VISIT = 'TEL_VISIT';

/**
 * 运维服务详情服务任务状态枚举
 */
export const SERVICE_STATUS_ENUM = {
  COMPLETE: 'COMPLETE',
  UNCOMPLETED: 'UNCOMPLETED',
};

/**
 * 运维服务详情具体任务状态枚举
 */
export const OPERATING_TASK_DETAIL_STATUS_ENUM = {
  FINISH: 'FINISH',
  WORKING: 'WORKING',
};

/**
 * 商户维度任务类型
 */
export enum MERCHANT_TASK_TYPE {
  BUILD_RELATIONSHIP = 'BUILD_RELATIONSHIP', // 建联
  DEMAND_FOCUS = 'DEMAND_FOCUS', // 需求聚焦
  COMPLIANCE_TRAINING = 'COMPLIANCE_TRAINING', // 日常培训
  AD_TOU_FANG_CONFIRM = 'AD_TOU_FANG_CONFIRM', // 广告投放确认
  NEW_SIGN_FU_PAN = 'NEW_SIGN_FU_PAN', // 新签复盘
  STOP_DROPPING = 'STOP_DROPPING', // 停投召回沟通
  STORE_INFRASTRUCTURE = 'STORE_INFRASTRUCTURE', // 门店基础设施
  FU_PAN = 'FU_PAN', // 复盘
  AD_EXTENSION = 'AD_EXTENSION', // 广告续充
  NIAN_FEI__EXTENSION = 'NIAN_FEI__EXTENSION', // 年费续约
  USP_OPTIMIZE = 'USP_OPTIMIZE', // usp优化
}

/**
 * 运维服务详情任务跳转类型
 */
export enum OPERATING_TASK_JUMP_TYPE {
  GO_FINISH = 'GO_FINISH', // 去完成
  GO_VISIT = 'GO_VISIT', // 记拜访
  CALL_PHONE = 'CALL_PHONE', // 电话
  XI_BAO = 'XI_BAO', // 喜报
  FU_PAN = 'FU_PAN', // 复盘
}

/**
 * 门店列表调用来源场景
 */
export enum SHOP_QUERY_SOURCE_ENUM {
  /**
   * 首页门店列表
   */
  BASIC_SHOP_LIST = 'BASIC_SHOP_LIST',
  /**
   * 任务详情门店选择组件
   */
  TASK_DETAIL_SHOP_LIST = 'TASK_DETAIL_SHOP_LIST',
  /**
   * 外呼抽屉
   */
  WAI_HU_SHOP_LIST = 'WAI_HU_SHOP_LIST',
}

/**
 * 商户列表按钮code
 */
export enum MERCHANT_BUTTON_CODE_ENUM {
  /**
   * 查看门店
   */
  VIEW_STORES = 'VIEW_STORES',
  /**
   * 去完成
   */
  TO_COMPLETE = 'TO_COMPLETE',
  /**
   * 生成喜报
   */
  GENERATE_NEWS = 'GENERATE_NEWS',
  /**
   * 记拜访
   */
  RECORD_VISITS = 'RECORD_VISITS',
  /**
   * 电话沟通
   */
  TELEPHONE_COMMUNICATION = 'TELEPHONE_COMMUNICATION',
  /**
   * 预警处理
   */
  WARNING_HANDLE = 'WARNING_HANDLE',
  /**
   * 运维服务
   */
  OPERATE_SERVICES = 'OPERATE_SERVICES',
  /**
   * 推广通
   */
  RECHARGE_EXTENSION = 'RECHARGE_EXTENSION',
  /**
   * 一键建群
   */
  CREATE_OPT_GROUP = 'CREATE_OPT_GROUP',
}

/**
 * 精细化复盘applicationCode入参区分
 */
export enum APPLICATION_CODE_ENUM {
  bd_review_board_pid = 'bd_review_board_pid', // 商家维度
  bd_review_board_shop_id = 'bd_review_board_shop_id', // 门店维度和excel下载
  美食喜报 = 'xibao_food_shop_detail_download_application',
  非美食喜报 = 'xibao_other_shop_detail_download_application',
}

/**
 * 任务详情tab名称
 */
export const TIMED_TASK_TITLE_NAME = {
  REPLAY_MESSAGE: '复盘消息',
  NOTIFY_AD_RE_PUSH_MESSAGE: '通知广告复投消息',
  RENEW_REMIND_MESSAGE: '续充提醒消息',
};

/**
 * 商户推荐分析筛选
 */
export const MERCHANT_POTENTIAL_OPTIONS = [
  {
    title: '潜力广告主',
    value: '1',
  },
];

/**
 * 门店标签列表
 */
export const SHOP_LABELS_OPTIONS = [
  {
    code: 'has_shop_complete',
    desc: '基建达标',
  },
  {
    code: 'has_ad_serving_30d',
    desc: '广告在投',
  },
  {
    code: 'has_online_wpT5',
    desc: '商户通',
  },
  {
    code: 'has_wang_pu',
    desc: '旺铺',
  },
  {
    code: 'alipay_touch',
    desc: '支付宝碰一下',
  },
];
