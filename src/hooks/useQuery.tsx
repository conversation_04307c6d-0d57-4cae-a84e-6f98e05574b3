import { useSearchParams } from 'ice';

export function useQuery(): [
  Record<string, any>,
  (newQuery: Record<string, any>) => void,
  () => void,
] {
  const [query, setQueryData] = useSearchParams();
  const queryData = Object.fromEntries(query.entries());
  const setQuery = (newQuery: Record<string, string>) => {
    const newData = { ...queryData, ...newQuery };
    setQueryData(newData);
  };
  const clearQuery = () => {
    setQueryData({});
  };
  return [queryData, setQuery, clearQuery];
}
