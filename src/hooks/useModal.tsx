import { ModalProps } from 'antd';
import { useState } from 'react';

interface IProps<IData = Record<string, any>> {
  data?: IData;
  onOk?: () => any | Promise<any>;
  onCancel?: () => any | Promise<any>;
}
export default function useModal<IData = Record<string, any>, U = unknown>(
  // @ts-ignore
  props: IProps<IData> & U & ModalProps = {},
) {
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<IData>(props.data || ({} as unknown as IData));
  const [confirmLoading, setConfirmLoading] = useState(false);
  const onCancel = async () => {
    await props?.onCancel?.();
    setOpen(false);
    setData({} as unknown as IData);
  };
  const onOk = async () => {
    try {
      setConfirmLoading(true);
      await props.onOk?.();
      setConfirmLoading(false);
      onCancel();
    } catch {
      setConfirmLoading(false);
    }
  };
  const openModal = () => {
    setOpen(true);
  };
  const updateAndOpen = (_data: IData) => {
    setData(_data);
    openModal();
  };
  return {
    modalProps: {
      ...props,
      open,
      onCancel,
      onOk,
      data,
      confirmLoading,
      onClose: onCancel,
    } satisfies IProps & ModalProps,
    openModal,
    closeModal: onCancel,
    updateModalData: setData,
    updateAndOpen,
  };
}
export type IModalProps<T = unknown, U = unknown> = ModalProps & IProps<T> & U;
