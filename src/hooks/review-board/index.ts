import { useRequest } from 'ahooks';
import { message } from 'antd';
import service from '@/_docplus/target/service/amap-sales-data-client/BdLastSelectFieldsManageFacade';
import services from '@/_docplus/target/service/amap-sales-data-client/DataQueryGwFacade';
import { downloadUtil } from '@/common/utils';
import dayjs from 'dayjs';

// 处理列表和指标接口入参
export const handlerParams = (param: {
  formData: any;
  applicationCode: string;
  page?: { current: number; pageSize: number };
  extInfo?: { changeToExcelUrlReturn: boolean; fields: string[] };
}) => {
  const { formData, applicationCode, page, extInfo, requestParams } = param || {};
  const { pid, shopIds, dateRange = [] } = formData || {};
  const { current, pageSize } = page || {};
  const { changeToExcelUrlReturn, fields } = extInfo || {};
  const _page = current ? { pageNo: current, pageSize } : {};
  // 下载接口额外参数
  const _extInfo = changeToExcelUrlReturn
    ? { extInfo: { changeToExcelUrlReturn, fields: fields?.join(',') }, resultType: 'LIST' }
    : {};
  const shopIdsString = shopIds?.map((id) => `'${id}'`).join(',');
  const params = {
    requestParams: {
      pid,
      startDate: dayjs(dateRange[0]).format('YYYYMMDD'),
      endDate: dayjs(dateRange[1]).format('YYYYMMDD'),
      shopIds: `(${shopIdsString})`,
    },
    applicationCode,
    ..._page,
    ..._extInfo,
  };
  return requestParams?.pid ? param : params;
};

export const fetchQueryData = async (params) => {

  const resultParams = handlerParams(params);
  try {
    // const par = {
    // "bizContextInfos": {
    //   "": ""
    // },
    // "pageNo": 0,
    // "requestId": "",
    // "requestParams": {
    //   "endDate": "20240908",
    //   "startDate": "20240801",
    //   "pid": "2088222547363415",
    //   "shopIds": "('2024081311077000000109984062')"
    // },
    // "pageSize": 20,
    // "requestSource": "",
    // "requestChannel": "",
    // "class": "com.amap.sales.data.client.dto.domainmodel.request.DataQueryRequest",
    // "applicationCode": "bd_review_board_pid",
    // "resultType": "",
    // "extInfo": {
    //   "": ""
    // }
  // }
    const res = await services.queryData(resultParams);
    if (!res.success) {
      message.error(
        res?.resultCode === 'YESTERDAY_DATA_NOT_READY'
          ? '昨日数据产出中，请选择其他时期或稍后再试'
          : res?.resultMessage || '系统异常，请稍后再试～',
      );
    }
    return res?.data || {};
  } catch (err) {
    message.error(err?.result?.resultMsg || '系统异常，请稍后再试～');
    return {};
  }
};

export const useQueryData = () => {
  const {
    run: loadQueryData,
    loading,
    data,
  } = useRequest(
    async (params) => {
      const res = await fetchQueryData(params);
      return res;
    },
    {
      manual: true,
    },
  );

  return {
    loading,
    data: data?.applicationDataList?.[0]?.ruleDataList?.[0]?.values?.[0] || {},
    lastSelectfields: data?.extInfo?.fields?.split(','),
    loadQueryData,
  };
};

export const useSaveUncheckFields = () => {
  const { run: loadSaveUncheckFields } = useRequest(
    async (params) => {
      const { pid, fields } = params;
      const res = await service.createOrUpdate({ pid, fields: fields?.join(',') });
      if (!res.success) {
        message.error(res?.resultMessage || '系统异常，请稍后再试～');
      }
      return {};
    },
    {
      manual: true,
    },
  );

  return {
    loadSaveUncheckFields,
  };
};

export const useDownload = () => {
  const { run: loadDownload, loading: downloadLoading } = useRequest(
    async (params) => {
      const res = await fetchQueryData(params);
      return res;
    },
    {
      manual: true,
      onSuccess: (res: any) => {
        if (res) {
          const downLoadExcelUrl =
            res?.applicationDataList?.[0]?.ruleDataList?.[0]?.extInfo?.downLoadExcelUrl;
          if (downLoadExcelUrl) {
            downloadUtil(downLoadExcelUrl);
          }
        }
      },
    },
  );

  return {
    downloadLoading,
    loadDownload,
  };
};
