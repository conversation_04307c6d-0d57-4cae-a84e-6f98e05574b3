import React, { PropsWithChildren, useState } from 'react';
import { ActionButtonType } from '@/constants';
import { IAction } from '@/types';
import { useActions } from './action-controller';
import { Skeleton } from 'antd';

interface IGlobalStore {
  viewer: string;
  setViewer: React.Dispatch<React.SetStateAction<string>>;
  actionMap: Map<ActionButtonType, IAction>;
  updateActions: (ope?: string) => void;
  isSwitchViewerShow: boolean;
}
const GlobalContext = React.createContext<IGlobalStore>({} as any);
export const GlobalProvider = (props: PropsWithChildren<any>) => {
  const [viewer, setViewer] = useState();
  const { actionMap, updateActions } = useActions();
  if (!actionMap) {
    return <Skeleton />;
  }
  return (
    <GlobalContext.Provider
      value={{
        viewer,
        setViewer,
        isSwitchViewerShow: actionMap
          ? !!actionMap.get(ActionButtonType.切换视角)?.showButton
          : false,
        actionMap,
        updateActions,
      }}
    >
      {props.children}
    </GlobalContext.Provider>
  );
};

export const useStore = () => React.useContext<IGlobalStore>(GlobalContext);
