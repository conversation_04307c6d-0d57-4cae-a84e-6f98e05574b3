import { useRequest } from 'ahooks';
import React from 'react';
import { getActionList } from '@/services';
import { IAction } from '@/types';
import { ActionButtonType, ActionScene } from '@/constants';
import { isMpa } from '@/utils';

export const ActionContext = React.createContext<{
  actionMap: Map<ActionButtonType, IAction>;
}>({} as any);

const emptyMap = new Map<ActionButtonType, IAction>();

export function useActions() {
  const {
    data: actionMap,
    run: updateActions,
    mutate,
  } = useRequest(
    async (viewOperatorId?: string) => {
      if (isMpa()) return emptyMap;
      const map = new Map<ActionButtonType, IAction>();
      const scenes = [ActionScene?.通用区块, ActionScene.切换视角, ActionScene.发送企微触达];
      const res = await getActionList({
        viewOperatorId: viewOperatorId || undefined,
        sceneList: scenes,
      });

      // 处理新的数据结构
      const viewConfig = res.view || {};
      Object.keys(viewConfig).forEach((scene) => {
        const list = viewConfig[scene] || [];
        list.forEach((item: any) => {
          map.set(item.type, {
            buttonType: item.type,
            showButton: item.show,
            greyButton: item.grey === 'true',
            buttonText: item.desc || '',
            jumpUrl: item.jumpUrl || '',
            client: item.client || '',
            jumpType: item.jumpType || '',
            jumpTypeNew: item.jumpTypeNew || '',
            jumpUrlList: item.jumpUrlList || '',
          });
        });
      });
      Object.keys(ActionButtonType).forEach((key) => {
        const k = ActionButtonType[key];
        if (!map.get(k)) {
          map.set(k, {
            buttonType: k as ActionButtonType,
            showButton: true,
          } as any);
        }
      });
      return map;
    },
    {
      onError() {
        mutate(emptyMap);
      },
    },
  );
  return {
    actionMap,
    updateActions,
  };
}
