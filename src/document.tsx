import { Meta, Title, <PERSON>s, <PERSON>, Scripts } from '@ice/runtime';

export default function Document() {
  return (
    <html>
      <head>
        <title>代运营平台</title>
        <meta charSet="utf-8" />
        <meta name="description" content="ice.js 3 lite scaffold" />
        <link
          rel="icon"
          href="https://img.alicdn.com/imgextra/i1/O1CN01jUhdM91rLGUSdPfJf_!!6000000005614-2-tps-58-58.png"
        />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
        <script
          type="text/javascript"
          id="beacon-aplus"
          src="//g.alicdn.com/alilog/mlog/aplus_v2.js"
          exparams="userid=&aplus&sidx=aplusSidex&ckx=aplusCkx"
          crossOrigin="anonymous"
          defer
        />
        <Meta />
        <Title />
        <Links />
      </head>
      <body>
        <Main />
        <Scripts />
      </body>
    </html>
  );
}
