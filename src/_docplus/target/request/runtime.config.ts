/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/muq8uc#j67QE
 */
const config = {
  'app': [
    'alsc-kbt-intergration-toolkit',
    'amap-sales-operation',
    'amap-sales-data'
  ],
  'debug': false,
  'mode': {
    'alsc-kbt-intergration-toolkit': {
      'alsc-kbt-intergration-toolkit-client': {
        'branch': 'releases/20240129110949781_r_release_209672_alsc-kbt-intergration-toolkit-code'
      }
    },
    'amap-sales-operation': {
      'amap-sales-operation-client': {
        'branch': 'master'
      }
    },
    'amap-sales-data': {
      'amap-sales-data-client': {
        'branch': 'releases/20240129110949781_r_release_209672_alsc-kbt-intergration-toolkit-code'
      }
    }
  },
  'output': {
    'apiFilter': {}
  }
}
export default config;
