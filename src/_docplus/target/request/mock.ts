/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/muq8uc
 */
import { WebclientInvokeRequest } from '@ali/alsc-gateway-web-client/lib/types/request';



export function mockableWrapper<F extends (...args: any[]) => any>(func: F): F {
  return <F>function (...args: Parameters<F>) {
    // 请求参数
    const req: Partial<WebclientInvokeRequest> = arguments?.[0];
    const apiKeyInfo = req?.apiKey.split(':');
    const realReq = {
      ...req,
      apiKey: apiKeyInfo[0],
    };
    const [, ...restArgs] = arguments;

    
    return func.call(window, realReq, ...restArgs);
  }
}
