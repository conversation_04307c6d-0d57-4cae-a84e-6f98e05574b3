/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 */
import originFetch, { GwResult } from '@ali/kb-fetch';
import { mockableWrapper } from './mock';

/**
 * Same as Partial<T> but goes deeper and makes Partial<T> all its properties and sub-properties.
 */
export type DeepPartial<T> = {
  [P in keyof T]?:
    T[P] extends Array<infer U> ? Array<DeepPartial<U>> :
    T[P] extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> :
    DeepPartial<T[P]> | T[P]
};

export type GatewayResult<T> = GwResult<T>;

const mockbleFetch = mockableWrapper(originFetch);

export type { GwRequestOption } from '@ali/kb-fetch';

export default {
  fetch: mockbleFetch,
}
