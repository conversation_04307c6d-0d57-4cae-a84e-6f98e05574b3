/* eslint-disable */
// Generated using typescript-generator version 2.32.889 on 2024-03-22 10:15:17.

export interface DownloadQRCodeReq {
  qrCodeUrls: string[];
  type: string;
  shopIds: string[];
}

/**
 * ResultDO
 *
 * 备注： 1、可以直接用，也可以继承后再加工； 2、如果需要static final（缓存起来）的ResultDO，请用 {@link ImmutableResult} 3、【谨慎】【谨慎】【谨慎】改动；
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 * @deprecated 使用 tribe包中的
 */
export interface KbResult<T> extends Serializable, IResult<any> {
  success: boolean;
  msg: string;
  /**
   * traceId
   */
  traceId: string;
}

export interface BindCodeReq {
  bindTargetId: string;
  bizType: string;
  bindInfos: BindInfo[];
}

export interface BindResVO extends Serializable {
  loginId: string;
  date: string;
  failedList: { [index: string]: string }[];
}

export interface QueryShopListByPoiReq {
  latitude: string;
  longitude: string;
  shopName: string;
  pageSize: number;
  pageNum: number;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/28
 * @deprecated 使用 tribe包中的
 */
export interface KbPageResult<T> extends Serializable {
  /**
   * 调用是否成功
   */
  succ: boolean;
  /**
   * 结果编码，通常错误时返回
   */
  code: string;
  /**
   * 结果信息，通常错误时返回
   */
  msg: string;
  /**
   * traceId
   */
  traceId: string;
  /**
   * 实际数据
   */
  dataList: T[];
  /**
   * 数据总数目
   */
  totalCount: number;
  /**
   * 每页显示的数据数目
   */
  pageSize: number;
  /**
   * 当前页号
   */
  pageNum: number;
  /**
   * 获取总页数
   * @return
   */
  totalPageNum: number;
  /**
   * 获取偏移量
   * @return
   */
  offset: number;
}

export interface KBShopSearchVO extends Serializable {
  /**
   * 门店id
   */
  shopId: string;
  /**
   * 门店名称
   */
  shopName: string;
  /**
   * 商户pid
   */
  merchantPid: string;
  /**
   * 商户名称
   */
  merchantName: string;
  /**
   * 门店状态
   */
  status: string;
  /**
   * 地址
   */
  address: string;
  /**
   * 经度
   */
  longitude: string;
  /**
   * 维度
   */
  latitude: string;
  /**
   * 距离
   */
  distance: string;
  /**
   * 门店一级类目
   */
  rootCategoryId: string;
  retail: boolean;
}

export interface QueryKbCodeReq {
  /**
   * 码生成批次号
   */
  batchId: number;
  /**
   * 口碑码绑定主体ID
   */
  targetId: string;
  /**
   * 口碑码模板昵称
   */
  templateNickName: string;
  /**
   * 口碑码绑定主体类型（@link KbCodeTypeEnum）
   */
  codeType: string;
  /**
   * 数据来源来源哪个应用
   */
  appSource: string;
  /**
   * 子类型
   */
  subTargetId: string;
  extInfo: { [index: string]: string };
  refresh: boolean;
}

export interface ShopKbCodeDto {
  /**
   * 生成码的批次号
   */
  batchId: number;
  /**
   * 口碑码值
   */
  qrCode: string;
  /**
   * 口碑码绑定主体ID
   */
  targetId: string;
  /**
   * 绑定子主体
   */
  subTargetId: string;
  /**
   * 码类型
   */
  codeType: string;
  /**
   * 纯码url
   */
  coreUrl: string;
  /**
   * 口碑码URL
   */
  codeUrl: string;
  /**
   * 口碑码生成状态
   */
  status: string;
}

export interface VerifyCodeReq {
  shopId: string;
  codeToken: string;
  qrcodeScene: string;
  bizType: string;
}

export interface VerifyCodeVO {
  limit: number;
  bindInfo: BindInfo;
  emptyCode: boolean;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/02
 */
export interface AlarmOperateRequest {
  /**
   * 来源
   */
  source: string;
  /**
   * 预警id
   */
  id: number;
  /**
   * 类型
   */
  type: string;
  /**
   * '门店id'
   */
  shopId: string;
  /**
   * '归属BD编号'
   */
  employeeNo: number;
  /**
   * '合约到期日期'
   */
  orderDueDate: string;
  /**
   * '旺铺订单刊例金额（最新合约）'
   */
  totalFee: number;
  /**
   * '旺铺订单实付金额（最新合约）'
   */
  payFee: number;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 */
export interface AlarmCategoryCountResponse extends Serializable {
  /**
   * 一级分类
   */
  parentCategoryId: number;
  /**
   * 二级分类数量
   */
  secondaryCategoryCountList: AlarmSecondaryCategoryCountResponse[];
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 */
export interface AlarmTotalCountResponse extends Serializable {
  /**
   * 用户编号
   */
  userId: number;
  /**
   * 总数
   */
  totalCount: number;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 */
export interface AlarmDetailQueryRequest extends BasePageQuery {
  /**
   * 用户id
   */
  userId: number;
  /**
   * 二级分类id
   */
  secondaryCategoryId: number;
  /**
   * 纬度
   */
  latitude: number;
  /**
   * 经度
   */
  longitude: number;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 */
export interface AlarmDetailResponse extends Serializable {
  /**
   * 预警id
   */
  id: number;
  /**
   * 一级分类
   */
  parentCategoryId: number;
  /**
   * 二级分类
   */
  secondaryCategoryId: number;
  /**
   * 用户id
   */
  userId: number;
  /**
   * 用户名称
   */
  userName: string;
  /**
   * 预警内容
   */
  reMark: string;
  /**
   * 预警主体
   */
  targetId: string;
  /**
   * 预警主体
   */
  targetIdStr: string;
  /**
   * 预警类型
   */
  targetType: number;
  /**
   * 门店信息
   */
  shopInfo: ShopInfoResponse;
}

/**
 * <AUTHOR>
 * @version $Id: OperateRequest.java, v 0.1 16/4/20 上午11:20 yxl Exp $
 */
export interface OperateRequest<T> extends Request<T> {
  /**
   * Getter method for property <tt>operatorInfo</tt>.
   * @return property value of operatorInfo
   */
  operatorInfo: OperatorInfo;
  /**
   * Getter method for property <tt>requestId</tt>.
   * @return property value of requestId
   */
  requestId: string;
  /**
   * Getter method for property <tt>channel</tt>.
   * @return property value of channel
   */
  channel: string;
}

/**
 * 物料模版
 * <AUTHOR>
 * @version $Id : StuffTemplateDto.java, v 0.1 2016-04-28 下午12:46 song.hus Exp $
 */
export interface StuffTemplate extends DataModel {
  /**
   * 物料模板ID
   */
  id: number;
  /**
   * 模版名称
   */
  name: string;
  /**
   * 模版说明
   */
  memo: string;
  /**
   * 扩展信息
   */
  ext: { [index: string]: string };
  /**
   * 物料属性
   */
  stuffType: StuffTypeEnum;
  /**
   * 物料类型
   */
  stuffAttrId: string;
  /**
   * 物料类型名称
   */
  stuffAttrName: string;
  /**
   * 物料尺寸
   * 物料模板改造，尺寸变成了手动输入文本，因此没有了尺寸编号的概念，为了兼容以前老的代码逻辑，默认使用“0”代替
   */
  size: string;
  /**
   * 物料尺寸名称
   */
  sizeName: string;
  /**
   * 物料图片信息
   */
  fileIds: number[];
  /**
   * 领域归属
   * {@link DomainEnum}，当前默认是{@code KOUBEI}
   */
  domain: string;
  /**
   * 业务来源
   * {@link BizSourceEnum}, 当前默认是{@code KOUBEI_STUFF}
   */
  bizSource: string;
  /**
   * 别名
   */
  nickName: string;
  /**
   * 行业类型
   */
  bizType: string;
  /**
   * 设备类别
   */
  deviceCategory: string;
  /**
   * 是否包含码(Y/N)
   */
  hasQrcode: string;
  /**
   * 整包数量
   */
  packageQuantityTips: string;
  /**
   * 物料SKU
   */
  tmallSku: string;
  /**
   * 码池
   * {@link QrcodePoolEnum}
   */
  qrcodePool: string;
  /**
   * 物料材质描述
   */
  stuffMaterialDesc: string;
  /**
   * 活动名称(物料小计名称)
   */
  activeName: string;
  /**
   * 审核类型
   * {@link CheckTypeEnum}
   */
  checkType: string;
  /**
   * 物料小计有效期开始时间，该字段已经无用
   */
  startTime: Date;
  /**
   * 物料小计有效期结束时间，该字段已经无用
   */
  endTime: Date;
  /**
   * 物料图片上传有效期开始
   */
  gmtStuffStart: Date;
  /**
   * 物料图片上传有效期结束
   */
  gmtStuffEnd: Date;
  /**
   * 提交验收图片数量条件
   * {@link TemplateExpressionEnum}
   */
  expression: string;
  /**
   * 提交验收图片数量
   */
  picNum: string;
  /**
   * 活动审批号
   */
  activeProcessNo: string;
  /**
   * 物料应用场景
   * {@link StuffSceneEnum}
   */
  stuffScenes: string[];
  /**
   * ISV类型
   * {@link IsvTypeEnum}
   */
  isvType: string;
  /**
   * 物料单位
   */
  unitDesc: string;
  /**
   * 码类型
   * {@link BindTypeEnum}
   */
  qrCodeType: string;
  /**
   * 码物料类型
   * {@link QrCodeStuffTypeEnum}
   */
  qrCodeStuffType: string;
  /**
   * 适用行业
   */
  stuffIndustry: string;
  /**
   * 示例文案
   */
  imgTips: string;
  /**
   * 码路由的场景值
   * {@link KbTrafficBizSceneEnum}
   */
  trafficBizScene: string;
  /**
   * 空码库存阈值
   */
  qrCodeStockThreshold: number;
  /**
   * 示意图
   */
  qrCodeImgField: string;
  /**
   * 业务归属事业部属性（物料铺设场景）
   * {@link BizDepAttrEnum}
   */
  bizDeptAttrs: string[];
  /**
   * 活动物料分组（物料铺设场景）
   * {@link StuffTypeEnum}
   */
  group: string;
  /**
   * 铺设建议描述（物料铺设场景）
   */
  paveAdviseText: string;
  /**
   * 铺设建议示意图（物料铺设场景）
   */
  paveAdviseField: string;
  /**
   * 铺设建议示意图（物料铺设场景）
   */
  paveAdviseFieldDto: FieldDTO;
  /**
   * 机审算法图（物料铺设场景）
   */
  machineAuditSampleImgFields: string[];
  /**
   * 机审算法图（物料铺设场景）
   */
  machineAuditSampleImgFieldDtos: FieldDTO[];
  /**
   * 预计铺设门店数（物料铺设场景）
   */
  predictPaveShopNum: string;
  fileDtos: FieldDTO[];
}

export interface StuffCopyTemplate extends StuffTemplate {
  oriId: string;
}

/**
 * 通用的请求模型
 * <AUTHOR>
 * @version $Id: Request.java, v 0.1  16/4/14 下午1:56 yxl Exp $
 */
export interface Request<T> extends ToString, Serializable {
  /**
   * Getter method for property <tt>data</tt>.
   * @return property value of data
   */
  data: T;
  /**
   * Getter method for property extInfo.
   * @return property value of extInfo
   */
  extInfo: { [index: string]: any };
}

/**
 * 物料模版查询
 * <AUTHOR>
 * @version $Id: StuffTemplateQueryCondition.java, v 0.1 2016-04-28 下午1:03 song.hus Exp $
 */
export interface StuffTemplateCondition extends ToString {
  /**
   * 业务来源， 默认来自口碑物料
   * {@link BizSourceEnum}
   */
  bizSource: string;
  /**
   * 模板状态
   * {@link StuffTemplateStatusEnum}
   */
  status: string;
  /**
   * 模板适用场景
   * {@link StuffSceneEnum}
   */
  stuffTemplateScene: string;
  /**
   * 是否过滤铺设时间失效的物料（仅针对物料铺设场景的有效）
   */
  filterPaveExpireTime: boolean;
}

/**
 * <AUTHOR>
 * @version $Id: OperatorInfo.java, v 0.1 2016-04-20 07:12 zhengyuan.zy Exp $$
 * @deprecated 使用 tribe包中的
 */
export interface OperatorInfo extends ToString {
  /**
   * Getter method for property operatorId.
   * @return property value of operatorId
   */
  operatorId: string;
  /**
   * Getter method for property operatorType.
   * @return property value of operatorType
   */
  operatorType: string;
  /**
   * Getter method for property operatorName.
   * @return property value of operatorName
   */
  operatorName: string;
  /**
   * Getter method for property operatorRole.
   * @return property value of operatorRole
   */
  operatorRole: string;
  /**
   * 操作人花名
   */
  operatorNickName: string;
  /**
   * 操作人域帐号
   */
  domainName: string;
}

/**
 * 通用的分页查询请求
 * <AUTHOR>
 * @version $Id: PageQueryRequest.java, v 0.1  16/4/14 下午2:04 yxl Exp $
 */
export interface PageQueryRequest<T> extends Request<T> {
  /**
   * 页数
   */
  pageNo: number;
  /**
   * 总行数
   */
  pageSize: number;
}

/**
 * 物料模版查询
 * <AUTHOR>
 * @version $Id: StuffTemplateQueryCondition.java, v 0.1 2016-04-28 下午1:03 song.hus Exp $
 */
export interface StuffTemplateQueryCondition extends ToString {
  /**
   * Getter method for property templateId.
   * @return property value of templateId
   */
  templateId: number;
  /**
   * Getter method for property name.
   * @return property value of name
   */
  name: string;
  /**
   * Getter method for property stuffType.
   * @return property value of stuffType
   */
  stuffType: StuffTypeEnum;
  /**
   * Getter method for property status.
   * @return property value of status
   */
  status: StuffTemplateStatusEnum;
  /**
   * Getter method for property size.
   * @return property value of size
   */
  size: string;
  /**
   * Getter method for property stuffAttrId.
   * @return property value of stuffAttrId
   */
  stuffAttrId: string;
  /**
   * Getter method for property creator.
   * @return property value of creator
   */
  creator: string;
  /**
   * Getter method for property startTime.
   * @return property value of startTime
   */
  startTime: Date;
  /**
   * Getter method for property endTime.
   * @return property value of endTime
   */
  endTime: Date;
  /**
   * Getter method for property domain.
   * @return property value of domain
   */
  domain: string;
  /**
   * Getter method for property <tt>bizSource</tt>.
   * @return property value of bizSource
   */
  bizSource: string;
  /**
   * Getter method for property <tt>nickName</tt>.
   * @return property value of nickName
   */
  nickName: string;
  /**
   * Getter method for property <tt>bizType</tt>.
   * @return property value of bizType
   */
  bizType: string;
  /**
   * Getter method for property <tt>stuffAttrName</tt>.
   * @return property value of stuffAttrName
   */
  stuffAttrName: string;
  /**
   * ids用作于生产单查询模板，只有ids里的模板有单价
   */
  ids: number[];
  /**
   * 设备类型
   * {@link StuffDeviceCategoryEnum}
   */
  deviceCategory: string;
  /**
   * 物料应用场景
   * {@link StuffSceneEnum}
   */
  stuffScenes: string[];
  /**
   * sku
   */
  sku: string;
}

/**
 * 多值分页类型查询返回结果，支持泛型
 *
 * 注：目前泛型只能在TR接口中使用，不能在WS接口使用
 * <AUTHOR>
 * @version $Id: PageQueryResult.java, v 0.1 16/4/14 下午2:28 yxl Exp $
 */
export interface PageQueryResult<T> extends KbResult<T> {
  totalSize: number;
}

/**
 * 物料模版
 * <AUTHOR>
 * @version $Id : StuffTemplateDto.java, v 0.1 2016-04-28 下午12:46 song.hus Exp $
 */
export interface StuffTemplateDto extends DataModel {
  /**
   * id
   */
  id: number;
  /**
   * 模版名称
   */
  name: string;
  /**
   * 模版说明
   */
  memo: string;
  /**
   * 扩展信息
   */
  ext: { [index: string]: string };
  /**
   * 物料属性
   */
  stuffType: StuffTypeEnum;
  /**
   * 物料类型
   * 此字段已作废
   * @deprecated
   */
  stuffAttrId: string;
  /**
   * 物料类型名称
   */
  stuffAttrName: string;
  /**
   * 物料类型别名
   */
  stuffAttrNickName: string;
  /**
   * 物料尺寸
   * 此字段已作废
   * @deprecated
   */
  size: string;
  /**
   * 物料尺寸名称
   */
  sizeName: string;
  /**
   * 状态
   */
  status: StuffTemplateStatusEnum;
  /**
   * 创建人
   */
  creator: string;
  /**
   * 创建人,真名(花名)
   */
  creatorName: string;
  /**
   * 活动审批号
   */
  activeProcessNo: string;
  /**
   * 活动名称
   */
  activeName: string;
  /**
   * 开始时间
   * @deprecated
   */
  startTime: Date;
  /**
   * 结束时间
   * @deprecated
   */
  endTime: Date;
  /**
   * 物料模板图片附件
   */
  resourceIds: string[];
  fileDtos: Attachment[];
  /**
   * 物料模板所属区域
   * {@link DomainEnum}
   */
  domain: string;
  /**
   * 业务来源
   * {@link BizSourceEnum}
   */
  bizSource: string;
  /**
   * 物料模板ID
   * 业务场景：物料再次申请时用于前端区分模板id
   */
  templateId: number;
  /**
   * 物料模板名称
   * 业务场景：物料再次申请时用于前端区分模板id
   */
  templateName: string;
  /**
   * 别名
   */
  nickName: string;
  /**
   * 行业名称
   */
  bizType: string;
  /**
   * 物料图片上传有效期开始
   */
  gmtStuffStart: Date;
  /**
   * 物料图片上传有效期结束
   */
  gmtStuffEnd: Date;
  /**
   * 物料上传图片匹配规则模式
   */
  expression: string;
  /**
   * 物料上传图片匹配规则数量
   */
  picNum: string;
  /**
   * 物料上传审核规则
   */
  checkType: string;
  /**
   * 设备类别
   * {@link StuffDeviceCategoryEnum}
   */
  deviceCategory: string;
  /**
   * 是否包含码
   */
  hasQrcode: string;
  /**
   * 整包数量提示
   */
  packageQuantityTips: string;
  /**
   * 天猫SKU
   */
  tmallSku: string;
  /**
   * 码池
   * {@link QrcodePoolEnum}
   */
  qrcodePool: string;
  /**
   * 物料材质描述
   */
  stuffMaterialDesc: string;
  /**
   * 物料应用场景
   * {@link StuffSceneEnum}
   */
  stuffScenes: string[];
  /**
   * ISV类型
   * {@link IsvTypeEnum}
   */
  isvType: string;
  /**
   * 物料单位
   */
  unitDesc: string;
  /**
   * 码类型
   * {@link BindTypeEnum}
   */
  qrCodeType: string;
  /**
   * 码物料类型
   * {@link QrCodeStuffTypeEnum}
   */
  qrCodeStuffType: string;
  /**
   * 适用行业
   */
  stuffIndustry: string;
  /**
   * 示例文案
   */
  imgTips: string;
  /**
   * 码路由的场景值
   * {@link KbTrafficBizSceneEnum}
   */
  trafficBizScene: string;
  /**
   * 空码库存阈值
   */
  qrCodeStockThreshold: number;
  /**
   * 示意图
   */
  qrCodeImgField: string;
  /**
   * 业务归属事业部属性（物料铺设场景）
   * {@link BizDepAttrEnum}
   */
  bizDeptAttrs: string[];
  /**
   * 活动物料分组（物料铺设场景）
   * {@link StuffTypeEnum}
   */
  group: string;
  /**
   * 铺设建议描述（物料铺设场景）
   */
  paveAdviseText: string;
  /**
   * 铺设建议示意图ID
   */
  paveAdviseField: string;
  /**
   * 铺设建议示意图对象
   */
  paveAdviseFieldVO: Attachment;
  /**
   * 机审算法图ID
   */
  machineAuditSampleImgFields: string[];
  /**
   * 机审算法图
   */
  machineAuditSampleImgFieldVOS: Attachment[];
  /**
   * 预计铺设门店数（物料铺设场景）
   */
  predictPaveShopNum: string;
}

/**
 * 状态更新请求类
 * <AUTHOR>
 * @version $Id: StuffTemplateUpStatus.java, v 0.1 2016-04-28 下午5:52 song.hus Exp $
 */
export interface StuffTemplateUpStatus extends ToString {
  /**
   * Get string.
   * @return Long
   */
  templateId: number;
  /**
   * Get stuff template status enum.
   * @return stuff template status enum
   */
  status: StuffTemplateStatusEnum;
  /**
   * Get string.
   * @return string
   */
  memo: string;
  /**
   * Getter method for property <tt>otherMemo</tt>.
   * @return property value of otherMemo
   */
  otherMemo: string;
}

/**
 * -- 空码
 * 	bindType=IN_SHOP_BINDING
 * 	remark=备注
 * 	templateType=STICKER
 * 	templateNickName=KOUBEI_QRCODE_480
 * 	quantity=2 数量
 * -- 明码
 *     bindType: DIRECT_BINDING
 * 	remark: 1
 * 	templateType: STICKER
 * 	templateNickName: KOUBEI_QRCODE_482
 * 	bindSource: excel
 * 	dataId:
 * <AUTHOR>
 */
export interface CreateQrCodeReq {
  bindType: string;
  remark: string;
  templateType: string;
  templateNickName: string;
  quantity: string;
  bindSource: string;
  dataId: string;
  /**
   * 直接绑定把 文本框的数据传输过来
   */
  text: string;
}

export interface CodeBatchReq extends BasePageQuery {
  /**
   * 生成人
   */
  applicantName: string;
}

/**
 * <AUTHOR>
 * @version $Id: StuffQrcodeApplyDto.java, v 0.1 2016-07-14 10:40 zhengyuan.zy Exp $$
 */
export interface StuffQrcodeApplyDto extends ApplyStuffQrcode {
  /**
   * Getter method for property id.
   * @return property value of id
   */
  id: number;
  /**
   * Getter method for property stuffAttrName.
   * @return property value of stuffAttrName
   */
  stuffAttrName: string;
  /**
   * Getter method for property fileUrl.
   * @return property value of fileUrl
   */
  fileUrl: string;
  /**
   * Getter method for property <tt>templateName</tt>.
   * @return property value of templateName
   */
  templateName: string;
  /**
   * Getter method for property <tt>templateSize</tt>.
   * @return property value of templateSize
   */
  templateSize: string;
  /**
   * Getter method for property <tt>templateImageURL</tt>.
   * @return property value of templateImageURL
   */
  templateImageURL: string;
  /**
   * Getter method for property <tt>unbindCount</tt>.
   * @return property value of unbindCount
   */
  unbindCount: number;
  /**
   * Getter method for property <tt>channel</tt>.
   * @return property value of channel
   */
  channel: string;
  /**
   * Getter method for property targetName.
   * @return property value of targetName
   */
  targetName: string;
  /**
   * Getter method for property isAsync.
   * @return property value of isAsync
   */
  isAsync: string;
}

export interface BindBatchReq extends BasePageQuery {
  bindTargetId: string;
  merchantPrincipalId: string;
  gmtModifiedStart: string;
  gmtModifiedEnd: string;
}

/**
 * 口碑码绑定关系dto
 * <AUTHOR>
 * @version $Id: KBCodeBindRelationDto.java, v 0.1 2017年3月2日 上午10:36:13 mingzhi.zmz Exp $
 */
export interface QrcodeBindRelationDto extends DataModel083922 {
  /**
   * Getter method for property <tt>id</tt>.
   * @return property value of id
   */
  id: number;
  /**
   * Getter method for property <tt>batchId</tt>.
   * @return property value of batchId
   */
  batchId: number;
  /**
   * Getter method for property <tt>staffPrincipalId</tt>.
   * @return property value of staffPrincipalId
   */
  staffPrincipalId: string;
  /**
   * Getter method for property <tt>staffPrincipalName</tt>.
   * @return property value of staffPrincipalName
   */
  staffPrincipalName: string;
  /**
   * Getter method for property <tt>qrCode</tt>.
   * @return property value of qrCode
   */
  qrCode: string;
  /**
   * Getter method for property <tt>providerId</tt>.
   * @return property value of providerId
   */
  providerId: string;
  /**
   * Getter method for property <tt>bindTargetId</tt>.
   * @return property value of bindTargetId
   */
  bindTargetId: string;
  /**
   * Getter method for property <tt>bindTargetType</tt>.
   * @return property value of bindTargetType
   */
  bindTargetType: string;
  /**
   * Getter method for property <tt>bindTargetValue</tt>.
   * @return property value of bindTargetValue
   */
  bindTargetValue: string;
  /**
   * Getter method for property <tt>bindTargetName</tt>.
   * @return property value of bindTargetName
   */
  bindTargetName: string;
  /**
   * Getter method for property <tt>bindBizType</tt>.
   * @return property value of bindBizType
   */
  bindBizType: string;
  /**
   * Getter method for property <tt>bizSource</tt>.
   * @return property value of bizSource
   */
  bizSource: string;
  /**
   * Getter method for property <tt>merchantId</tt>.
   * @return property value of merchantId
   */
  merchantId: string;
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  /**
   * Getter method for property <tt>resourceURL</tt>.
   * @return property value of resourceURL
   */
  resourceURL: string;
  /**
   * Getter method for property <tt>coreURL</tt>.
   * @return property value of coreURL
   */
  coreURL: string;
  /**
   * Getter method for property <tt>stuffAttrName</tt>.
   * @return property value of stuffAttrName
   */
  stuffAttrName: string;
  /**
   * Getter method for property <tt>templateId</tt>.
   * @return property value of templateId
   */
  templateId: string;
  /**
   * Getter method for property <tt>templateSize</tt>.
   * @return property value of templateSize
   */
  templateSize: string;
  /**
   * Getter method for property <tt>templateImageURL</tt>.
   * @return property value of templateImageURL
   */
  templateImageURL: string;
  /**
   * Getter method for property <tt>templateName</tt>.
   * @return property value of templateName
   */
  templateName: string;
  /**
   * Getter method for property <tt>templateNickName</tt>.
   * @return property value of templateNickName
   */
  templateNickName: string;
  /**
   * Getter method for property <tt>remark</tt>.
   * @return property value of remark
   */
  remark: string;
  /**
   * Getter method for property <tt>quantity</tt>.
   * @return property value of quantity
   */
  quantity: number;
  /**
   * Getter method for property <tt>applicantPrincipalName</tt>.
   * @return property value of applicantPrincipalName
   */
  applicantPrincipalName: string;
  /**
   * Getter method for property <tt>extInfo</tt>.
   * @return property value of extInfo
   */
  extInfo: string;
  /**
   * Getter method for property <tt>bindType</tt>.
   * @return property value of bindType
   */
  bindType: string;
  /**
   * Getter method for property <tt>status</tt>.
   * @return property value of status
   */
  status: string;
  /**
   * Getter method for property <tt>bindCount</tt>.
   * @return property value of bindCount
   */
  bindCount: { [index: string]: number };
  bindShopCount: number;
}

/**
 * <AUTHOR>
 */
export interface BindTargetCodesReq extends BasePageQuery {
  bindTargetId: string;
}

export interface BindQrCodeVO extends Serializable {
  qrCode: string;
  coreURL: string;
  resourceURL: string;
  /**
   * 绑定人
   */
  applicantPrincipalName: string;
  /**
   * 绑定时间
   */
  gmtCreate: Date;
  /**
   * 生成批次
   */
  batchId: number;
}

export interface BindPidBatchReq extends BasePageQuery {
  merchantPrincipalId: string;
  gmtModifiedStart: string;
  gmtModifiedEnd: string;
}

export interface UnbindBatchReq extends BasePageQuery {
  /**
   * 批次号
   */
  batchId: number;
  /**
   * 生成的开始时间
   */
  gmtCreateStart: string;
  /**
   * 生成的结束时间
   */
  gmtCreateEnd: string;
}

/**
 * <AUTHOR>
 */
export interface UnBindCodeReq extends BasePageQuery {
  batchId: number;
}

export interface QrCodeVO extends Serializable {
  qrCode: string;
  coreURL: string;
  resourceURL: string;
}

/**
 * <AUTHOR>
 */
export interface ParseTextReq {
  bindScene: string;
  templateNickName: string;
  /**
   * 如果是门店就是 门店ID_A,门店ID_B
   *
   * 如果是商品码就是 门店ID_A:121,门店ID_A:132
   */
  text: string;
}

export interface KbCodeTemplateConfigVO {
  /**
   * 绑定类型
   * {@link BindTypeEnum#getCode()}
   */
  id: string;
  /**
   * {@link BindTypeEnum#getDesc()}
   */
  name: string;
  /**
   * {@link BindTypeEnum#getExt()}
   */
  desc: string;
  templateNickName: string;
  /**
   * 子项目
   */
  children: KbCodeTemplateTrafficItemDetailVO[];
}

export interface ShopViewDTO {
  /**
   * 门店Id
   */
  shopId: string;
  /**
   * 门店名称
   */
  shopName: string;
  /**
   * 门店的商户PId
   */
  merchantPid: string;
  /**
   * 门店的商户名称
   */
  merchantName: string;
  /**
   * 门店的状态
   */
  status: string;
  /**
   * 省代码
   */
  provinceCode: string;
  /**
   * 城市代码
   */
  cityCode: string;
  /**
   * 地区代码
   */
  districtCode: string;
  /**
   * 省名
   */
  provinceName: string;
  /**
   * 城市名
   */
  cityName: string;
  /**
   * 区名
   */
  districtName: string;
  /**
   * 地址
   */
  address: string;
  /**
   * 纬度
   */
  latitude: number;
  /**
   * 经度
   */
  longitude: number;
  /**
   * 品牌id
   */
  brandId: string;
  /**
   * 门店logo
   */
  logoUrl: string;
  /**
   * 创建时间
   */
  createTime: Date;
  /**
   * 门店销售标
   */
  shopSaleLabels: string[];
  /**
   * 经营品类(一级类目)ID
   */
  categoryId: string;
  /**
   * 经营品类(一级类目)名称
   */
  categoryName: string;
  /**
   * 经营品类(二级类目)ID
   */
  subcategoryId: string;
  /**
   * 经营品类(二级类目)名称
   */
  subcategoryName: string;
  /**
   * 门店三级类目id
   */
  detailCategoryId: string;
  /**
   * 门店三级类目名称
   */
  detailCategoryName: string;
  /**
   * 门店展示状态
   */
  displayStatus: string;
  /**
   * 门店kp信息
   */
  kpInfo: string;
  /**
   * 联系电话
   */
  mobile: string;
}

/**
 * <AUTHOR>
 */
export interface QueryByNameReq {
  keyword: string;
  size: number;
}

/**
 * 层叠展示VO
 * <AUTHOR>
 * @version $Id: Cascader.java, v 0.1 2016年1月22日 下午5:16:18 lvfei Exp $
 */
export interface CascaderVO {
  /**
   * Getter method for property <tt>value</tt>.
   * @return property value of value
   */
  value: string;
  /**
   * Getter method for property <tt>label</tt>.
   * @return property value of label
   */
  label: string;
}

/**
 * <AUTHOR>
 */
export interface SearchAgentShopReq {
  shopName: string;
}

/**
 * 口碑码搜索门店的信息
 * <AUTHOR>
 * @version $Id: KbCodeShopVO.java, v 0.1 2017年3月2日 下午4:20:18 junrui.mjr Exp $
 */
export interface KbCodeShopVO {
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>shopName</tt>.
   * @return property value of shopName
   */
  shopName: string;
  /**
   * Getter method for property <tt>merchantPid</tt>.
   * @return property value of merchantPid
   */
  merchantPid: string;
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  /**
   * Getter method for property <tt>status</tt>.
   * @return property value of status
   */
  status: string;
  /**
   * Getter method for property <tt>province</tt>.
   * @return property value of province
   */
  province: string;
  /**
   * Getter method for property <tt>city</tt>.
   * @return property value of city
   */
  city: string;
  /**
   * Getter method for property <tt>district</tt>.
   * @return property value of district
   */
  district: string;
  /**
   * Getter method for property <tt>provinceCode</tt>.
   * @return property value of provinceCode
   */
  provinceCode: string;
  /**
   * Getter method for property <tt>cityCode</tt>.
   * @return property value of cityCode
   */
  cityCode: string;
  /**
   * Getter method for property <tt>districtCode</tt>.
   * @return property value of districtCode
   */
  districtCode: string;
}

/**
 * 判断门店是否为快消行业门店的请求入参
 * <AUTHOR> <EMAIL>
 * @version 1.0.0
 * @create 2023/1/28 19:21
 */
export interface IsRetailShopReq {
  /**
   * 门店ID
   */
  shopId: string;
  /**
   * 请求来源app；
   * crmhome、merchantApp、kbservcenter
   */
  appSource: string;
}

/**
 * 展示门店商户码、门店收款二维码的请求入参
 * <AUTHOR> <EMAIL>
 * @version 1.0.0
 * @create 2023/1/28 17:13
 */
export interface ShowKbShopQrCodeReq {
  /**
   * 码图片类型；
   * @see com.alsc.kbt.toolkit.enums.QrCodeImageType
   */
  codeType: string;
  /**
   * 商户PID；
   */
  partnerId: string;
  /**
   * 门店ID；
   */
  shopId: string;
  /**
   * 门店名称；
   */
  shopName: string;
  /**
   * 强制刷新图片标识；
   * 0或者空：不刷新
   * 1：刷新
   */
  refresh: string;
  refreshBoolean: boolean;
}

/**
 * 展示店铺收款二维码和门店宣传码请求入参
 * <AUTHOR> <EMAIL>
 * @version 1.0.0
 * @create 2023/1/29 10:47
 */
export interface ShowKbShopQrCodeH5Req {
  /**
   * 门店ID；
   */
  shopId: string;
  biz: string;
  /**
   * 强制刷新图片标识；
   * 0或者空：不刷新
   * 1：刷新
   */
  refresh: string;
}

/**
 * 展示店铺收款二维码和门店宣传码请求出参
 * <AUTHOR> <EMAIL>
 * @version 1.0.0
 * @create 2023/1/29 10:48
 */
export interface ShowKbShopQrCodeH5VO {
  /**
   * 门店收款二维码-普通版本URL
   */
  qrCodeUrl: string;
  /**
   * 店铺宣传码普通版本URL
   */
  infoQrCodeUrl: string;
}

/**
 * <AUTHOR>
 * @version : CreateStuffPaveRequest.java, v 0.1 2020年04月14日 16:50 honger.lb Exp $
 */
export interface CreateStuffPaveRequest {
  /**
   * 幂等请求 ID
   */
  requestId: string;
  /**
   * 铺设对象 id
   */
  targetId: string;
  /**
   * 铺设对象名
   */
  targetName: string;
  /**
   * 铺设对象类型
   */
  targetIdType: string;
  /**
   * 国家id
   */
  countryId: string;
  /**
   * 省id
   */
  provinceId: string;
  /**
   * 市id
   */
  cityId: string;
  /**
   * 城市名
   */
  cityName: string;
  /**
   * 区县id
   */
  areaId: string;
  /**
   * 详细地址
   */
  addressDetail: string;
  /**
   * 记录物料时的经度
   */
  longitude: string;
  /**
   * 记录铺设时的纬度
   */
  latitude: string;
  /**
   * 铺设描述
   */
  paveContent: string;
  /**
   * 门头照
   */
  shopFrontPics: string;
  /**
   * 签到距离
   */
  signDistance: string;
  /**
   * 物料图片信息
   */
  attachmentList: PaveAttachmentRequest[];
  /**
   * 分享对象
   */
  shareVisitRecordRequest: PaveInfoShareRequest;
  /**
   * 渠道
   */
  channel: string;
}

/**
 * <AUTHOR>
 * @version : PaveCheckDetailInfoVO.java, v 0.1 2020年04月27日 21:56 honger.lb Exp $
 */
export interface PaveCheckAuditDetailVO {
  /**
   * 是否有双十二标签
   */
  hasDual12Label: boolean;
  /**
   * 铺设详情
   */
  stuffCheckDetailVO: PaveCheckDetailVO;
}

/**
 * <AUTHOR>
 * @version : PaveDetailInfoVO.java, v 0.1 2020年04月16日 18:02 honger.lb Exp $
 */
export interface PaveDetailInfoVO extends PaveBaseInfoVO {
  /**
   * 国家id
   */
  countryId: string;
  /**
   * 省id
   */
  provinceId: string;
  /**
   * 市id
   */
  cityId: string;
  /**
   * 城市名
   */
  cityName: string;
  /**
   * 区县id
   */
  areaId: string;
  /**
   * 详细地址
   */
  addressDetail: string;
  /**
   * 记录物料时的经度
   */
  longitude: string;
  /**
   * 记录铺设时的纬度
   */
  latitude: string;
  /**
   * 门头照
   */
  shopFrontPics: Attachment;
  /**
   * 物料图片信息
   */
  attachmentList: Attachment[];
  /**
   * 签到距离
   */
  signDistance: string;
  /**
   * 驳回原因
   */
  rejectReason: string;
  /**
   * 备注
   */
  memo: string;
  /**
   * 创建人
   */
  creatorId: string;
  /**
   * 创建人名称
   */
  createName: string;
  /**
   * 创建人类型
   */
  creatorType: string;
}

/**
 * <AUTHOR>
 * @version : PageQueryPaveRequest.java, v 0.1 2020年04月26日 11:04 honger.lb Exp $
 */
export interface PageQueryPaveRequest {
  /**
   * 页数
   */
  pageNo: number;
  /**
   * 总行数
   */
  pageSize: number;
  data: QueryPaveInfoRequest;
}

/**
 * <AUTHOR>
 * @version : QueryTargetRequest.java, v 0.1 2020年04月23日 21:31 honger.lb Exp $
 */
export interface QueryTargetRequest {
  /**
   * 主体 id
   */
  targetId: string;
  /**
   * 主体类型
   */
  targetType: string;
}

/**
 * <AUTHOR>
 * @version : PaveTargetVO.java, v 0.1 2020年04月23日 21:32 honger.lb Exp $
 */
export interface PaveTargetVO {
  id: string;
  /**
   * 类型
   */
  type: string;
  /**
   * 名字
   */
  name: string;
  /**
   * 国家
   */
  countryId: string;
  /**
   * 省
   */
  provinceId: string;
  /**
   * 城市
   */
  cityId: string;
  /**
   * 城市名称
   */
  cityName: string;
  /**
   * 地区
   */
  districtId: string;
  /**
   * 地址
   */
  address: string;
  /**
   * 经度
   */
  longitude: number;
  /**
   * 纬度
   */
  latitude: number;
}

/**
 * <AUTHOR>
 * @version : QueryTemplateRequest.java, v 0.1 2020年04月14日 16:13 honger.lb Exp $
 */
export interface QueryTemplateRequest {
  /**
   * 模板状态
   * {@link StuffTemplateStatusEnum}
   */
  status: string;
  /**
   * 是否过滤铺设时间失效的物料
   */
  filterPaveExpireTime: boolean;
}

/**
 * <AUTHOR>
 * @version : PaveTemplateVO.java, v 0.1 2020年04月14日 15:47 honger.lb Exp $
 */
export interface PaveTemplateVO {
  /**
   * id
   */
  id: number;
  /**
   * 物料名称
   */
  name: string;
  /**
   * 物料别名
   */
  nickName: string;
  /**
   * 状态
   * {@link StuffTemplateStatusEnum}
   */
  status: string;
  /**
   * 铺设建议描述（物料铺设场景）
   */
  paveAdviseText: string;
  /**
   * 铺设建议示意图对象
   */
  paveAdviseField: Attachment;
  /**
   * 物料上传图片匹配规则模式
   * {@link TemplateExpressionEnum}
   */
  expression: string;
  /**
   * 物料上传图片匹配规则数量
   */
  picNum: string;
  /**
   * 物料上传审核规则
   * {@link CheckTypeEnum}
   */
  checkType: string;
  /**
   * 活动物料分组（物料铺设场景）
   * {@link StuffTypeEnum}
   */
  group: string;
}

/**
 * <AUTHOR>
 * @version v 0.1
 * @classname
 * @date 8/8/22
 */
export interface OssSignatureCO extends ToString {
  policy: string;
  /**
   * 签名
   */
  signature: string;
  /**
   * 存储key名
   */
  key: string;
  /**
   * 域名
   */
  host: string;
  ossaccessKeyId: string;
}

/**
 * <AUTHOR>
 * @version Id: ImportFileGatewayRequest.java, v 0.1 2022/12/27 20:26 shengwei Exp $$
 */
export interface ExportFileGatewayRequest extends AmapAuditGwBaseRequest {
  /**
   * 业务code
   */
  bizCode: string;
  /**
   * 外部业务id
   */
  outBizId: string;
  /**
   * 业务参数
   */
  bizParam: string;
  /**
   * 备注
   */
  memo: string;
}

/**
 * <AUTHOR>
 * @version Id: ImportFileGatewayRequest.java, v 0.1 2022/12/27 20:26 shengwei Exp $$
 */
export interface ExportFilePageQueryRecordGatewayRequest extends AmapAuditGwPageBaseRequest {
  /**
   * 业务code
   */
  bizCode: string;
  /**
   * 业务id
   */
  bizId: string;
}

/**
 * <AUTHOR>
 * @version Id: ImportFileVO.java, v 0.1 2022/12/27 20:27 shengwei Exp $$
 */
export interface ExportFileRecordVO extends Serializable {
  /**
   * 导出id
   */
  exportId: number;
  /**
   * 业务code
   */
  bizCode: string;
  /**
   * 业务名
   */
  bizName: string;
  /**
   * 操作id
   */
  operatorId: string;
  /**
   * 操作名
   */
  operatorName: string;
  /**
   * 导出时间
   */
  exportDate: Date;
  /**
   * 导出文件名
   */
  exportFileName: string;
  /**
   * 导出状态
   */
  exportStatus: string;
}

/**
 * <AUTHOR>
 * @version Id: ImportFileGatewayRequest.java, v 0.1 2022/12/27 20:26 shengwei Exp $$
 */
export interface ImportFileGatewayRequest {
  /**
   * 业务code
   */
  bizCode: string;
  /**
   * 外部业务id
   */
  outBizId: string;
  /**
   * 导入文件url
   */
  importFileUrl: string;
  /**
   * oss文件key
   * 注意，只有url没传时才会生效
   */
  ossFileKey: string;
  /**
   * oss访问key
   * 注意，只有url没传时才会生效
   */
  ossAccessKey: string;
  /**
   * 导入文件名
   */
  importFileName: string;
  /**
   * 请求id
   */
  requestId: string;
  /**
   * 备注
   */
  memo: string;
}

/**
 * <AUTHOR>
 * @version Id: ImportFileGatewayRequest.java, v 0.1 2022/12/27 20:26 shengwei Exp $$
 */
export interface ImportFilePageQueryRecordGatewayRequest extends BasePageQuery {
  /**
   * 业务code
   */
  bizCode: string;
  /**
   * 业务id
   */
  bizId: string;
}

/**
 * <AUTHOR>
 * @version Id: ImportFileVO.java, v 0.1 2022/12/27 20:27 shengwei Exp $$
 */
export interface ImportFileRecordVO extends Serializable {
  /**
   * 导出id
   */
  importId: number;
  /**
   * 业务code
   */
  bizCode: string;
  /**
   * 业务名
   */
  bizName: string;
  /**
   * 操作id
   */
  operatorId: string;
  /**
   * 操作名
   */
  operatorName: string;
  /**
   * 导入时间
   */
  importDate: Date;
  /**
   * 导入文件名
   */
  importFileName: string;
  /**
   * 导入状态
   */
  importStatus: string;
  /**
   * 总数量
   */
  totalCount: number;
  /**
   * 成功数量
   */
  successCount: number;
  /**
   * 失败数量
   */
  failCount: number;
  /**
   * 结果信息
   */
  resultMessage: string;
  /**
   * 是否可以导出成功结果
   */
  canExportSuccessResult: boolean;
}

/**
 * 查询代运营商户列表请求
 * <AUTHOR>
 * @version : AgentOperationMerchantListRequest.java, v 0.1 2023年05月27日 下午3:20 LiShu Exp $
 */
export interface AgentOperationMerchantTaskListRequest extends BaseTaskQueryRequest {
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>taskStatus</tt>.
   * @return property value of taskStatus
   */
  taskStatus: string;
  /**
   * Getter method for property <tt>commercializationQueryTags</tt>.
   * @return property value of commercializationQueryTags
   */
  commercializationQueryTags: string[];
  /**
   * Getter method for property <tt>shopBaseTaskFinishStatus</tt>.
   * @return property value of shopBaseTaskFinishStatus
   */
  shopBaseTaskFinishStatus: string;
  /**
   * Getter method for property <tt>advertisementTaskStatus</tt>.
   * @return property value of advertisementTaskStatus
   */
  advertisementTaskStatus: string;
  /**
   * AD_CURRENT_MONTH_COST: 当月总消耗
   * AD_CURRENT_BALANCE:现金余额
   */
  sortBy: string;
  /**
   * ASC：升序
   * DESC：降序
   */
  sortType: string;
  /**
   * 商户意向
   */
  merchantIntentionCondition: string[];
}

export interface GatewayResult<T> extends Serializable {
  success: boolean;
  resultMessage: string;
  resultCode: string;
  data: T;
  extInfo: { [index: string]: any };
}

/**
 * 分页通用插件
 * <AUTHOR>
 * @version : CommonPageData.java, v 0.1 2023年06月04日 下午3:11 LiShu Exp $
 */
export interface CommonPageData<T> extends ToString {
  /**
   * get totalCount
   * @return
   */
  totalCount: number;
  /**
   * get pageNo
   * @return
   */
  pageNo: number;
  /**
   * get pageSize
   * @return
   */
  pageSize: number;
  /**
   * get totalPage
   * @return
   */
  totalPage: number;
  /**
   * 获取下一页
   * @return
   */
  nextPage: number;
  /**
   * Getter method for property <tt>hasNextPage</tt>.
   * @return property value of hasNextPage
   */
  hasNextPage: boolean;
  /**
   * get list
   * @return
   */
  list: T[];
  /**
   * 获取前一页
   * @return
   */
  prevPage: number;
  /**
   * 列表是否为空
   * @return
   */
  emptyForList: boolean;
  length: number;
  offset: number;
}

/**
 * 代运营商户模型
 * <AUTHOR>
 * @version : AgentOperationMerchantDTO.java, v 0.1 2023年05月27日 下午3:29 LiShu Exp $
 */
export interface AgentOperationMerchantTaskDTO extends ToString {
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  /**
   * Getter method for property <tt>taskStatus</tt>.
   * @return property value of taskStatus
   */
  taskStatus: string;
  /**
   * Getter method for property <tt>adTaskCompletedTotalNum</tt>.
   * @return property value of adTaskCompletedTotalNum
   */
  adTaskCompletedTotalNum: string;
  /**
   * Getter method for property <tt>adTaskTotalNum</tt>.
   * @return property value of adTaskTotalNum
   */
  adTaskTotalNum: string;
  /**
   * Getter method for property <tt>infrastructTaskCompletedTotalNum</tt>.
   * @return property value of infrastructTaskCompletedTotalNum
   */
  infrastructTaskCompletedTotalNum: string;
  /**
   * Getter method for property <tt>infrastructTaskTotalNum</tt>.
   * @return property value of infrastructTaskTotalNum
   */
  infrastructTaskTotalNum: string;
  /**
   * Getter method for property <tt>showMerchantBusinessNews</tt>.
   * @return property value of showMerchantBusinessNews
   */
  showMerchantBusinessNews: boolean;
  /**
   * Getter method for property <tt>mainShopName</tt>.
   * @return property value of mainShopName
   */
  mainShopName: string;
  /**
   * Getter method for property <tt>adTaskCompletionProgress</tt>.
   * @return property value of adTaskCompletionProgress
   */
  adTaskCompletionProgress: string;
  /**
   * Getter method for property <tt>infrastructureCompletionProgress</tt>.
   * @return property value of infrastructureCompletionProgress
   */
  infrastructureCompletionProgress: string;
  /**
   * Getter method for property <tt>adCurrentMonthCost</tt>.
   * @return property value of adCurrentMonthCost
   */
  adCurrentMonthCost: string;
  /**
   * Getter method for property <tt>adCurrentBalance</tt>.
   * @return property value of adCurrentBalance
   */
  adCurrentBalance: string;
  /**
   * Getter method for property <tt>adRechargeCount</tt>.
   * @return property value of adRechargeCount
   */
  adRechargeCount: Number;
  /**
   * Getter method for property <tt>shopBaseTaskFinishStatus</tt>.
   * @return property value of shopBaseTaskFinishStatus
   */
  shopBaseTaskFinishStatus: string;
  /**
   * Getter method for property <tt>advertisementTaskStatus</tt>.
   * @return property value of advertisementTaskStatus
   */
  advertisementTaskStatus: string;
  /**
   * Getter method for property <tt>merchantLabels</tt>.
   * @return property value of merchantLabels
   */
  merchantLabels: string[];
  /**
   * 广告建议标签
   */
  suggestionLabels: SuggestionLabelDTO[];
  /**
   * 商户意向
   */
  merchantIntention: MerchantIntentionDTO[];
  /**
   * 是否展示电话沟通按钮
   * 灰度开关控制
   */
  showCallButton: boolean;
}

/**
 * 代运营门店任务列表
 * <AUTHOR>
 * @version : AgentOperationShopListRequest.java, v 0.1 2023年05月27日 下午3:49 LiShu Exp $
 */
export interface AgentOperationShopTaskListRequest extends BaseTaskQueryRequest {
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>shopName</tt>.
   * @return property value of shopName
   */
  shopName: string;
  /**
   * Getter method for property <tt>shopTaskStatus</tt>.
   * @return property value of shopTaskStatus
   */
  shopTaskStatus: string;
  /**
   * Getter method for property <tt>includeShopTaskProcess</tt>.
   * @return property value of includeShopTaskProcess
   */
  includeShopTaskProcess: boolean;
  /**
   * 新增商家分筛选条件
   * @see ShopScoreConditionEnum
   */
  shopScoreCondition: string;
  /**
   * 新增商家分筛选条件多选
   * @see ShopScoreConditionEnum
   */
  shopScoreConditionList: string[];
  /**
   * 门店poi
   */
  poiId: string;
  /**
   * 枚举，根据某字段排序
   * QUANTITY_SCORE：质量分
   */
  sortBy: string;
  /**
   * asc：升序
   * desc：降序
   */
  sortType: string;
  /**
   * 商户通过期筛选条件
   */
  shangHuTongExpireCondition: string[];
  /**
   * 商户意向筛选条件
   * @return
   */
  merchantIntentionCondition: string[];
  /**
   * 调用场景
   */
  source: string;
}

/**
 * 代运营门店基建任务列表
 * <AUTHOR>
 * @version : AgentOperationShopTaskDTO.java, v 0.1 2023年05月27日 下午3:47 LiShu Exp $
 */
export interface AgentOperationShopTaskDTO extends ToString {
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>shopName</tt>.
   * @return property value of shopName
   */
  shopName: string;
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>shopTaskStatus</tt>.
   * @return property value of shopTaskStatus
   */
  shopTaskStatus: string;
  /**
   * Getter method for property <tt>infrastructTaskCompletedTotalNum</tt>.
   * @return property value of infrastructTaskCompletedTotalNum
   */
  infrastructTaskCompletedTotalNum: string;
  /**
   * Getter method for property <tt>infrastructTaskTotalNum</tt>.
   * @return property value of infrastructTaskTotalNum
   */
  infrastructTaskTotalNum: string;
  /**
   * Getter method for property <tt>showShopBusinessNews</tt>.
   * @return property value of showShopBusinessNews
   */
  showShopBusinessNews: boolean;
  /**
   * Getter method for property <tt>taskFlowInstanceId</tt>.
   * @return property value of taskFlowInstanceId
   */
  taskFlowInstanceId: string;
  /**
   * Getter method for property <tt>shopQualityScore</tt>.
   * @return property value of shopQualityScore
   */
  shopQualityScore: string;
  /**
   * poiId
   */
  poiId: string;
  /**
   * 商户名称
   */
  merchantName: string;
  /**
   * 商户通到期时间
   */
  shangHuTongExpireTime: string;
  /**
   * 商户通状态
   */
  shangHuTongStatus: string;
  shopLabels: string[];
  /**
   * 商户通子状态
   */
  shangHuTongSubStatus: string;
  /**
   * 广告建议标签
   */
  suggestionLabels: SuggestionLabelDTO[];
  merchantIntention: MerchantIntentionDTO[];
  /**
   * 支付跳转链接
   */
  payJumpList: TaskJumpDTO[];
  /**
   * 门店地址
   */
  address: string;
  /**
   * 门店头图
   */
  pic: string;
}

/**
 * 代运营统计数据请求
 * <AUTHOR>
 * @version : AgentOperationStatisticsRequest.java, v 0.1 2023年05月27日 下午3:09 LiShu Exp $
 */
export interface AgentOperationStatisticsRequest extends BaseTaskRequest {}

/**
 * 代运营统计数据
 * <AUTHOR>
 * @version : AgentOperationStatisticsDTO.java, v 0.1 2023年05月27日 下午3:12 LiShu Exp $
 */
export interface AgentOperationStatisticsDTO extends ToString {
  /**
   * Getter method for property <tt>agentMerchantTotalNum</tt>.
   * @return property value of agentMerchantTotalNum
   */
  agentMerchantTotalNum: string;
  /**
   * Getter method for property <tt>agentShopTotalNum</tt>.
   * @return property value of agentShopTotalNum
   */
  agentShopTotalNum: string;
  /**
   * Getter method for property <tt>adTaskCompletionRate</tt>.
   * @return property value of adTaskCompletionRate
   */
  adTaskCompletionRate: string;
  /**
   * Getter method for property <tt>infrastructTaskCompletionRate</tt>.
   * @return property value of infrastructTaskCompletionRate
   */
  infrastructTaskCompletionRate: string;
  /**
   * Getter method for property <tt>wangpuInfrastructTaskCompletionRate</tt>.
   * @return property value of wangpuInfrastructTaskCompletionRate
   */
  wangpuInfrastructTaskCompletionRate: string;
}

/**
 * 门店基建任务详情
 * <AUTHOR>
 * @version : ShopInfrastTaskDetailRequest.java, v 0.1 2023年05月27日 下午4:15 LiShu Exp $
 */
export interface AgentOperationTaskDetailRequest extends BaseTaskRequest {
  /**
   * Getter method for property <tt>taskDetailScene</tt>.
   * @return property value of taskDetailScene
   */
  taskDetailScene: string;
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>taskFlowInstanceId</tt>.
   * @return property value of taskFlowInstanceId
   */
  taskFlowInstanceId: string;
}

/**
 * 代运营任务详情
 * <AUTHOR>
 * @version : AgentOperationShopInfrastTaskDetailDTO.java, v 0.1 2023年05月27日 下午4:19 LiShu Exp $
 */
export interface AgentOperationTaskDetailDTO extends ToString {
  /**
   * Getter method for property <tt>taskFlowInstanceId</tt>.
   * @return property value of taskFlowInstanceId
   */
  taskFlowInstanceId: string;
  /**
   * Getter method for property <tt>agentOperationTaskJumpList</tt>.
   * @return property value of agentOperationTaskJumpList
   */
  agentOperationTaskJumpList: TaskJumpDTO[];
  /**
   * Getter method for property <tt>taskDetailDTOList</tt>.
   * @return property value of taskDetailDTOList
   */
  taskDetailDTOList: TaskDetailDTO[];
  /**
   * 门店id
   */
  shopId: string;
  /**
   * 任务类型
   */
  taskDetailScene: string;
}

/**
 * <AUTHOR>
 * @version $Id: AgentOperationPerformanceStatisticsRequest.java, v 0.1 2023-09-07 5:32 PM boliang.hbl Exp $$
 * DESC:
 */
export interface AgentOperationPerformanceStatisticsRequest extends BaseTaskRequest {
  /**
   * Getter method for property <tt>periodValue</tt>.
   * @return property value of periodValue
   */
  periodValue: string;
}

/**
 * 代运营绩效统计数据
 * <AUTHOR>
 * @version : AgentOperationStatisticsDTO.java, v 0.1 2023年05月27日 下午3:12 LiShu Exp $
 */
export interface AgentPerformanceStatisticsDTO extends ToString {
  /**
   * Getter method for property <tt>rankNo</tt>.
   * @return property value of rankNo
   */
  rankNo: string;
  /**
   * Getter method for property <tt>rootFactor</tt>.
   * @return property value of rootFactor
   */
  rootFactor: string;
  /**
   * Getter method for property <tt>inComeFactor</tt>.
   * @return property value of inComeFactor
   */
  inComeFactor: string;
  /**
   * Getter method for property <tt>adMerchantFactor</tt>.
   * @return property value of adMerchantFactor
   */
  adMerchantFactor: string;
  /**
   * Getter method for property <tt>infrastructureFactor</tt>.
   * @return property value of infrastructureFactor
   */
  infrastructureFactor: string;
  /**
   * Getter method for property <tt>indicatorTtemDTOList</tt>.
   * @return property value of indicatorTtemDTOList
   */
  indicatorTtemDTOList: IndicatorTtemDTO[];
}

export interface AgentOperationShopChangeRequest extends BaseTaskRequest {}

export interface AgentOperationShopChangeDTO extends ToString {
  /**
   * 门店分异动数
   */
  scoreAbnormalNumb: string;
  /**
   * 门店分下降数
   */
  scoreDelineNumb: string;
  /**
   * 4~5分门店
   */
  scoreFourToFiveNumb: string;
  /**
   * 5~6分门店
   */
  scoreFiveToSixNumb: string;
  /**
   * Getter method for property adTaskSummary.
   * @return property value of adTaskSummary
   */
  adTaskSummary: AdTaskSummaryDTO[];
  /**
   * 需要续约商户通的门店数
   */
  shangHuTongRenewalNumb: string;
  /**
   * 商户通预警对象
   */
  shangHuTongRenewalUrgentDTO: ShangHuTongRenewalUrgentDTO;
}

export interface AgentOperationMerchantDetailRequest extends BaseTaskRequest {
  /**
   * 商户pid
   */
  pid: string;
}

export interface AgentOperationMerchantDetailDTO extends ToString {
  /**
   * 商户id
   */
  pid: string;
  /**
   * 商户名称
   */
  merchantName: string;
  /**
   * 主店名
   */
  mainShopName: string;
  /**
   * 广告当月总消耗
   */
  adCurrentMonthCost: string;
  /**
   * 广告现金余额
   */
  adCurrentBalance: string;
  /**
   * 累计充值
   */
  adRechargeCount: Number;
  /**
   * 商户标签
   */
  merchantLabels: string[];
  /**
   * 最近一次通话时间
   */
  lastCallTime: string;
  /**
   * 营业门店总数
   */
  shopOpenNum: number;
  /**
   * 商户通在约门店总数
   */
  shangHuTongShopNum: number;
}

/**
 * 代运营短链接生成
 * <AUTHOR>
 * @version : AgentOperationShortLinkRequest.java, v 0.1 2023年06月20日 下午3:10 LiShu Exp $
 */
export interface AgentOperationShortLinkRequest extends BaseTaskRequest {
  /**
   * Getter method for property <tt>longLinks</tt>.
   * @return property value of longLinks
   */
  longLinks: string;
}

/**
 * 短链接服务
 * <AUTHOR>
 * @version : AgentOperationShortLinkDTO.java, v 0.1 2023年06月20日 下午3:12 LiShu Exp $
 */
export interface AgentOperationShortLinkDTO extends ToString {
  /**
   * Getter method for property <tt>shortLinks</tt>.
   * @return property value of shortLinks
   */
  shortLinks: string;
}

/**
 * <AUTHOR>
 * @date 2023/06/15
 */
export interface SmartWordTemplateInstRecallRequst extends BaseRequest {
  /**
   * leadsId
   */
  leadsId: string;
  /**
   * Getter method for property <tt>bizType</tt>.
   * @return property value of bizType
   */
  bizType: string;
  /**
   * Getter method for property <tt>recallParams</tt>.
   * @return property value of recallParams
   */
  recallParams: { [index: string]: string };
  /**
   * 操作者信息
   */
  operatorInfo: OperatorInfo671c73;
}

/**
 * 端上灰度切流配置查询请求
 * <AUTHOR>
 * @date 2023/07/11/20:07
 */
export interface GrayConfigQueryGwRequest extends GwBaseRequestDTO {}

export interface GwBaseResultDTO<T> extends ToStringbd7e46 {
  code: string;
  message: string;
  success: boolean;
  msgInfo: string;
  msgCode: string;
  traceId: string;
  data: T;
  extInfo: { [index: string]: any };
}

/**
 * 端上灰度切流配置查询结果
 * <AUTHOR>
 * @date 2023/07/11/20:07
 */
export interface BizGrayConfigGwResultVO extends ToStringbd7e46 {
  /**
   * 读切流标记（true: 处于切流范围内）
   */
  inGray: boolean;
}

/**
 * 智能话术匹配请求
 * <AUTHOR>
 * @version $Id: SmartWordMatchRequest.java, v 0.1 2023年06月05日 17:56 kaio Exp $
 */
export interface SmartWordMatchRequest extends GwBaseRequestDTO {
  /**
   * targetId
   */
  targetId: string;
  /**
   * 场景类型
   * tel : 电销
   * ftf : 面销
   */
  saleType: string;
  entrance: string;
  bizType: string;
}

/**
 * <AUTHOR>
 * @version $Id: SmartWordMatchResultVO.java, v 0.1 2024年02月22日 11:14 荒丘 Exp $
 */
export interface SmartWordMatchResultVO extends ToString {
  /**
   * 模板渲染唯一ID
   */
  templateRenderId: string;
  /**
   * 渲染内容
   */
  word: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryItemStatusChangeRequest.java, v 0.1 2023年09月20日 15:50 荒丘 Exp $
 */
export interface VisitCategoryItemStatusChangeRequest extends BaseRequest {
  /**
   * 分类ID
   */
  visitCategoryId: string;
  /**
   * 拜访事项ID
   */
  visitItemId: string;
  /**
   * 要切换到的状态
   */
  status: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryItemCreateRequest.java, v 0.1 2023年09月20日 15:49 荒丘 Exp $
 */
export interface VisitCategoryItemCreateRequest extends BaseRequest {
  /**
   * 要创建的拜访分类信息
   */
  visitCategory: VisitCategoryVO;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryItemDeleteRequest.java, v 0.1 2023年09月20日 15:49 荒丘 Exp $
 */
export interface VisitCategoryItemDeleteRequest extends BaseRequest {
  /**
   * 分类ID
   */
  visitCategoryId: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryItemModifyRequest.java, v 0.1 2023年09月20日 15:49 荒丘 Exp $
 */
export interface VisitCategoryItemModifyRequest extends BaseRequest {
  /**
   * 要修改的分类信息
   */
  visitCategory: VisitCategoryVO;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryItemDetailQueryRequest.java, v 0.1 2023年09月20日 15:47 荒丘 Exp $
 */
export interface VisitCategoryItemDetailQueryRequest extends BaseRequest {
  /**
   * 分类ID
   */
  visitCategoryId: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryVO.java, v 0.1 2023年09月20日 15:52 荒丘 Exp $
 */
export interface VisitCategoryVO extends ToString {
  /**
   * 分类ID
   */
  visitCategoryId: string;
  /**
   * 分类名称
   */
  name: string;
  /**
   * 分类描述
   */
  intro: string;
  /**
   * 状态 ENABLE（已启用）DISABLE（已停用）
   * @see VisitConfigStatusEnum
   */
  status: string;
  statusText: string;
  /**
   * 创建人信息
   */
  creatorId: string;
  creatorName: string;
  /**
   * 拜访事项列表
   */
  itemList: VisitItemVO[];
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryItemPageQueryRequest.java, v 0.1 2023年09月20日 15:47 荒丘 Exp $
 */
export interface VisitCategoryItemPageQueryRequest extends BaseRequest {
  /**
   * 只展示有效状态的
   */
  onlyEnableStatus: boolean;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryEleVO.java, v 0.1 2023年11月23日 21:49 荒丘 Exp $
 */
export interface VisitCategoryEleVO extends ToString {
  /**
   * 分类ID
   */
  visitCategoryId: string;
  /**
   * 分类名称
   */
  name: string;
  /**
   * 分类描述
   */
  intro: string;
  /**
   * 分类被有效配置使用的数量
   */
  usedConfigCount: number;
  /**
   * 状态 ENABLE（已启用）DISABLE（已停用）
   * @see VisitConfigStatusEnum
   */
  status: string;
  statusText: string;
  /**
   * 创建人信息
   */
  creatorId: string;
  creatorName: string;
  itemList: VisitItemEleVO[];
  /**
   * 行动点列表
   */
  actionList: VisitActionVO[];
}

/**
 * 电话解密接口
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:02
 */
export interface VisitPhoneDecodeRequest extends GwBaseRequestDTO {
  /**
   * 加密-电话信息
   */
  encodePhone: string;
}

/**
 * 电话解密接口
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:37
 */
export interface VisitPhoneDecodeVO extends ToString {
  /**
   * 解密后电话
   */
  decodePhone: string;
}

/**
 * 基础请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:02
 */
export interface VisitBaseRequest extends GwBaseRequestDTO {
  /**
   * 业务来源
   */
  source: string;
}

/**
 * 基础配置信息
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:37
 */
export interface VisitBaseConfigInfoVO extends ToString {
  /**
   * code
   */
  code: string;
  /**
   * desc
   */
  desc: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigStatusChangeRequest.java, v 0.1 2023年09月20日 16:14 荒丘 Exp $
 */
export interface VisitConfigStatusChangeRequest extends BaseRequest {
  /**
   * 拜访配置ID
   */
  configId: string;
  /**
   * 要切换到的状态
   * ENABLE（已启用）DISABLE（已停用）
   * @see VisitEnableStatusEnum
   */
  status: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigCreateRequest.java, v 0.1 2023年09月20日 16:09 荒丘 Exp $
 */
export interface VisitConfigCreateRequest extends BaseRequest {
  /**
   * 要创建的拜访配置信息
   */
  visitConfig: VisitConfigVO;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigDeleteRequest.java, v 0.1 2023年12月06日 19:19 荒丘 Exp $
 */
export interface VisitConfigDeleteRequest extends BaseRequest {
  /**
   * 拜访配置ID
   */
  configId: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigModifyRequest.java, v 0.1 2023年09月20日 16:10 荒丘 Exp $
 */
export interface VisitConfigModifyRequest extends BaseRequest {
  /**
   * 要修改的拜访配置信息
   */
  visitConfig: VisitConfigVO;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigPageQueryRequest.java, v 0.1 2023年09月20日 16:09 荒丘 Exp $
 */
export interface VisitConfigPageQueryRequest extends BasePageNoQueryRequest {}

export interface Pagination<T> extends ToStringbd7e46 {
  start: number;
  count: number;
  totalCount: number;
  curPage: number;
  prePage: number;
  nextPage: number;
  totalPage: number;
  hasMore: boolean;
  data: T;
  nextPageStart: number;
  prePageStart: number;
}

/**
 * 拜访配置列表VO
 * <AUTHOR>
 * @version $Id: VisitConfigEleVO.java, v 0.1 2023年11月21日 19:57 荒丘 Exp $
 */
export interface VisitConfigEleVO extends VisitConfigSimpleVO {
  targetTypeNameList: string[];
  visitChannelNameList: string[];
  sceneFieldNameList: string[];
  visitCategoryNameList: string[];
  /**
   * 状态 ENABLE（已启用）DISABLE（已停用）
   * @see VisitConfigStatusEnum
   */
  status: string;
  statusText: string;
  /**
   * 行动点列表
   */
  actionList: VisitActionVO[];
  /**
   * 创建人信息
   */
  creatorId: string;
  creatorName: string;
  /**
   * 最近一次修改人信息
   */
  modifierId: string;
  modifierName: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigDetailQueryRequest.java, v 0.1 2023年09月20日 14:10 荒丘 Exp $
 */
export interface VisitConfigDetailQueryRequest extends BaseRequest {
  /**
   * 拜访配置ID
   */
  configId: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigVO.java, v 0.1 2023年09月20日 14:11 荒丘 Exp $
 */
export interface VisitConfigVO extends ToString {
  visitConfigId: string;
  visitConfigVersion: string;
  visitConfigName: string;
  /**
   * 准入岗位列表
   */
  entryJobList: JobVO[];
  /**
   * 准入主图类型
   */
  entryTargetList: VisitTargetTypeVO[];
  /**
   * 支持的拜访渠道列表
   */
  visitChannelList: VisitChannelVO[];
  /**
   * 拜访分类列表
   */
  visitCategoryList: VisitCategoryVO[];
  /**
   * 拜访场景字段
   */
  visitSceneFieldList: VisitFieldVO[];
  /**
   * 拜访备注必填开关
   */
  visitContentSwitch: boolean;
  /**
   * 陪访开关
   */
  participateRatingSwitch: boolean;
}

/**
 * 拜访计划取消
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:02
 */
export interface VisitPlanCancelRequest extends GwBaseRequestDTO {
  /**
   * 计划id
   */
  planId: string;
}

/**
 * 拜访计划创建
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-11-10 11:02
 */
export interface VisitPlanCreateRequest extends GwBaseRequestDTO {
  /**
   * 拜访主体ID
   */
  targetId: string;
  /**
   * 拜访主体类型
   * @see VisitTargetTypeEnum
   */
  targetType: string;
  /**
   * 计划时间
   */
  gmtPlan: Date;
  /**
   * 拜访计划描述
   */
  content: string;
  /**
   * 联系人信息
   */
  contactPerson: VisitContactPersonVO;
  /**
   * 请求来源
   * CALL（电销）XY（轩辕）
   */
  source: string;
}

/**
 * 拜访计划分页查询请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-11-09 11:12
 */
export interface VisitPlanPageQueryRequest extends GwBasePageNoRequestDTO {
  /**
   * 用户id--暂只支持搜索自己
   */
  opId: string;
  /**
   * 岗位ID
   */
  jobId: string;
  /**
   * 拜访记录创建开始时间
   */
  startTime: Date;
  /**
   * 拜访记录创建结束时间
   */
  endTime: Date;
  /**
   * 经度
   */
  longitude: string;
  /**
   * 纬度
   */
  latitude: string;
  /**
   * 拜访对象ID
   */
  targetId: string;
  /**
   * 拜访对象类型
   */
  targetType: string;
  /**
   * 状态
   *  WAIT("待执行"),
   *     FINISH("已完成"),
   *     CANCEL("已取消"),
   */
  statusList: string[];
}

/**
 * 拜访计划信息vo
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-11-09 11:37
 */
export interface VisitPlanInfoVO extends ToString {
  /**
   * 拜访计划id
   */
  planId: string;
  /**
   * 关联拜访记录id
   */
  visitRecordId: string;
  /**
   * 距离：单位米-当前位置到主体
   */
  distance: string;
  /**
   * 拜访计划时间
   */
  gmtPlan: string;
  /**
   * 拜访计划描述
   */
  memo: string;
  /**
   * 拜访计划状态 WAIT("待执行"), FINISH("已完成"), CANCEL("已取消"),
   */
  status: string;
  /**
   * 最近拜访时间
   */
  lastVisitTime: string;
  /**
   * 当前用户是否可取消该拜访计划
   */
  canCancel: boolean;
  /**
   * 当前用户是否可记拜访
   */
  canRecordVisit: boolean;
  /**
   * 拜访主体
   */
  targetInfo: VisitTargetVO;
  /**
   * 联系人信息
   */
  contactPerson: VisitContactPersonVO;
  /**
   * 当前拜访人信息
   */
  visitUserInfo: VisitUserInfoVO;
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordCreateRequest.java, v 0.1 2023年09月20日 16:27 荒丘 Exp $
 */
export interface VisitRecordCreateRequest extends BaseRequest {
  /**
   * 拜访主体ID
   */
  targetId: string;
  /**
   * 拜访主体类型
   * @see VisitTargetTypeEnum
   */
  targetType: string;
  /**
   * 拜访配置ID
   */
  configId: string;
  /**
   * 拜访配置版本号
   */
  configVersion: string;
  /**
   * toolkit相关-业务场景枚举
   */
  bizScene: string;
  /**
   * 请求来源
   * CALL（电销）XY（轩辕）
   */
  source: string;
  /**
   * 拜访方式
   * @see VisitChannelEnum
   */
  visitChannel: string;
  /**
   * 经度，上门拜访才需要传递
   */
  longitude: string;
  /**
   * 纬度，上门拜访才需要传递
   */
  latitude: string;
  /**
   * 创建拜访记录时
   * 所在位置距客户的距离（米）
   */
  distance: number;
  /**
   * 通话记录ID
   */
  callRecordId: string;
  /**
   * 小号绑定记录ID
   * 电话邀约场景,通过外呼记录ID创建拜访时传递
   */
  shortNumbBindRecordId: string;
  /**
   * 指定拜访计划Id
   */
  fromVisitPlanId: string;
  /**
   * 是否电话补录场景 channel只能传 ON_OTHER
   */
  callReplenishFlag: boolean;
  /**
   * 拜访记录具体填写的内容
   */
  inputInfo: VisitRecordInputVO;
  callCreateScene: boolean;
  outCall: boolean;
  telInvitationCreateScene: boolean;
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordCreateResultVO.java, v 0.1 2023年10月18日 19:11 荒丘 Exp $
 */
export interface VisitRecordCreateResultVO extends ToString {
  visitRecordId: string;
  /**
   * 拜访计划ID
   */
  visitPlanId: string;
  /**
   * 拜访计划ID
   */
  visitParticipantRecordIdList: string[];
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordModifyRequest.java, v 0.1 2023年09月20日 16:27 荒丘 Exp $
 */
export interface VisitRecordModifyRequest extends BaseRequest {
  /**
   * 拜访记录ID
   */
  visitRecordId: string;
  /**
   * 拜访记录具体填写的内容
   */
  inputInfo: VisitRecordInputVO;
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordModifyResultVO.java, v 0.1 2023年11月30日 21:24 荒丘 Exp $
 */
export interface VisitRecordModifyResultVO extends ToString {
  visitRecordId: string;
  /**
   * 拜访计划ID
   */
  visitParticipantRecordIdList: string[];
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordPrepareRequest.java, v 0.1 2023年09月20日 16:26 荒丘 Exp $
 */
export interface VisitRecordPrepareRequest extends BaseRequest {
  /**
   * 拜访主体ID
   */
  targetId: string;
  /**
   * 拜访主体类型
   */
  targetType: string;
  /**
   * 拜访记录ID
   */
  visitRecordId: string;
  /**
   * 通话记录ID
   * 电销场景,通过外呼记录ID创建拜访时传递
   */
  callRecordId: string;
  /**
   * 小号绑定记录ID
   * 电话邀约场景,通过外呼记录ID创建拜访时传递
   */
  shortNumbBindRecordId: string;
  /**
   * 拜访配置ID
   */
  configId: string;
  /**
   * 拜访配置版本号
   */
  configVersion: string;
  /**
   * 请求来源
   * CALL（电销）XY（轩辕）
   */
  source: string;
  /**
   * 是否电话补录场景 channel只能传 ON_OTHER
   */
  callReplenishFlag: boolean;
  /**
   * toolkit相关-业务场景枚举
   * @see ToolkitBaseBizSceneEnum
   */
  bizScene: string;
  /**
   * 是否编辑场景
   * @return
   */
  editScene: boolean;
  callCreateScene: boolean;
  telInvitationCreateScene: boolean;
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordPrepareResultVO.java, v 0.1 2023年10月08日 16:25 荒丘 Exp $
 */
export interface VisitRecordPrepareResultVO extends ToString {
  /**
   * 拜访主体信息
   */
  visitTarget: VisitTargetVO;
  /**
   * 调用KP接口的参数
   */
  contactPersonEntityId: string;
  contactPersonEntityType: string;
  /**
   * 如果传入了指定的拜访记录ID,则下发拜访记录里面的联系人信息
   */
  contactPerson: VisitContactPersonVO;
  /**
   * 使用的拜访配置信息
   */
  visitConfig: VisitConfigVO;
  /**
   * 电销专用,小二手动选择的外呼拜访结果
   */
  topLevelItemList: VisitTopLevelItemVO[];
  /**
   * 电销专用,小二根据通话自动判断的外呼拜访结果
   */
  topLevelAutoItemList: VisitTopLevelItemVO[];
  /**
   * 默认陪访人信息
   */
  defaultParticipateList: VisitParticipateUserVO[];
  /**
   * 拜访方式
   * (编辑场景的时候才会下发,是创建拜访记录是提交的拜访方式,修改时不允许修改)
   * @see VisitChannelEnum
   */
  visitChannel: string;
  /**
   * 创建拜访记录时
   * 所在位置距客户的距离（米）
   */
  distance: number;
  /**
   * 是否可修改拜访联系人信息
   * 目前电销渠道的不允许修改
   */
  canModifyCpInfo: boolean;
  /**
   * 之前填写的内容
   * (编辑场景才会有值)
   */
  inputInfo: VisitRecordInputVO;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigDecisionRequest.java, v 0.1 2023年09月20日 16:24 荒丘 Exp $
 */
export interface VisitConfigDecisionRequest extends BaseRequest {
  /**
   * 拜访主体ID
   */
  targetId: string;
  /**
   * 拜访主体类型
   */
  targetType: string;
  /**
   * 拜访记录ID
   */
  visitRecordId: string;
  /**
   * 通话记录ID
   * 电销场景,通过外呼记录ID创建拜访时传递
   */
  callRecordId: string;
  /**
   * 小号绑定记录ID
   * 电话邀约场景,通过外呼记录ID创建拜访时传递
   */
  shortNumbBindRecordId: string;
  /**
   * 请求来源
   * CALL（电销）XY（轩辕）
   * @see VisitRecordSourceEnum
   */
  source: string;
  /**
   * 拜访录入场景
   * @see VisitEntrySceneEnum
   */
  scene: string;
  /**
   * 是否编辑场景
   * @return
   */
  editScene: boolean;
  callCreateScene: boolean;
  telInvitationCreateScene: boolean;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigDecisionResultVO.java, v 0.1 2023年10月08日 14:07 荒丘 Exp $
 */
export interface VisitConfigDecisionResultVO extends ToString {
  configList: VisitConfigSimpleVO[];
}

/**
 * 拜访记录制定主体分页查询请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:12
 */
export interface VisitRecordTargetQueryRequest extends VisitRecordPageQueryRequest {}

/**
 * 拜访记录信息vo
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:37
 */
export interface VisitRecordInfoVO extends VisitRecordBaseInfoVO {
  /**
   * 拜访主体
   */
  targetInfo: VisitTargetVO;
  /**
   * 定位地址
   */
  locationAddress: string;
  /**
   * 拜访事项
   */
  visitCategoryList: string[];
  /**
   * 联系人信息
   */
  contactPerson: VisitContactPersonVO;
  /**
   * 陪访信息
   */
  visitParticipantList: VisitParticipantRecordVO[];
}

/**
 * 拜访记录分页查询请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:12
 */
export interface VisitRecordPageQueryRequest extends GwBasePageNoRequestDTO {
  /**
   * 指定拜访记录id
   */
  visitRecordId: string;
  /**
   * 岗位ID
   */
  jobId: string;
  /**
   * 拜访人ID
   */
  visitorId: string;
  /**
   * 拜访记录创建开始时间
   */
  startTime: Date;
  /**
   * 拜访记录创建结束时间
   */
  endTime: Date;
  /**
   * 拜访方式-多选
   */
  visitChannelList: string[];
  /**
   * 有无陪访，三态 true、false、null
   */
  hasParticipant: boolean;
  /**
   * 陪访人ID
   */
  participantId: string;
  /**
   * 有无陪访评价，三态 true、false、null
   */
  hasParticipantRating: boolean;
  /**
   * 拜访对象ID
   */
  targetId: string;
  /**
   * 拜访对象名称
   */
  targetName: string;
  /**
   * pid
   */
  pid: string;
  /**
   * 状态
   * CommonStatusEnum key
   */
  status: string;
  /**
   * 拜访对象类型
   */
  targetType: string;
  /**
   * 联系人是否KP，三态 true、false、null
   */
  hasKeyPerson: boolean;
  /**
   * 是否有定位预警，三态 true、false、null
   */
  hasLocationAlert: boolean;
  /**
   * 通话时长最短的秒数
   */
  callDurationMinSec: number;
  /**
   * 拜访记录关联通话记录的呼叫电话
   */
  callPhone: string;
  /**
   * 是否有录音，三态 true、false、null
   */
  hasRecording: boolean;
  /**
   * 使用滚动查询--导出时单独使用
   */
  canUseScroll: boolean;
  /**
   * targetType扩展
   * 目前仅支持 --- STORE_WITH_LEADS==传改参数的时候关联门店开店的leads
   */
  targetTypeExtend: string;
  /**
   * leads认领id
   */
  leadsClaimId: string;
  /**
   * 业务场景 list ToolkitBaseBizSceneEnum
   */
  bizSceneList: string[];
  /**
   * 来源--默认c31
   */
  sourceList: string[];
}

/**
 * 拜访-电销-意向类型-筛选项查询
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-12-14 17:46
 */
export interface VisitIntentionRequest extends BaseRequest {}

/**
 * 拜访-电销-意向类型-筛选项查询VO
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-12-14 17:46
 */
export interface IntentionOptionVO extends ToString {
  /**
   * 选项编码
   */
  code: string;
  /**
   * 选项名称
   */
  label: string;
}

/**
 * 拜访记录制定主体分页查询请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:12
 */
export interface VisitRecordCardQueryRequest extends GwBaseRequestDTO {
  /**
   * 拜访对象ID
   */
  targetId: string;
  /**
   * 拜访对象类型 LEADS_ENTITY("商机"), STORE("门店"), MERCHANT("商户"),;
   */
  targetType: string;
}

/**
 * 拜访记录卡片vo
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:37
 */
export interface VisitRecordCardVO extends ToString {
  /**
   * 7天内拜访次数
   */
  visit7DayCount: string;
  /**
   * 90天内拜访次数
   */
  visit90DayCount: string;
  /**
   * 总拜访次数
   */
  visitTotalCount: string;
  /**
   * 最后拜访时间
   */
  lastVisitTime: string;
}

/**
 * 拜访记录详情接口
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:12
 */
export interface VisitRecordDetailQueryRequest extends BaseRequest {
  /**
   * 拜访记录ID
   */
  visitRecordId: string;
  /**
   * 场景：NORMAL（通用场景，默认）、APPOINT_TARGET(指定主体)
   * VisitRecordSceneEnum
   */
  scene: string;
  /**
   * 请求来源： CALL（电销）XY（轩辕）
   * VisitRecordSourceEnum
   */
  source: string;
}

/**
 * 拜访计划详情vo
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:37
 */
export interface VisitRecordDetailVO extends ToString {
  /**
   * 拜访记录基础信息
   */
  visitRecordBaseInfoVO: VisitRecordBaseInfoVO;
  /**
   * 联系人信息
   */
  contactPerson: VisitContactPersonVO;
  /**
   * 拜访主体信息
   */
  visitTargetVO: VisitTargetVO;
  /**
   * 记录关联的分类事项
   */
  visitCategoryList: VisitRecordCategoryVO[];
  /**
   * 记录关联的拜访场景
   */
  visitSceneFieldList: VisitRecordFieldVO[];
  /**
   * 通话信息
   */
  callRecord: VisitRecordCallRecordVO;
  /**
   * 是否历史数据
   */
  historyFlag: boolean;
  /**
   * 是否同步的时候有数据缺失
   */
  historyLostData: boolean;
  /**
   * 到家侧的数据ID
   */
  historyVisitId: string;
  /**
   * 陪访信息
   */
  visitParticipantList: VisitParticipantRecordVO[];
}

/**
 * <AUTHOR>
 * @version $Id: VisitTargetDetailQueryRequest.java, v 0.1 2023年09月20日 16:21 荒丘 Exp $
 */
export interface VisitTargetDetailQueryRequest extends BaseRequest {
  /**
   * 拜访主体ID
   */
  targetId: string;
  /**
   * 拜访主体类型
   */
  targetType: string;
  /**
   * 经度
   */
  longitude: string;
  /**
   * 纬度
   */
  latitude: string;
}

/**
 * 主体信息
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-22 14:38
 */
export interface VisitTargetVO extends ToString {
  /**
   * 主体ID
   */
  targetId: string;
  /**
   * 主体类型
   */
  targetType: string;
  /**
   * pid
   */
  pid: string;
  /**
   * 主体类型文案
   */
  targetTypeDesc: string;
  /**
   * 主体名称
   */
  targetName: string;
  /**
   * 省，
   */
  provinceName: string;
  /**
   * 市，
   */
  cityName: string;
  /**
   * 区，
   */
  districtName: string;
  /**
   * 地址，
   */
  address: string;
  /**
   * 经度，可能为null
   */
  longitude: string;
  /**
   * 维度，可能为null
   */
  latitude: string;
  /**
   * 扩展字段
   */
  extInfo: { [index: string]: any };
  /**
   * 距离--单位米
   */
  distance: string;
  /**
   * 商户维度 冗余商户的主店名
   */
  mainStoreName: string;
  key: string;
}

/**
 * <AUTHOR>
 * @date 2023/07/03
 */
export interface SmartWordTemplateInfoSaveRequest extends BaseRequest {
  /**
   * 话术内容
   */
  smartWordTemplateInfoDTO: SmartWordTemplateInfoDTO;
}

/**
 * Description:
 * Author: xuelin
 * Date: 2023/12/12 14:47
 */
export interface SmartWordTemplateInfoDisableRequest extends BaseRequest {
  /**
   * 规则id
   */
  bizId: string;
}

/**
 * Description:
 * Author: xuelin
 * Date: 2023/12/12 14:45
 */
export interface SmartWordTemplateInfoEnableRequest extends BaseRequest {
  /**
   * 规则id
   */
  bizId: string;
}

/**
 * <AUTHOR>
 * @date 2023/07/03
 */
export interface SmartWordTemplateInfoDetailQueryRequest extends BaseRequest {
  /**
   * 主键id
   */
  id: number;
  /**
   * 规则id
   */
  bizId: string;
}

export interface SmartWordTemplateInfoDTO extends ToString {
  /**
   * 主键id
   */
  id: number;
  /**
   * 规则id
   */
  bizId: string;
  /**
   * 业务类型
   */
  bizType: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 类目
   */
  categories: string[];
  /**
   * 类目中文
   */
  categoriesName: string[];
  /**
   * 状态
   */
  status: string;
  /**
   * 模式
   */
  mode: string[];
  /**
   * 环境
   */
  env: string;
  /**
   * 是否历史
   */
  history: string;
  /**
   * 创建时间
   */
  gmtCreate: Date;
  /**
   * 修改时间
   */
  gmtModified: Date;
  /**
   * 生效平台
   */
  platforms: string[];
  /**
   * 创建人
   */
  creator: string;
  /**
   * 修改人
   */
  updator: string;
  /**
   * 限制
   */
  maxLimit: number;
  /**
   * 条件组
   */
  conditionGroupDTOS: ConditionGroupDTO[];
  /**
   * 规则创建时间
   */
  gmtRule: Date;
}

export interface SmartWordIndicatorConditionQueryRequest extends BaseRequest {
  /**
   * 业务类型 {@link com.alsc.kbt.toolkit.smartword.enums.SmartWordTemplateCodeEnum}
   */
  bizType: string;
  /**
   * 指标类型 {@link com.alsc.kbt.toolkit.smartword.enums.IndicatorTypeEnum}
   */
  indicatorType: string;
}

export interface IndicatorDTO extends ToString {
  /**
   * 指标code
   */
  code: string;
  /**
   * 指标名
   */
  name: string;
  /**
   * 公式 {@link FormulasEnums}
   */
  formulas: FormulasDTO[];
  /**
   * 选项
   */
  selectOptions: IndicatorOption[];
  /**
   * 单位
   */
  unit: string;
  /**
   * 指标类型{@link IndicatorTypeEnum}
   */
  types: string[];
  /**
   * 指标选项
   */
  indicatorOptions: IndicatorOption[];
}

/**
 * <AUTHOR>
 * @date 2023/12/11
 */
export interface SmartWordGroupRuleConfigQueryRequest extends BaseRequest {
  /**
   * Getter method for property <tt>bizType</tt>.
   * @return property value of bizType
   */
  bizType: string;
}

export interface RuleGroupConfigDTO extends ToString {
  /**
   * 群组code
   */
  groupCode: string;
  /**
   * 群组名称
   */
  groupName: string;
  /**
   * 规则配置
   */
  ruleConfigDTOS: RuleConfigDTO[];
  /**
   * 是否设置
   */
  configList: RuleValueConfigDTO[];
  /**
   * 快速全选
   */
  fastSelectAll: RuleValueConfigDTO;
}

/**
 * Description:
 * Author: xuelin
 * Date: 2023/12/12 15:19
 */
export interface SmartWordTemplateInfoQueryRequest extends BasePageNoQueryRequest {
  queryCond: SmartWordTemplateInfoQueryCond;
}

/**
 * 陪访记录评价请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-11-09 14:24
 */
export interface VisitParticipantRecordAppraiseRequest extends GwBaseRequestDTO {
  /**
   * 评价id
   */
  visitParticipantRecordId: string;
  /**
   * 评价内容，不可为空
   */
  content: string;
  /**
   * 经度
   */
  longitude: string;
  /**
   * 纬度
   */
  latitude: string;
}

/**
 * 陪访记录分页查询请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-11-09 11:12
 */
export interface VisitParticipantRecordPageQueryRequest extends VisitRecordPageQueryRequest {
  /**
   * 陪访人岗位id
   */
  participantJobId: string;
}

/**
 * 陪访记录评价请求
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-11-09 14:24
 */
export interface VisitParticipantRecordDetailQueryRequest extends GwBaseRequestDTO {
  /**
   * 评价id
   */
  visitParticipantRecordId: string;
  /**
   * 请求来源： CALL（电销）XY（轩辕）
   * VisitRecordSourceEnum
   */
  source: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallTargetCountRequest.java, v 0.1 2023年08月25日 11:20 荒丘 Exp $
 */
export interface CallTargetCountRequest extends GwBaseRequestDTO {
  /**
   * 业务场景
   * @see ToolkitBaseBizSceneEnum
   */
  bizScene: string;
  /**
   * 计数场景
   * @see CallTargetCountQuerySceneEnum
   */
  countSceneList: string[];
}

/**
 * <AUTHOR>
 * @version $Id: CallTargetCountVO.java, v 0.1 2023年08月25日 11:21 荒丘 Exp $
 */
export interface CallTargetCountVO extends ToString {
  /**
   * 计数场景
   * @see CallTargetCountQuerySceneEnum
   */
  countScene: string;
  /**
   * 数据总量
   */
  totalCount: number;
  /**
   * 扩展字段
   */
  extInfo: { [index: string]: any };
}

/**
 * <AUTHOR>
 * @version $Id: CallTargetPageQueryRequest.java, v 0.1 2023年08月16日 11:11 荒丘 Exp $
 */
export interface CallTargetPageQueryRequest extends GwBasePageNoRequestDTO {
  /**
   * 业务场景
   * @see ToolkitBaseBizSceneEnum
   */
  bizScene: string;
  /**
   * 我的商户：MY_PARTNER
   * 拨打列表：DIAL_LIST
   * 拜访列表：VISIT_PLAN_LIST
   * @see CallTargetQueryTypeEnum
   */
  queryType: string;
  /**
   * 过滤分类
   * LEADS_ENTITY_PRIVATE("【Leads】私海leads"),
   * LEADS_ENTITY_MIX_OPERATION("【Leads】混业门店leads"),
   * SHOP_AGENT_OPERATION("【门店】代运营归属"),
   * SHOP_ITEM_SALE("【门店】商品归属"),
   *
   * SHOP_ADVERTISE_OPERATE("【门店】广告运营归属"),
   * @see CallTargetFilterTypeEnum
   */
  filterType: string;
  /**
   * 查询参数
   * @see CallTargetQueryParamKeyEnum
   * 支持的key:
   * shopId,门店ID,可选,精确匹配,只在【门店】分类下生效
   * shopName,门店名称,可选,模糊匹配,只在【门店】分类下生效
   * kbPid,商户ID,可选,精确匹配,只在【门店】分类下生效
   * leadsEntityId,商机ID,可选,精确匹配,只在【精确匹配】分类下生效
   */
  queryParam: { [index: string]: string };
  /**
   * 我的商户：MY_PARTNER
   * 拨打列表：DIAL_LIST
   * 拜访列表：VISIT_PLAN_LIST
   * @see CallTargetSortTypeEnum
   */
  sortType: string;
  /**
   * 最后一次拨打结果
   */
  lastCallResult: string;
}

/**
 * "targetId":"", // 通话主体ID:商机ID
 * "targetSubId":"", // 通话主体二级ID：poiId
 * "targetName":"", // 通话主体名称：商机的店铺名称
 * "toPhoneShield":"", // 被叫号码（脱敏）
 * <AUTHOR>
 * @version $Id: CallTargetVO.java, v 0.1 2023年05月29日 19:12 荒丘 Exp $
 */
export interface CallTargetVO extends ToString {
  /**
   * 通话主体ID
   */
  targetId: string;
  /**
   * 通话主体二级ID
   */
  targetSubId: string;
  /**
   * 主体类型
   */
  targetType: string;
  /**
   * 通话主体名称
   */
  targetName: string;
  /**
   * 被叫号码（脱敏）
   */
  toPhoneShield: string;
  /**
   * 省名称
   */
  provinceName: string;
  /**
   * 城市名称
   */
  cityName: string;
  /**
   * 区名称
   */
  districtName: string;
  /**
   * 地址
   */
  address: string;
  /**
   * 完整地址地址 queryBlockInfo接口使用
   */
  fullAddress: string;
  /**
   * 图片
   */
  picture: string;
  /**
   * 是否可选择
   */
  canSelect: boolean;
  /**
   * 不可选择的原因
   */
  canNotSelectReason: string;
  /**
   * 商机所属范围
   * 公海\私海
   */
  leadsRange: string;
  /**
   * 被私海认领时,领地ID
   */
  privateTerritoryId: string;
  /**
   * 在公海里面时,当前小二可认领的领地ID
   * (粗略通过商机所在领地列表+小二可作业范围领地列表做交集获取)
   */
  publicTerritoryList: CallTerritoryVO[];
  /**
   * 扩展字段
   */
  extInfo: { [index: string]: any };
}

/**
 * <AUTHOR>
 * @version $Id: CallTargetContactPersonQueryRequest.java, v 0.1 2023年08月25日 15:27 荒丘 Exp $
 */
export interface CallTargetContactPersonQueryRequest extends GwBaseRequestDTO {
  /**
   * 呼叫主体ID
   * 商机ID 或 店铺ID
   */
  targetId: string;
  /**
   * 呼叫主体类型
   *
   * 商机
   * @see CallTargetTypeEnum#LEADS_ENTITY
   *
   * * @see CallTargetTypeEnum#STORE
   */
  targetType: string;
  /**
   * 业务场景，透传到kp侧；默认 TEL_VISIT
   */
  scene: string;
}

/**
 * 联系信息VO
 * <AUTHOR>
 * @version $Id: CallTargetContactInfoVO.java, v 0.1 2023年08月25日 15:32 荒丘 Exp $
 */
export interface CallTargetContactInfoVO extends ToString {
  /**
   * 联系方式ID
   */
  contactId: string;
  /**
   * 主联系方式
   */
  mainContactWay: CallContactWayVO;
  /**
   * 其他联系方式
   */
  otherContactWayList: CallContactWayVO[];
  /**
   * 联系人ID
   */
  contactPersonId: string;
  /**
   * 联系人名称
   */
  contactPersonName: string;
  /**
   * 是否关键人
   */
  kpFlag: boolean;
  /**
   * 是否可编辑
   */
  editable: boolean;
  /**
   * 职务
   */
  roleDesc: string;
  /**
   * 联系人属性
   */
  attribute: CallTargetContactAttributeVO;
  /**
   * 联系方式统计指标
   */
  statistics: CallTargetContactInfoStatisticsVO;
  /**
   * 扩展字段
   */
  extInfo: { [index: string]: any };
}

/**
 * <AUTHOR>
 * @version $Id: CallTargetPageQueryRequest.java, v 0.1 2023年08月16日 11:11 荒丘 Exp $
 */
export interface CallTargetSearchByPhoneRequest extends GwBasePageNoRequestDTO {
  /**
   * 主体分类
   * SHOP_BY_PHONE("【门店】基于手机号召回"),
   * LEADS_ENTITY_BY_PHONE("【商机】基于手机号召回"),;
   * @see CallTargetFilterTypeEnum
   */
  filterType: string;
  /**
   * 外呼电话
   */
  phone: string;
}

/**
 * <AUTHOR>
 * @date 2023/08/16
 */
export interface CallRecordSaveRequest extends ToString {
  /**
   * Getter method for property <tt>contactId</tt>.
   * @return property value of contactId
   */
  contactId: string;
  /**
   * Getter method for property <tt>instanceId</tt>.
   * @return property value of instanceId
   */
  instanceId: string;
  /**
   * Getter method for property <tt>targetId</tt>.
   * @return property value of targetId
   */
  targetId: string;
  /**
   * Getter method for property <tt>kpContactId</tt>.
   * @return property value of kpContactId
   */
  kpContactId: string;
  /**
   * Getter method for property <tt>targetType</tt>.
   * @return property value of targetType
   */
  targetType: string;
}

/**
 * <AUTHOR>
 * @date 2023/08/16
 */
export interface CallRecordMissingVisitQueryRequest extends BaseRequest {
  /**
   * Getter method for property <tt>instanceId</tt>.
   * @return property value of instanceId
   */
  instanceId: string;
  /**
   * Getter method for property <tt>bizScene</tt>.
   * @return property value of bizScene
   */
  bizScene: string;
}

/**
 * <AUTHOR>
 * @date 2023/08/16
 */
export interface CallRecordStatsInfoQueryRequest extends BaseRequest {
  /**
   * Getter method for property <tt>startTime</tt>.
   * @return property value of startTime
   */
  startTime: Date;
  /**
   * Getter method for property <tt>endTime</tt>.
   * @return property value of endTime
   */
  endTime: Date;
  /**
   * Getter method for property <tt>instanceId</tt>.
   * @return property value of instanceId
   */
  instanceId: string;
}

/**
 * 通话数据
 */
export interface CallRecordStatsInfoVO extends ToString {
  /**
   * Getter method for property <tt>dialCount</tt>.
   * @return property value of dialCount
   */
  dialCount: number;
  /**
   * Getter method for property <tt>validCount</tt>.
   * @return property value of validCount
   */
  validCount: number;
  /**
   * Getter method for property <tt>validDistinctTargetCount</tt>.
   * @return property value of validDistinctTargetCount
   */
  validDistinctTargetCount: number;
  /**
   * Getter method for property <tt>callOutTime</tt>.
   * @return property value of callOutTime
   */
  callOutTime: number;
  /**
   * Getter method for property <tt>callInCount</tt>.
   * @return property value of callInCount
   */
  callInCount: number;
  /**
   * Getter method for property <tt>callInTime</tt>.
   * @return property value of callInTime
   */
  callInTime: number;
}

/**
 * 查询通话记录--未关联拜访记录
 * <AUTHOR>
 * @version v 0.1
 * @classname CallRecordMissingVisitQueryRequest
 * @date 2024-01-09 17:16
 */
export interface CallRecordMissVisitQueryRequest extends BaseRequest {
  /**
   * 呼叫主体ID
   */
  targetId: string;
  /**
   * 呼叫主体类型 -- 默认leads
   * @see CallTargetTypeEnum
   */
  targetType: string;
  /**
   * 用户bumngid
   */
  bumngId: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallRecordExistQueryRequest.java, v 0.1 2023年06月07日 12:10 荒丘 Exp $
 */
export interface CallRecordExistQueryResultVO extends ToStringbd7e46 {
  /**
   * 业务场景
   * @see com.alsc.kbt.toolkit.enums.call.CallRecordBizSceneEnum
   */
  bizScene: string;
  /**
   * 是否有记录需要填写拜访
   */
  hasRecordNeedAddVisit: boolean;
  /**
   * 通话记录id
   * 隐私小号场景是隐私小号绑定记录的id
   * 外呼场景是通话记录id
   */
  callBizId: string;
}

/**
 * 用户实例查询请求
 * <AUTHOR>
 * @date 2023/08/16
 */
export interface CallUserInstanceQueryRequest extends BaseRequest {}

/**
 * <AUTHOR>
 * @date 2023/08/14
 */
export interface CallInstanceInfoVO extends ToString {
  /**
   * Getter method for property <tt>instanceId</tt>.
   * @return property value of instanceId
   */
  instanceId: string;
  /**
   * Getter method for property <tt>name</tt>.
   * @return property value of name
   */
  name: string;
  /**
   * Getter method for property <tt>description</tt>.
   * @return property value of description
   */
  description: string;
  /**
   * Getter method for property <tt>status</tt>.
   * @return property value of status
   */
  status: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallUseLaunchSurveyToReleaseCallRequest.java, v 0.1 2024年02月29日 19:24 荒丘 Exp $
 */
export interface CallUseLaunchSurveyToReleaseCallRequest extends BaseRequest {
  /**
   * 实例id
   */
  instanceId: string;
  /**
   * 被叫电话
   */
  toPhone: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallUseLaunchSurveyToReleaseCallVO.java, v 0.1 2024年02月29日 19:26 荒丘 Exp $
 */
export interface CallUseLaunchSurveyToReleaseCallVO extends ToString {
  useLaunchSurveyToReleaseCall: boolean;
}

/**
 * <AUTHOR>
 * @version $Id: OutCallPrepareRequest.java, v 0.1 2024年01月24日 10:58 荒丘 Exp $
 */
export interface OutCallPrepareRequest extends BaseRequest {
  /**
   * 业务场景
   * @see ToolkitBaseBizSceneEnum
   */
  bizScene: string;
}

/**
 * <AUTHOR>
 * @version $Id: OutCallPrepareResultVO.java, v 0.1 2024年01月24日 10:59 荒丘 Exp $
 */
export interface OutCallPrepareResultVO extends ToString {
  bumngId: string;
  /**
   * echo平台需要的登录token
   */
  echoToken: string;
}

/**
 * 外呼阶段推进请求模型
 * <AUTHOR>
 * @date 2023/10/17
 */
export interface OutCallProgressRequest extends GwBaseRequestDTO {
  /**
   * 通话记录ID
   */
  recordId: string;
  /**
   * 通话事件类型
   * @see CallRecordEventTypeEnum
   */
  progressEvent: string;
  /**
   * 渠道通话记录ID
   */
  channelRecordId: string;
}

/**
 * 外呼阶段推进结果
 * <AUTHOR>
 * @date 2023/10/17
 */
export interface OutCallProgressResultVO extends ToString {}

/**
 * 电话区块查询接口
 * <AUTHOR>
 * @version $Id: OutCallStartRequest.java, v 0.1 2023年05月17日 17:56 荒丘 Exp $
 */
export interface CallBlockQueryRequest extends GwBaseSignSepExtendRequestDTO {
  /**
   * 呼叫主体ID
   * 商机ID
   */
  targetId: string;
  /**
   * 呼叫主体类型
   *
   * 商机
   * @see CallTargetTypeEnum#LEADS_ENTITY
   * @see CallTargetTypeEnum#AMAP_SHOP
   */
  targetType: string;
  /**
   * 展业类型
   * @see CallBusinessOperateTypeEnum
   */
  businessOperateType: string;
  /**
   * 业务场景
   * @see ToolkitBaseBizSceneEnum
   */
  bizScene: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallBlockInfoVO.java, v 0.1 2023年05月31日 22:23 荒丘 Exp $
 */
export interface CallBlockInfoVO extends ToString {
  /**
   * 当前要外呼的渠道
   * @see CallChannelEnum
   */
  channel: string;
  callTarget: CallTargetVO;
  bdCallConfig: BdCallConfigVO;
  bumngId: string;
  /**
   * echo平台需要的登录token
   */
  echoToken: string;
}

/**
 * 外呼座席管理请求
 * <AUTHOR>
 * @version : CallAgentManageRequest.java, v 0.1 2023年10月17日 下午5:38 LiShu Exp $
 */
export interface CallAgentManageRequest extends GwBaseRequestDTO {
  /**
   * Getter method for property <tt>channel</tt>.
   * @return property value of channel
   */
  channel: string;
}

/**
 * 外呼座席展示模型
 * <AUTHOR>
 * @version : CallAgentVO.java, v 0.1 2023年10月17日 下午5:43 LiShu Exp $
 */
export interface CallAgentVO extends ToString {
  /**
   * Getter method for property <tt>channel</tt>.
   * @return property value of channel
   */
  channel: string;
  /**
   * Getter method for property <tt>accountNo</tt>.
   * @return property value of accountNo
   */
  accountNo: string;
  /**
   * Getter method for property <tt>password</tt>.
   * @return property value of password
   */
  password: string;
  /**
   * Getter method for property <tt>agentInfoId</tt>.
   * @return property value of agentInfoId
   */
  agentInfoId: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallRecordQueryRequest.java, v 0.1 2023年05月29日 11:22 荒丘 Exp $
 */
export interface CallRecordQueryRequest extends GwBaseRequestDTO {
  /**
   * 呼叫记录ID
   */
  recordId: string;
}

/**
 * 外呼记录简单信息VO
 *
 * "recordId":"外呼记录ID",
 * "status":"RINGING",  //
 * "statusText":"振铃中",  //
 * "needAutoRefresh":true, // 是否需要自动刷新信息（通话记录未到到终态时，前端需要定时调用本接口，刷新通话信息）
 * "handleStatus":"YES", // 接通状态 ： YES NO
 * "handleDurationSec":null, // 接通时长，单位：秒  未接通时为 null
 * "failedReason":"被叫用户是黑名单" // 失败原因：调用下游发起外呼接口失败时的失败原因，
 * <AUTHOR>
 * @version $Id: CallRecordSimpleVO.java, v 0.1 2023年05月25日 15:35 荒丘 Exp $
 */
export interface CallRecordSimpleVO extends ToString {
  /**
   * 呼叫记录ID
   */
  recordId: string;
  /**
   * 呼叫记录状态
   */
  status: string;
  statusText: string;
  /**
   * 是否需要持续刷新
   */
  needAutoRefresh: boolean;
  /**
   * 是否接通
   */
  handleStatus: string;
  /**
   * 通话时长
   */
  handleDurationSec: number;
  /**
   * 失败原因
   */
  failedReason: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallRecordDetailQueryRequest.java, v 0.1 2024年02月22日 02:21 荒丘 Exp $
 */
export interface CallRecordDetailQueryRequest extends GwBaseRequestDTO {
  /**
   * 呼叫记录ID
   */
  recordId: string;
  /**
   * 查询选项
   * withRecText
   */
  options: string[];
}

/**
 * 外呼记录简单信息VO
 *
 * "recordId":"外呼记录ID",
 * "status":"RINGING",  //
 * "statusText":"振铃中",  //
 * "needAutoRefresh":true, // 是否需要自动刷新信息（通话记录未到到终态时，前端需要定时调用本接口，刷新通话信息）
 * "handleStatus":"YES", // 接通状态 ： YES NO
 * "handleDurationSec":null, // 接通时长，单位：秒  未接通时为 null
 * "failedReason":"被叫用户是黑名单" // 失败原因：调用下游发起外呼接口失败时的失败原因，
 * <AUTHOR>
 * @version $Id: CallRecordSimpleVO.java, v 0.1 2023年05月25日 15:35 荒丘 Exp $
 */
export interface CallRecordDetailVO extends ToString {
  /**
   * 呼叫记录ID
   */
  recordId: string;
  /**
   * 录音解析之后的文本[内部录音分析的]
   */
  recTextFromAI: string;
}

/**
 * 外呼附加活动请求
 * Description:
 * Author: xuelin
 * Date: 2024/1/23 14:20
 */
export interface OutCallAdditionalActivityRequest extends GwBaseRequestDTO {
  /**
   * 呼叫记录ID
   */
  recordId: string;
  /**
   * 呼叫主体ID
   * 商机ID
   */
  targetId: string;
  /**
   * 呼叫主体类型
   * 商机
   * @see CallTargetTypeEnum#LEADS_ENTITY
   */
  targetType: string;
  /**
   * 上报动作类型
   * @see CallAdditionalActivityActionTypeEnum
   */
  actionType: string;
  /**
   * 动作对应的参数
   */
  actionParam: { [index: string]: any };
}

/**
 * <AUTHOR>
 * @version $Id: OutCallResultReportRequest.java, v 0.1 2023年05月17日 19:57 荒丘 Exp $
 */
export interface OutCallResultReportRequest extends GwBaseSignSepExtendRequestDTO {
  /**
   * 呼叫记录ID
   */
  recordId: string;
  /**
   * 呼叫主体ID
   * 商机ID
   */
  targetId: string;
  /**
   * 呼叫主体类型
   *
   * 商机
   * @see CallTargetTypeEnum#LEADS_ENTITY
   */
  targetType: string;
  /**
   * 展业记录内容(拜访结果内容)
   */
  visitContent: string;
  /**
   * 上报动作类型
   * @see CallResultReportActionTypeEnum
   */
  actionType: string;
  /**
   * 动作对应的参数
   */
  actionParam: { [index: string]: any };
}

/**
 * <AUTHOR>
 * @version $Id: BdSalesPhoneSaveRequest.java, v 0.1 2023年06月16日 11:10 荒丘 Exp $
 */
export interface BdSalesPhoneSaveRequest extends GwBaseRequestDTO {
  /**
   * 电话号码不能为空
   */
  phone: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallRecordSearchRequest.java, v 0.1 2023年06月02日 11:31 荒丘 Exp $
 */
export interface CallRecordSearchRequest extends GwBasePageNoRequestDTO {
  /**
   * 呼叫主体ID
   */
  targetId: string;
  /**
   * 呼叫主体二级ID
   */
  targetSubId: string;
  /**
   * 外呼小二ID归属公司ID
   */
  companyId: string;
  /**
   * 外呼小二ID
   */
  operatorId: string;
  /**
   * 展业类型
   */
  businessOperateType: string;
  /**
   * 通话时长筛选下限
   */
  handleDurationSecMin: number;
  /**
   * 通话时长筛选上限
   */
  handleDurationSecMax: number;
  /**
   * 被叫号码
   */
  toPhone: string;
  /**
   * 业务场景
   * @see ToolkitBaseBizSceneEnum
   */
  bizScene: string;
  /**
   * 通话记录查询开始时间【新增】
   * 不传默认取值当前时间-7天 ，并且开始结束时间跨度不能超过3个月
   * 格式：yyyy-MM-dd hh:mm:ss
   */
  startDateTime: string;
  /**
   * 通话记录查询结束时间【新增】
   * 不传默认取值当前时间 ，并且开始结束时间跨度不能超过3个月
   * 格式：yyyy-MM-dd hh:mm:ss
   */
  endDateTime: string;
  /**
   * 录音语气标签
   */
  recToneTag: string;
  /**
   * 录音话术匹配度标签
   */
  recSmartWordTag: string;
  /**
   * 小记质量标签
   */
  visitQualityTag: string;
}

/**
 * <AUTHOR>
 * @version $Id: CalRecordSearchListEleVO.java, v 0.1 2023年06月02日 11:34 荒丘 Exp $
 */
export interface CalRecordSearchListEleVO extends ToString {
  recordId: string;
  targetId: string;
  targetSubId: string;
  targetName: string;
  /**
   * 电话号码脱敏
   * @deprecated
   */
  toPhone: string;
  toPhoneShield: string;
  companyId: string;
  companyName: string;
  operatorId: string;
  operatorName: string;
  dialingTime: string;
  handleDurationSec: number;
  businessOperateType: string;
  businessOperateTypeText: string;
  actionList: CallActionVO[];
  /**
   * 录音语气标签
   */
  recToneTag: string;
  recToneTagText: string;
  /**
   * 录音话术匹配度标签
   */
  recSmartWordTag: string;
  recSmartWordTagText: string;
  recSmartWordTagReason: string;
  /**
   * 小记质量标签
   */
  visitQualityTag: string;
  visitQualityTagDesc: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallTargetInfoSearchRequest.java, v 0.1 2023年06月02日 11:32 荒丘 Exp $
 */
export interface CallTargetInfoSearchRequest extends GwBasePageNoRequestDTO {
  /**
   * 呼叫主体类型
   */
  targetType: string;
  /**
   * 呼叫主体名称
   */
  targetName: string;
}

/**
 * <AUTHOR>
 * @version $Id: OutCallStartRequest.java, v 0.1 2023年05月17日 17:56 荒丘 Exp $
 */
export interface OutCallStartRequest extends GwBaseSignSepExtendRequestDTO {
  /**
   * 呼叫主体ID
   * 商机ID
   */
  targetId: string;
  /**
   * 呼叫主体类型
   * 商机
   * @see CallTargetTypeEnum#LEADS_ENTITY
   */
  targetType: string;
  /**
   * 坐席ID
   */
  agentInfoId: string;
  /**
   * 展业类型
   * @see CallBusinessOperateTypeEnum
   */
  businessOperateType: string;
  /**
   * 主叫电话号码
   */
  fromPhone: string;
  /**
   * 被叫电话号码
   */
  toPhone: string;
  /**
   * 外呼渠道,备用的,之后接入读多家ISV的时候,可以供前端指定使用的渠道,本期不会使用
   * @see CallChannelEnum#getCode()
   */
  channel: string;
  /**
   * 业务场景
   * @see ToolkitBaseBizSceneEnum
   */
  bizScene: string;
  /**
   * KP联系人id
   */
  kpContactId: string;
}

/**
 * <AUTHOR>
 * @version $Id: OutCallStartResultVO.java, v 0.1 2023年05月24日 17:22 荒丘 Exp $
 */
export interface OutCallStartResultVO extends ToString {
  /**
   * 呼叫记录ID，逻辑主键
   */
  recordId: string;
  /**
   * 本次外呼使用的渠道
   */
  channel: string;
}

/**
 * <AUTHOR>
 * @version $Id: OutCallResultSubmitOrderRequest.java, v 0.1 2023年06月14日 11:41 荒丘 Exp $
 */
export interface OutCallResultSubmitOrderRequest extends GwBaseSignSepExtendRequestDTO {
  /**
   * 呼叫记录ID
   */
  recordId: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallBindRecordReportRequest.java, v 0.1 2023年12月13日 16:25 荒丘 Exp $
 */
export interface CallShortNumberCancelRequest extends BaseRequest {
  /**
   * 呼叫主体ID
   */
  targetId: string;
  /**
   * 呼叫主体类型 -- 默认leads
   * @see CallTargetTypeEnum
   */
  targetType: string;
  /**
   * 业务场景-- 默认C33_APP_PHONE_CALL
   */
  scene: string;
}

export interface BaseRequest extends ToStringbd7e46 {
  requestId: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallBindRecordMissingResultVO.java, v 0.1 2023年12月13日 16:22 荒丘 Exp $
 */
export interface CallBindRecordMissingResultVO extends ToString {
  /**
   * 缺失拜访记录的绑定记录ID
   */
  bindRecordIdList: string[];
  /**
   * 绑定记录ID和对应的联系人信息map
   */
  contactMap: { [index: string]: any };
}

/**
 * <AUTHOR>
 * @version $Id: CallBindRecordReportRequest.java, v 0.1 2023年12月13日 16:25 荒丘 Exp $
 */
export interface CallBindRecordReportRequest extends BaseRequest {
  /**
   * 绑定记录ID
   */
  bindRecordId: string;
  /**
   * KP联系人id
   */
  kpContactId: string;
}

export interface Serializable {}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 * @deprecated 使用 tribe包中的
 */
export interface IResult<T> extends IErrorCode {
  succ: boolean;
  data: T;
}

export interface BindInfo extends Serializable {
  /**
   * 绑定主体id
   */
  bindTargetId: string;
  /**
   * 绑定主体名称
   */
  bindTargetName: string;
  /**
   * 绑定主体类型
   */
  bindTargetType: string;
  /**
   * 绑定子主体编号
   */
  bindTargetValue: string;
  /**
   * 码token列表
   */
  qrCodes: { [index: string]: string }[];
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/14
 */
export interface AlarmSecondaryCategoryCountResponse extends Serializable {
  /**
   * 一级分类
   */
  parentCategoryId: number;
  /**
   * 二级分类
   */
  secondaryCategoryId: number;
  /**
   * 数量
   */
  totalCount: number;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/07
 */
export interface BasePageQuery extends BaseDO {
  pageSize: number;
  pageNum: number;
  offset: number;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/14
 */
export interface ShopInfoResponse extends Serializable {
  /**
   * 门店id
   */
  shopId: string;
  /**
   * 门店编号
   */
  shopIdStr: string;
  /**
   * 门店名称
   */
  shopName: string;
  /**
   * 门店图片地址
   */
  imgUrl: string;
  /**
   * 门店距离
   */
  distance: string;
  /**
   * 门店地址
   */
  address: string;
  /**
   * 门店状态
   */
  runningStatus: number;
  /**
   * 门店标签
   */
  labelList: ShopLabelResponse[];
  /**
   * 预警合作类型
   */
  todoCooperationEnum: string;
  /**
   * 二级分类
   */
  secondaryShopLevel: number;
  /**
   * 质量分层
   */
  highQuality: number;
  /**
   * TODO:
   */
  exclusiveCompetitor: number;
  /**
   * 商户id
   */
  merchantPid: number;
  /**
   * 商户名称
   */
  merchantName: string;
  /**
   * 品牌名称
   */
  bdNames: string;
}

export interface FieldDTO {
  fileName: string;
  fileId: string;
  resourceId: string;
  mem: string;
}

/**
 * 数据模型，包装gmtCreate，gmtModified
 */
export interface DataModel extends ToString {
  /**
   * Getter method for property <tt>gmtCreate</tt>.
   * @return property value of gmtCreate
   */
  gmtCreate: Date;
  /**
   * Getter method for property <tt>gmtModified</tt>.
   * @return property value of gmtModified
   */
  gmtModified: Date;
}

/**
 * <AUTHOR>
 * @version $Id: ToString.java, v 0.1 16/4/14 下午2:28 yxl Exp $
 */
export interface ToString extends Serializable {}

/**
 * 附件对象
 * <AUTHOR>
 * @version $Id: Attachment.java, v 0.1 2016年4月18日 上午10:41:10 yxl Exp $
 */
export interface Attachment extends ToString {
  /**
   * Getter method for property <tt>fileId</tt>.
   * @return property value of fileId
   */
  fileId: number;
  /**
   * Getter method for property <tt>resourceId</tt>.
   * @return property value of resourceId
   */
  resourceId: string;
  /**
   * Getter method for property <tt>bizId</tt>.
   * @return property value of bizId
   */
  bizId: number;
  /**
   * Getter method for property <tt>bizType</tt>.
   * @return property value of bizType
   */
  bizType: string;
  /**
   * Getter method for property <tt>attaName</tt>.
   * @return property value of attaName
   */
  attaName: string;
  /**
   * Getter method for property <tt>attaDesc</tt>.
   * @return property value of attaDesc
   */
  attaDesc: string;
  /**
   * Getter method for property <tt>attaUrl</tt>.
   * @return property value of attaUrl
   */
  attaUrl: string;
  /**
   * 子类型
   */
  subBizType: string;
  /**
   * 扩展
   */
  extInfo: { [index: string]: string };
}

/**
 * 二维码申请信息
 * <AUTHOR>
 * @version $Id: ApplyStuffQrcode.java, v 0.1 2016-07-14 10:40 zhengyuan.zy Exp $$
 */
export interface ApplyStuffQrcode extends DataModel083922 {
  /**
   * Getter method for property applicant.
   * @return property value of applicant
   */
  applicant: string;
  /**
   * Getter method for property applicantId.
   * @return property value of applicantId
   */
  applicantId: string;
  /**
   * Getter method for property applicantType.
   * @return property value of applicantType
   */
  applicantType: string;
  /**
   * Getter method for property quantity.
   * @return property value of quantity
   */
  quantity: number;
  /**
   * Getter method for property stuffAttrId.
   * @return property value of stuffAttrId
   */
  stuffAttrId: string;
  /**
   * Getter method for property <tt>stuffAttrNickName</tt>.
   * @return property value of stuffAttrNickName
   */
  stuffAttrNickName: string;
  /**
   * Getter method for property status.
   * @return property value of status
   */
  status: string;
  /**
   * Getter method for property extInfo.
   * @return property value of extInfo
   */
  extInfo: string;
  /**
   * Getter method for property remark.
   * @return property value of remark
   */
  remark: string;
  /**
   * Getter method for property source.
   * @return property value of source
   */
  source: string;
  /**
   * Getter method for property bindType.
   * @return property value of bindType
   */
  bindType: string;
  /**
   * Getter method for property templateId.
   * @return property value of templateId
   */
  templateId: string;
  /**
   * Getter method for property <tt>templateNickName</tt>.
   * @return property value of templateNickName
   */
  templateNickName: string;
  /**
   * Getter method for property itemFlowId.
   * @return property value of itemFlowId
   */
  itemFlowId: string;
  /**
   * Getter method for property providerId.
   * @return property value of providerId
   */
  providerId: string;
  /**
   * Getter method for property providerName.
   * @return property value of providerName
   */
  providerName: string;
  /**
   * Getter method for property role.
   * @return property value of role
   */
  role: string;
  /**
   * Getter method for property <tt>targetId</tt>.
   * @return property value of targetId
   */
  targetId: string;
  /**
   * Getter method for property <tt>targetIdType</tt>.
   * @return property value of targetIdType
   */
  targetIdType: string;
}

/**
 * 数据模型，包装gmtCreate，gmtModified
 * <AUTHOR>
 * @version $Id: DataModel.java, v 0.1 2016年4月18日 上午6:56:27 zhengyuan.zy Exp $
 */
export interface DataModel083922 extends Serializable {
  /**
   * Getter method for property <tt>gmtCreate</tt>.
   * @return property value of gmtCreate
   */
  gmtCreate: Date;
  /**
   * Getter method for property <tt>gmtModified</tt>.
   * @return property value of gmtModified
   */
  gmtModified: Date;
}

export interface KbCodeTemplateTrafficItemDetailVO {
  /**
   * 码路由的场景值
   * {@link com.alipay.kbasset.common.service.facade.stuff.bff.enums.KbTrafficBizSceneEnum}
   */
  trafficBizScene: string;
  /**
   * 码路由场景值描述
   */
  trafficBizSceneDesc: string;
  /**
   * 码路由场景值名称
   */
  trafficBizSceneName: string;
  templateNickName: string;
}

/**
 * <AUTHOR>
 * @version : PaveAttachmentRequest.java, v 0.1 2020年04月16日 16:25 honger.lb Exp $
 */
export interface PaveAttachmentRequest {
  /**
   * 模板别名,如果是基础物料或者码物料的话，即使BASIC: 基础物料  OTHER:其他物料  ACTIVITY_CODEMATERIAL:码物料
   */
  bizSource: string;
  /**
   * 图片 resourceId
   */
  resourceIds: string[];
}

/**
 * <AUTHOR>
 * @version : PaveInfoShareRequest.java, v 0.1 2020年04月16日 16:22 honger.lb Exp $
 */
export interface PaveInfoShareRequest {
  /**
   * 分享链接
   */
  link: string;
  /**
   * 分享接收人session多个逗号分开
   */
  receiverSessions: string;
  /**
   * 分享群聊session多个逗号分开
   * @deprecated
   */
  groupSessions: string;
}

/**
 * <AUTHOR>
 * @version : PaveCheckDetailVO.java, v 0.1 2020年04月27日 21:52 honger.lb Exp $
 */
export interface PaveCheckDetailVO {
  /**
   * 门店地址
   */
  shopAddress: string;
  /**
   * 审核信息
   */
  stuffCheckVO: PaveStuffCheckVO;
  /**
   * 铺设图片
   */
  stuffImgs: PaveAuditImgVO[];
  /**
   * 模板
   */
  stuffTemplateDto: StuffTemplateDto;
  /**
   * 操作记录
   */
  bizOrders: PaveAuditBizLogVO[];
}

/**
 * <AUTHOR>
 * @version : PaveBaseInfo.java, v 0.1 2020年04月16日 17:33 honger.lb Exp $
 */
export interface PaveBaseInfoVO extends Serializable {
  /**
   * 铺设 ID
   */
  paveId: string;
  /**
   * 铺设主体 id
   */
  targetId: string;
  /**
   * 主体类型
   */
  targetType: string;
  /**
   * 铺设主体名称
   */
  targetName: string;
  /**
   * 活动物料模板
   */
  bizSource: string;
  /**
   * 活动物料名称
   */
  bizSourceName: string;
  /**
   * 铺设状态
   * {@link StuffCheckStatusEnum}
   */
  status: string;
  /**
   * 审核状态
   */
  checkStatus: string;
  /**
   * 审核类型
   * {@link CheckTypeEnum}
   */
  checkType: string;
  /**
   * 创建时间
   * yyyy-MM-dd HH:mm:ss
   */
  gmtCreate: string;
  /**
   * 铺设样例
   */
  paveSample: Attachment;
}

/**
 * <AUTHOR>
 * @version : QueryPaveInfoRequest.java, v 0.1 2020年04月16日 17:59 honger.lb Exp $
 */
export interface QueryPaveInfoRequest {
  /**
   * 状态
   */
  status: string;
  /**
   * 铺设起始
   * yyyy-MM-dd
   */
  gmtStart: string;
  /**
   * 铺设结束
   * yyyy-MM-dd
   */
  gmtEnd: string;
}

/**
 * 高德audit网关 基础请求
 * 配置参考
 * https://mse.alibaba-inc.com/pre/diamond/configlist/configedit?DataId=com.amap.audit:bffGatewayConfig.json&Group=mpaudit&AppName=amap-mp-aduit&tab=edit
 * "/mp/cloudStore/bff/toolkit/exportManage/queryList": {
 * 			"artifactId": "alsc-kbt-intergration-toolkit",
 * 			"type": "HSF",
 * 			"apiInterface": "com.alsc.kbt.toolkit.gateway.biz.ExportFileGateway",
 * 			"method": "pageQueryExportFileRecord",
 *             "userId": "opUserId",
 *             "userName": "opUserName",
 * 			"params": [{
 * 				"name": "request",
 * 				"type": "com.alsc.kbt.toolkit.gateway.exportFile.request.ExportFilePageQueryRecordGatewayRequest"
 *                        }]* 		},
 * <AUTHOR>
 * @version v 0.1
 * @classname AmapAuditGwBaseRequest
 * @date 2024-03-21 14:28
 */
export interface AmapAuditGwBaseRequest extends BaseRequest {
  /**
   * 操作人userId
   */
  opUserId: string;
  /**
   * 操作人userName
   */
  opUserName: string;
}

/**
 * 高德audit网关 分页基础请求
 * 配置参考
 * https://mse.alibaba-inc.com/pre/diamond/configlist/configedit?DataId=com.amap.audit:bffGatewayConfig.json&Group=mpaudit&AppName=amap-mp-aduit&tab=edit
 * "/mp/cloudStore/bff/toolkit/exportManage/queryList": {
 * 			"artifactId": "alsc-kbt-intergration-toolkit",
 * 			"type": "HSF",
 * 			"apiInterface": "com.alsc.kbt.toolkit.gateway.biz.ExportFileGateway",
 * 			"method": "pageQueryExportFileRecord",
 *             "userId": "opUserId",
 *             "userName": "opUserName",
 * 			"params": [{
 * 				"name": "request",
 * 				"type": "com.alsc.kbt.toolkit.gateway.exportFile.request.ExportFilePageQueryRecordGatewayRequest"
 *                        }]* 		},
 * <AUTHOR>
 * @version v 0.1
 * @classname AmapAuditGwBaseRequest
 * @date 2024-03-21 14:28
 */
export interface AmapAuditGwPageBaseRequest extends AmapAuditGwBaseRequest {
  pageSize: number;
  pageNum: number;
  offset: number;
}

export interface OrderCondition extends ToStringbd7e46 {
  columnName: string;
  asc: boolean;
}

/**
 * 通用操作员信息
 * <AUTHOR>
 * @version : CommonOperatorInfo.java, v 0.1 2023年05月31日 上午11:27 LiShu Exp $
 */
export interface CommonOperatorInfo extends ToString {
  /**
   * Getter method for property <tt>operatorId</tt>.
   * @return property value of operatorId
   */
  operatorId: string;
  /**
   * Getter method for property <tt>operatorType</tt>.
   * @return property value of operatorType
   */
  operatorType: string;
  /**
   * Getter method for property <tt>operatorName</tt>.
   * @return property value of operatorName
   */
  operatorName: string;
  /**
   * Getter method for property <tt>sellerId</tt>.
   * @return property value of sellerId
   */
  sellerId: string;
  /**
   * Getter method for property <tt>partnerId</tt>.
   * @return property value of partnerId
   */
  partnerId: string;
  /**
   * Getter method for property <tt>workId</tt>.
   * @return property value of workId
   */
  workId: string;
}

/**
 * 任务查询基础父类
 * <AUTHOR>
 * @version : BaseTaskQueryRequest.java, v 0.1 2023年05月20日 下午3:07 LiShu Exp $
 */
export interface BaseTaskQueryRequest extends BasePageNoQueryRequest {
  /**
   * Getter method for property <tt>appSource</tt>.
   * @return property value of appSource
   */
  appSource: string;
  /**
   * Getter method for property <tt>commonOperatorInfo</tt>.
   * @return property value of commonOperatorInfo
   */
  commonOperatorInfo: CommonOperatorInfo;
}

export interface SuggestionLabelDTO {
  labelCode: string;
  labelContent: string[];
}

export interface MerchantIntentionDTO {
  /**
   * 商户意向程度
   * {@link com.alsc.kbt.toolkit.biz.task.enums.MerchantIntentionDegreeEnum}
   */
  intentionDegree: string;
  intentionDesc: string;
  recordTime: string;
  /**
   * 商户意向来源
   * {@link MerchantIntentionSource}
   */
  source: string;
}

/**
 * 任务跳转地址
 * <AUTHOR>
 * @version : TaskJumpDTO.java, v 0.1 2023年05月27日 下午4:33 yaoyu Exp $
 */
export interface TaskJumpDTO extends ToString {
  /**
   * Getter method for property <tt>client</tt>.
   * @return property value of client
   */
  client: string;
  /**
   * Getter method for property <tt>jumpType</tt>.
   * @return property value of jumpType
   */
  jumpType: string;
  /**
   * Getter method for property <tt>jumpUrl</tt>.
   * @return property value of jumpUrl
   */
  jumpUrl: string;
  /**
   * Getter method for property <tt>buttonText</tt>.
   * @return property value of buttonText
   */
  buttonText: string;
  /**
   * 新增跳转类型
   */
  jumpTypeNew: string;
  /**
   * 新增跳转链接
   */
  jumpUrlList: string[];
}

/**
 * 任务域请求基类
 * <AUTHOR>
 * @date 2023/05/17/10:31
 */
export interface BaseTaskRequest extends BaseRequest {
  /**
   * 来源应用
   *
   * {@link AppSourceEnum#getCode()}
   */
  appSource: string;
  /**
   * Getter method for property <tt>commonOperatorInfo</tt>.
   * @return property value of commonOperatorInfo
   */
  commonOperatorInfo: CommonOperatorInfo;
  /**
   * Getter method for property <tt>extInfo</tt>.
   * @return property value of extInfo
   */
  extInfo: { [index: string]: string };
}

/**
 * 任务详情
 * <AUTHOR>
 * @version : ShopInfrastTaskDetailDTO.java, v 0.1 2023年05月27日 下午4:22 LiShu Exp $
 */
export interface TaskDetailDTO extends ToString {
  /**
   * Getter method for property <tt>taskName</tt>.
   * @return property value of taskName
   */
  taskName: string;
  /**
   * Getter method for property <tt>taskDesc</tt>.
   * @return property value of taskDesc
   */
  taskDesc: string;
  /**
   * Getter method for property <tt>taskTip</tt>.
   * @return property value of taskTip
   */
  taskTip: string;
  /**
   * Getter method for property <tt>taskDetailStatus</tt>.
   * @return property value of taskDetailStatus
   */
  taskDetailStatus: string;
  /**
   * Getter method for property <tt>taskScore</tt>.
   * @return property value of taskScore
   */
  taskScore: string;
  /**
   * Getter method for property <tt>taskIndicatorTargetNum</tt>.
   * @return property value of taskIndicatorTargetNum
   */
  taskIndicatorTargetNum: string;
  /**
   * Getter method for property <tt>taskIndicatorCompletedNum</tt>.
   * @return property value of taskIndicatorCompletedNum
   */
  taskIndicatorCompletedNum: string;
  /**
   * Getter method for property <tt>taskDetailJumpList</tt>.
   * @return property value of taskDetailJumpList
   */
  taskDetailJumpList: TaskJumpDTO[];
  /**
   * 任务详情跳转
   */
  sopButtons: TaskJumpDTO[];
  /**
   * 任务实际得分,默认是0分
   */
  taskActualScore: string;
  /**
   * 任务扩展信息,目前有商户通过期信息、商户意向信息等
   */
  extInfo: { [index: string]: any };
  taskDetailLabelList: string[];
  /**
   * 奖励类型，merchant_score 为商家分，exposure 为曝光
   */
  rewardType?: string;
}

/**
 * <AUTHOR>
 * @version $Id: IndicatorTtemDTO.java, v 0.1 2023-09-06 10:39 AM boliang.hbl Exp $$
 * DESC:指标项
 */
export interface IndicatorTtemDTO {
  /**
   * Getter method for property <tt>indicatorGroupName</tt>.
   * @return property value of indicatorGroupName
   */
  indicatorGroupCode: string;
  /**
   * Getter method for property <tt>targetData</tt>.
   * @return property value of targetData
   */
  targetData: string;
  /**
   * Getter method for property <tt>currentAccomplish</tt>.
   * @return property value of currentAccomplish
   */
  currentAccomplish: string;
  /**
   * Getter method for property <tt>completePercent</tt>.
   * @return property value of completePercent
   */
  completePercent: string;
}

/**
 *
 *
 * 广告待办任务概览DTO模型.</br>
 * <AUTHOR>
 * @version : AdTaskSummaryDTO, v 0.1 2023/12/1 16:02 muhan Exp $
 */
export interface AdTaskSummaryDTO extends ToString {
  /**
   * Getter method for property taskName.
   * @return property value of taskName
   */
  taskName: string;
  /**
   * Getter method for property taskDesc.
   * @return property value of taskDesc
   */
  taskDesc: string;
  /**
   * Getter method for property taskCount.
   * @return property value of taskCount
   */
  taskCount: number;
  /**
   * Getter method for property pcJumpUrl.
   * @return property value of pcJumpUrl
   */
  pcJumpUrl: string;
}

/**
 * 商户通预警对象
 */
export interface ShangHuTongRenewalUrgentDTO {
  day: number;
  week: number;
  month: number;
}

export interface OperatorInfo671c73 extends ToStringbd7e46 {
  operatorId: string;
  operatorType: string;
  operatorName: string;
  sellerId: string;
  partnerId: string;
  workId: string;
}

export interface GwBaseRequestDTO extends BaseRequest {
  extMap: { [index: string]: string };
}

export interface ToStringbd7e46 extends Serializable, BeanSimpleLog {}

/**
 * <AUTHOR>
 * @version $Id: VisitItemVO.java, v 0.1 2023年09月20日 19:19 荒丘 Exp $
 */
export interface VisitItemVO extends ToString {
  /**
   * 事项ID
   */
  visitItemId: string;
  /**
   * 归属分类ID
   */
  visitCategoryId: string;
  /**
   * 事项名称
   */
  name: string;
  /**
   * 事项描述
   */
  intro: string;
  /**
   * 是否有效拜访
   *
   * 只有系统创建的顶级事项才会有这个字段,配置界面小二创建的默认都是null
   */
  validVisitFlag: boolean;
  /**
   * 状态 ENABLE（已启用）DISABLE（已停用）
   * @see VisitConfigStatusEnum
   */
  status: string;
  statusText: string;
  /**
   * 被config使用的数量
   */
  usedConfigCount: number;
  /**
   * 字段列表
   */
  fieldList: VisitFieldVO[];
  /**
   * 适用的拜访渠道列表
   * 当前事项适用的渠道,必须在配置支持的渠道范围内
   */
  supportChannelList: string[];
  /**
   * 扩展字段
   */
  extInfo: { [index: string]: any };
}

/**
 * <AUTHOR>
 * @version $Id: VisitItemEleVO.java, v 0.1 2023年11月23日 21:51 荒丘 Exp $
 */
export interface VisitItemEleVO extends ToString {
  /**
   * 分类ID
   */
  visitItemId: string;
  /**
   * 事项名称
   */
  name: string;
  /**
   * 分类被有效配置使用的数量
   */
  usedConfigCount: number;
  /**
   * 状态 ENABLE（已启用）DISABLE（已停用）
   * @see VisitConfigStatusEnum
   */
  status: string;
  statusText: string;
  /**
   * 事项下的字段
   */
  fieldList: VisitFieldEleVO[];
}

/**
 * <AUTHOR>
 * @version $Id: VisitActionVO.java, v 0.1 2023年11月21日 20:00 荒丘 Exp $
 */
export interface VisitActionVO extends ToString {
  /**
   * @see VisitActionTypeEnum
   * 动作类型：MODIFY（修改）QUERY_DETAIL（查看详情）CLOSE（废弃）OPEN（生效）
   */
  actionType: string;
  /**
   * 动作名称
   */
  actionName: string;
}

export interface BasePageNoQueryRequest extends BaseRequest {
  pageNum: number;
  pageSize: number;
  orderConditionList: OrderCondition[];
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigSimpleVO.java, v 0.1 2023年10月08日 14:05 荒丘 Exp $
 */
export interface VisitConfigSimpleVO extends ToString {
  configId: string;
  /**
   * 版本号
   */
  configVersion: string;
  /**
   * 拜访配置名称
   */
  configName: string;
}

/**
 * <AUTHOR>
 * @version $Id: JobVO.java, v 0.1 2023年09月22日 10:29 荒丘 Exp $
 */
export interface JobVO extends ToString {
  jobId: string;
  jobName: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitTargetTypeVO.java, v 0.1 2023年09月22日 10:30 荒丘 Exp $
 */
export interface VisitTargetTypeVO extends ToString {
  targetType: string;
  targetTypeName: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitChannelVO.java, v 0.1 2023年09月22日 10:31 荒丘 Exp $
 */
export interface VisitChannelVO extends ToString {
  code: string;
  label: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitFieldVO.java, v 0.1 2023年09月20日 19:24 荒丘 Exp $
 */
export interface VisitFieldVO extends ToString {
  /**
   * 字段ID
   */
  visitFieldId: string;
  /**
   * 字段名称
   */
  name: string;
  /**
   * 字段说明
   */
  intro: string;
  /**
   * 字段一级类型
   * @see VisitFieldTypeEnum
   */
  fieldType: string;
  /**
   * 字段二级类型
   * @see VisitFieldOptionTypeEnum
   */
  fieldOption: string;
  /**
   * 是否必填
   */
  required: boolean;
  /**
   * 最小数量(字段必填时生效)
   * @see VisitFieldOptionTypeEnum#TEXT 文本时是最小字数
   * @see VisitFieldOptionTypeEnum#IMAGE 最小图片数量
   * @see VisitFieldOptionTypeEnum#ONLY_PHOTO 最小图片数量
   * @see VisitFieldOptionTypeEnum#FILE 最小文件数量
   * @see VisitFieldOptionTypeEnum#EXCEL 最小文件数量
   * @deprecated
   */
  minCount: number;
  /**
   * 文本必填时的最小字数
   */
  wordCountFloor: number;
  /**
   * 图片必填时的最小图片数
   */
  imageCountFloor: number;
  /**
   * 文件必填时的最小图片数
   */
  fileCountFloor: number;
  /**
   * 单选多选时的选项字段
   */
  options: VisitFieldOptionVO[];
  /**
   * 适用的拜访渠道列表
   * 当前事项适用的渠道,必须在配置支持的渠道范围内
   */
  supportChannelList: string[];
  /**
   * 是否在配置中被选中
   * 字段在拜访配置中才有效
   */
  selected: boolean;
}

/**
 * 拜访联系人信息
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-22 14:38
 */
export interface VisitContactPersonVO extends ToString {
  /**
   * 联系人ID,外部系统的ID,不强依赖这个
   */
  cpId: string;
  /**
   * 联系人名称
   */
  cpName: string;
  /**
   * 联系人职位
   * 外部传入的,这里冗余存储一下
   */
  cpPosition: string;
  /**
   * 是否KP
   * YES/NO
   * @see com.alsc.kbt.toolkit.enums.YesNoEnum
   */
  keyPersonFlag: string;
  /**
   * 明文电话号码
   */
  contactNo: string;
  /**
   * 加密电话号码 ，解密使用公共接口里面的解密方法
   */
  contactNoEncode: string;
  /**
   * 脱敏电话号码
   */
  contactNoShield: string;
}

export interface GwBasePageNoRequestDTO extends BasePageNoQueryRequest {
  extMap: { [index: string]: string };
}

/**
 * 用户信息
 * <AUTHOR>
 * @version v 0.1
 */
export interface VisitUserInfoVO extends ToString {
  /**
   * 用户名称
   */
  userName: string;
  /**
   * 用户昵称（花名）
   */
  userNick: string;
  /**
   * 用户职位
   */
  userJobList: string[];
  /**
   * 用户联系方式-明文
   */
  contact: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordInputVO.java, v 0.1 2023年09月20日 16:36 荒丘 Exp $
 */
export interface VisitRecordInputVO extends ToString {
  /**
   * 这个是根据用户选择的 topLevelItemList 中的元素对应ID传入
   */
  topLevelItemId: string;
  /**
   * 这个是根据用户选择的 topLevelItemList 中的元素对应validVisit填写的
   * @see YesNoEnum
   * 不填默认否
   */
  validFlag: string;
  /**
   * 联系人信息
   */
  contactPerson: VisitContactPersonVO;
  /**
   * 拜访描述
   */
  content: string;
  secondVisitPlan: boolean;
  planTime: Date;
  planContent: string;
  /**
   * 用户选中的拜访事项ID
   * 特殊的:
   * 1.用户选中了拜访事项,拜访事项下的必填字段才会检查是否必填
   * 2.用户选中了拜访事项,如果拜访事项下字段全是非必填,也得有至少一个字段填写内容
   */
  visitItemIdList: string[];
  /**
   * 用户填写的动态字段列表
   */
  visitFieldList: VisitFieldDataVO[];
  /**
   * 陪访人信息
   */
  participateList: VisitParticipateUserVO[];
}

/**
 * <AUTHOR>
 * @version $Id: VisitTopLevelItemVO.java, v 0.1 2023年10月19日 14:44 荒丘 Exp $
 */
export interface VisitTopLevelItemVO extends ToString {
  /**
   * 事项ID
   */
  visitItemId: string;
  /**
   * 事项名称
   */
  name: string;
  /**
   * 是否有效拜访
   *
   * 只有系统创建的顶级事项才会有这个字段,配置界面小二创建的默认都是null
   */
  validVisitFlag: boolean;
  /**
   * 状态 ENABLE（已启用）DISABLE（已停用）
   * @see VisitConfigStatusEnum
   */
  status: string;
  statusText: string;
  /**
   * 扩展字段
   */
  extInfo: { [index: string]: any };
  /**
   * 是否被选中
   */
  selected: boolean;
}

/**
 * <AUTHOR>
 * @version $Id: VisitParticipateUserVO.java, v 0.1 2023年11月27日 11:41 荒丘 Exp $
 */
export interface VisitParticipateUserVO extends ToString {
  /**
   * 陪访人ID
   */
  participateId: string;
  /**
   * 陪访人名称
   */
  participateName: string;
  /**
   * 陪访人昵称
   */
  participateNick: string;
  /**
   * 编辑场景时,陪访人是否可以移除
   */
  canDelete: boolean;
}

/**
 * 陪访记录vo
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-11-09 14:38
 */
export interface VisitParticipantRecordVO extends ToString {
  /**
   * 陪访记录id
   */
  visitParticipantRecordId: string;
  /**
   * 陪访时间
   */
  createTime: string;
  /**
   * 评价时间
   */
  ratingTime: string;
  /**
   * 是否归属当前用户
   */
  indexUserOwner: boolean;
  /**
   * 是否已经评价
   */
  ratingFlag: boolean;
  /**
   * 评价内容
   */
  content: string;
  /**
   * 是否可修改评价
   */
  canModifyRating: boolean;
  /**
   * 陪访人id
   */
  opId: string;
  /**
   * 陪访人
   */
  participantUserInfo: VisitUserInfoVO;
  /**
   * 是否可调整详情页
   */
  canJumpDetail: boolean;
}

/**
 * 拜访计划信息vo
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-20 11:37
 */
export interface VisitRecordBaseInfoVO extends ToString {
  /**
   * 拜访记录逻辑ID
   */
  visitRecordId: string;
  /**
   * 组织架构
   * 第一个默认是hr架构
   */
  jobPathList: string[];
  /**
   * 拜访人ID
   */
  visitorInfo: VisitUserInfoVO;
  /**
   * 拜访时间
   */
  visitTime: string;
  /**
   * 拜访方式
   */
  visitChannel: string;
  /**
   * 拜访方式文案
   */
  visitChannelDesc: string;
  /**
   * 拜访描述
   */
  visitContent: string;
  /**
   * 是否有定位预警 true、false
   */
  hasLocationAlert: string;
  /**
   * 定位距离，单位：米
   */
  distance: string;
  /**
   * 状态
   * EFFECTIVE("有效"),
   * INVALID("无效"),
   */
  status: string;
  /**
   * 通话时长，单位：秒
   */
  callDurationSec: string;
  /**
   * 拜访有效性枚举
   */
  visitValidType: string;
  /**
   * 拜访有效性文案
   */
  visitValidTypeDesc: string;
  /**
   * 无效原因列表
   */
  invalidReason: string;
  /**
   * 是否电话补录场景 true、false
   */
  callReplenishFlag: string;
  /**
   * 是否可编辑 true、false
   */
  canEdit: string;
  /**
   * 关联通话记录ID
   */
  relateCallRecordId: string;
}

/**
 * 记录关联的分类事项
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-22 14:47
 */
export interface VisitRecordCategoryVO extends ToString {
  /**
   * id
   */
  visitCategoryId: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 介绍
   */
  intro: string;
  /**
   * 拜访事项
   */
  itemList: VisitRecordItemVO[];
}

/**
 * 记录关联的拜访字段
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-22 14:47
 */
export interface VisitRecordFieldVO extends ToString {
  /**
   * 字段ID
   */
  visitFieldId: string;
  /**
   * 字段名称
   */
  name: string;
  /**
   * 字段说明
   */
  intro: string;
  /**
   * 字段一级类型
   * @see
   */
  fieldType: VisitFieldTypeEnum;
  /**
   * 字段二级类型
   * @see
   */
  fieldOption: VisitFieldOptionTypeEnum;
  /**
   * 填入的字段
   */
  data: string;
  /**
   * 前端展示使用的字段,如果是上传的图片或文件,需要将oss路径转换成可访问的url
   */
  displayData: string;
  /**
   * 单选多选时的选项字段
   */
  optionList: VisitRecordFieldOptionVO[];
}

/**
 * 拜访记录关联通话记录信息
 * <AUTHOR>
 * @version v 0.1
 * @classname VisitRecordCallRecordVO
 * @date 2023-10-08 15:54
 */
export interface VisitRecordCallRecordVO extends ToString {
  /**
   * 通话时间
   */
  callTime: Date;
  /**
   * 通话时长/单位秒
   */
  callSec: number;
  /**
   * 加密电话号码 ，解密使用公共接口里面的解密方法
   */
  contactEncode: string;
  /**
   * 脱敏电话号码
   */
  contactShield: string;
  /**
   * 录音链接-不一定有
   */
  callSoundRecordLink: string;
}

export interface ConditionGroupDTO extends ToString {
  /**
   * 编号
   */
  numberNo: string;
  /**
   * 优先级
   */
  priority: number;
  /**
   * 标签配置值
   */
  labelGroupDetailValueDTOS: LabelGroupDetailValueDTO[];
  /**
   * 是否设置指标
   */
  indicate: boolean;
  /**
   * 指标详情
   */
  indicatorRuleDetailDTO: IndicatorRuleDetailDTO;
  /**
   * 话术内容
   */
  content: string;
}

export interface FormulasDTO extends ToString {
  /**
   * 编码
   */
  code: string;
  /**
   * 描述
   */
  label: string;
  /**
   * 过滤选择项
   */
  excludeSelectOptions: string[];
}

export interface IndicatorOption extends ToString {
  /**
   * 描述
   */
  label: string;
  /**
   * 值
   */
  value: string;
}

/**
 * 模板属性
 * <AUTHOR>
 * @version : TemplatePropertyDTO.java, v 0.1 2020年07月06日 5:25 下午 xiaowai Exp $
 */
export interface RuleConfigDTO extends ToString {
  /**
   * 规则code
   */
  ruleCode: string;
  /**
   * 规则名称
   */
  ruleName: string;
  /**
   * 最多可选择数量
   */
  maxMultiSelect: string;
  /**
   * 是否可多选
   */
  multiSelect: boolean;
  /**
   * 是否必选
   */
  required: boolean;
  /**
   * 候选属性值
   */
  ruleValueList: RuleValueConfigDTO[];
}

/**
 * 模板属性值
 * <AUTHOR>
 * @version : TemplatePropertyValueDTO.java, v 0.1 2020年07月06日 5:27 下午 xiaowai Exp $
 */
export interface RuleValueConfigDTO extends ToString {
  /**
   * 规则值名称
   */
  ruleValueDesc: string;
  /**
   * 规则值code
   */
  ruleValueCode: string;
}

/**
 * Description:
 * Author: xuelin
 * Date: 2023/12/20 13:54
 */
export interface SmartWordTemplateInfoQueryCond {}

/**
 * <AUTHOR>
 * @version $Id: CallTerritoryVO.java, v 0.1 2023年09月06日 19:36 荒丘 Exp $
 */
export interface CallTerritoryVO extends ToString {
  /**
   * 领地ID
   */
  territoryId: string;
  /**
   * 领地名称
   */
  territoryName: string;
}

/**
 * 联系方式
 * <AUTHOR>
 * @version $Id: CallContactWayVO.java, v 0.1 2023年08月25日 16:24 荒丘 Exp $
 */
export interface CallContactWayVO extends ToString {
  /**
   * 联系方式类型
   * @see CallContactType
   */
  contactType: string;
  /**
   * 联系方式内容
   */
  contactValue: string;
}

/**
 * <AUTHOR>
 * @date 2024/02/07
 */
export interface CallTargetContactAttributeVO extends ToString {
  /**
   * 空号标识
   */
  phoneNotExist: boolean;
  /**
   * 无关KP标记
   */
  notRelatedKP: boolean;
  /**
   * 中文属性标签
   * @value ContactAttributeTypeEnum#getDesc()
   */
  chineseAttributeLabels: string[];
}

/**
 * <AUTHOR>
 * @version $Id: CallTargetContactInfoStatisticsVO.java, v 0.1 2023年08月25日 16:35 荒丘 Exp $
 */
export interface CallTargetContactInfoStatisticsVO extends ToString {
  /**
   * 电话号码
   */
  phone: string;
  /**
   * 接通率
   * 20%
   */
  connectedRateStr: string;
  /**
   * 40s接通率
   * 20%
   */
  connectedOver40sRateStr: string;
  /**
   * 上午接通率
   * 20%
   */
  connectedMornRateStr: string;
  /**
   * 中午接通率
   * 20%
   */
  connectedNoonRateStr: string;
  /**
   * 下午接通率
   * 20%
   */
  connectedAfternoonRateStr: string;
  /**
   * 晚上接通率
   * 20%
   */
  connectedEvenRateStr: string;
  /**
   * 通话次数
   * 10
   */
  calledCnt: number;
}

/**
 * 支持签维分离扩展请求模型
 * <AUTHOR>
 * @version : GwBaseShopExtendRequestDTO.java, v 0.1 2023年12月27日 2:34 PM mayuanmac Exp $
 */
export interface GwBaseSignSepExtendRequestDTO extends GwBaseRequestDTO {
  /**
   * 业务场景，区分新签还是维护
   * @value {@code com.alsc.kbt.toolkit.enums.ExpendTypeEnum}
   */
  opType: string;
  /**
   * 店铺id
   */
  shopId: string;
}

/**
 * 当前登录小二外呼配置
 * <AUTHOR>
 * @version $Id: BdCallConfigVO.java, v 0.1 2023年05月31日 22:22 荒丘 Exp $
 */
export interface BdCallConfigVO extends ToString {
  /**
   * 小二电销的:主叫号码
   */
  fromPhone: string;
}

/**
 * <AUTHOR>
 * @version $Id: CallActionVO.java, v 0.1 2023年06月06日 15:15 荒丘 Exp $
 */
export interface CallActionVO extends ToString {
  actionType: string;
  actionName: string;
  fileUrl: string;
}

/**
 * 错误码接口，给枚举使用的接口
 *
 * 对应于大家常规理解的errorCode、errorMessage
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 * @deprecated 使用 tribe包中的
 */
export interface IErrorCode {
  /**
   * 唯一编码，用于校验识别。一般是英文，可以组合数字，如：CE003
   */
  code: string;
  /**
   * 含义描述，用于方便理解。可以是英文串，也可以用中文串，如：CHK_PARAMS_ILLEGAL 参数检查异常
   */
  message: string;
}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 */
export interface BaseDO extends Serializable {}

/**
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/14
 */
export interface ShopLabelResponse extends Serializable {
  /**
   * 标签名称
   */
  title: string;
  /**
   * 标签字体颜色
   */
  titleColor: string;
  /**
   * 标签背景颜色
   */
  backgroundColor: string;
  /**
   * 标签边框颜色
   */
  borderColor: string;
  /**
   * 标签备注
   */
  remark: string;
}

/**
 * <AUTHOR>
 * @version : PaveStuffCheckVO.java, v 0.1 2020年04月27日 21:54 honger.lb Exp $
 */
export interface PaveStuffCheckVO {
  auditOperatorName: string;
  channel: string;
  checkEnable: string;
  checkMemo: string;
  checkReason: string;
  cityName: string;
  detailEnable: string;
  paveCreatorName: string;
  paveCreatorType: string;
  paveCreatorTypeName: string;
  paveName: string;
  paveTime: Date;
  shopAddress: string;
  status: string;
  statusName: string;
  stuffCheckId: string;
  stuffImgCount: number;
  stuffType: string;
  stuffTypeName: string;
  targetId: string;
  targetName: string;
  signDistance: string;
}

/**
 * <AUTHOR>
 * @version : PaveAuditImgVO.java, v 0.1 2020年04月28日 11:35 honger.lb Exp $
 */
export interface PaveAuditImgVO extends Attachment {
  urlParam: string;
  name: string;
}

/**
 * <AUTHOR>
 * @version : PaveAuditBizLogVO.java, v 0.1 2020年04月28日 11:27 honger.lb Exp $
 */
export interface PaveAuditBizLogVO {
  /**
   * 描述
   */
  desc: string;
  /**
   * 备注
   */
  memo: string;
  /**
   * 操作人
   */
  operatorName: string;
  /**
   * 操作状态
   */
  operatorStatus: string;
  /**
   * 操作状态描述
   */
  operatorStatusDesc: string;
  /**
   * 操作时间
   */
  processTime: Date;
  /**
   * 铺设状态
   */
  type: string;
  /**
   * 任务描述
   */
  typeDesc: string;
}

export interface BeanSimpleLog {}

/**
 * <AUTHOR>
 * @version $Id: VisitFieldEleVO.java, v 0.1 2023年11月23日 21:53 荒丘 Exp $
 */
export interface VisitFieldEleVO extends ToString {
  /**
   * 分类ID
   */
  visitFieldId: string;
  /**
   * 事项名称
   */
  name: string;
}

/**
 * <AUTHOR>
 * @version $Id: VisitFieldOptionVO.java, v 0.1 2023年09月20日 19:34 荒丘 Exp $
 */
export interface VisitFieldOptionVO extends ToString {
  /**
   * code
   */
  code: string;
  /**
   * 值
   */
  value: string;
  /**
   * 子项
   */
  children: VisitFieldOptionVO[];
}

/**
 * 拜访字段的内容
 * 只有id和值,不包括字段模板信息
 * <AUTHOR>
 * @version $Id: VisitFieldDataVO.java, v 0.1 2023年10月10日 15:18 荒丘 Exp $
 */
export interface VisitFieldDataVO extends ToString {
  /**
   * 字段ID
   */
  visitFieldId: string;
  /**
   * 传入的值
   * 单个值,传单元素的列表
   * 多个值,传多元素的列表
   * 图片,元素传oss相对路径
   * 服务端下发这个模型是,图片的展示地址会对应放在displayList中
   */
  valueList: string[];
  valueDisplayList: string[];
}

/**
 * 记录关联的分类事项
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-22 14:47
 */
export interface VisitRecordItemVO extends ToString {
  /**
   * ID
   */
  visitItemId: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 事项下的配置列表
   */
  fieldList: VisitRecordFieldVO[];
}

/**
 * 记录关联的拜访字段
 * <AUTHOR>
 * @version v 0.1
 * @date 2023-09-22 14:47
 */
export interface VisitRecordFieldOptionVO extends ToString {
  /**
   * code
   */
  code: string;
  /**
   * 值
   */
  value: string;
  /**
   * 子项
   */
  children: VisitRecordFieldOptionVO[];
}

export interface LabelGroupDetailValueDTO extends ToString {
  /**
   * 是否设置, 0表示不设置，1表示设置
   */
  set: string;
  /**
   * 标签配置值
   */
  labelDetailValueDTOS: LabelDetailValueDTO[];
  /**
   * 规则code{@link SmartWordRuleCodeEnum}
   */
  groupCode: string;
}

export interface IndicatorRuleDetailDTO {
  /**
   * 组间关系
   */
  relation: string;
  /**
   * 指标组
   */
  ruleConditionGroupList: IndicatorRuleConditionGroupDTO[];
}

export interface LabelDetailValueDTO extends ToString {
  /**
   * code
   */
  code: string;
  /**
   * 标签配置值
   */
  values: string[];
}

export interface IndicatorRuleConditionGroupDTO extends ToString {
  /**
   * 组内关系
   */
  relation: string;
  /**
   * 指标
   */
  conditionList: IndicatorRuleConditionDTO[];
}

export interface IndicatorRuleConditionDTO extends ToString {
  /**
   * id
   */
  id: string;
  /**
   * 规则类型
   */
  ruleType: string;
  /**
   * 公式
   */
  formula: string;
  /**
   * 指标code
   */
  code: string;
  /**
   * 指标内容
   */
  content: string;
}

/**
 * 口碑码绑定
 * <AUTHOR>
 * @version $Id: QrcodeBindFacade.java, v 0.1 2022年12月10日 上午9:33:23 juyang Exp $
 */
export interface QrcodeBindFacade {
  batchDownloadQRCode(downloadQRCodeReq: DownloadQRCodeReq): Promise<KbResult<string>>;

  bindCode(bindCodeReq: BindCodeReq): Promise<KbResult<BindResVO>>;

  isInPercentOnBind(): Promise<KbResult<boolean>>;

  needBuildShopCode(shopId: string, appSource: string): Promise<KbResult<boolean>>;

  queryMyShopListByPoi(
    queryShopListByPoiReq: QueryShopListByPoiReq,
  ): Promise<KbPageResult<KBShopSearchVO>>;

  queryShopKbCode(queryKbCodeReq: QueryKbCodeReq): Promise<KbResult<ShopKbCodeDto>>;

  verifyCode(verifyCodeReq: VerifyCodeReq): Promise<KbResult<VerifyCodeVO>>;
}

/**
 * 预警操作服务
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/02
 */
export interface AlarmOperateService {
  /**
   * 新增预警
   * @param request
   * @return
   */
  add(request: AlarmOperateRequest): Promise<KbResult<string>>;

  /**
   * 预警消息归档
   * @param alarmId
   * @return
   */
  archive(alarmId: number): Promise<KbResult<void>>;

  /**
   * 忽略预警
   * @param alarmId
   * @return
   */
  ignore(alarmId: number, ignoreStatus: number): Promise<KbResult<void>>;

  /**
   * 预警消息触达
   * @param alarmId
   * @return
   */
  sendMessage(alarmId: number): Promise<KbResult<void>>;
}

/**
 * 预警数据读取服务类
 * @author: boxian
 * @email: <EMAIL>
 * @date: 2022/09/05
 */
export interface AlarmQueryService {
  /**
   * 预警类目数据获取
   * @param userId
   * @return
   */
  fetchCategoryCount(userId: number): Promise<KbResult<AlarmCategoryCountResponse[]>>;

  /**
   * 预警总量数据获取
   * @param userId
   * @return
   */
  fetchCount(userId: number): Promise<KbResult<AlarmTotalCountResponse>>;

  /**
   * 预警详情列表查询
   * @param request
   * @return
   */
  listDetails(request: AlarmDetailQueryRequest): Promise<KbPageResult<AlarmDetailResponse>>;

  /**
   * 预警详情查询
   * @param id
   * @return
   */
  queryById(id: number): Promise<KbResult<AlarmDetailResponse>>;
}

export interface AssetStuffTemplateService {
  /**
   * 增加物料模版
   * @param request
   * @return
   */
  addTemplate(request: OperateRequest<StuffTemplate>): Promise<KbResult<number>>;

  /**
   * 物料模版复制
   * @param request
   * @return
   */
  copyTemplate(request: OperateRequest<StuffCopyTemplate>): Promise<KbResult<number>>;

  /**
   * 编辑物料模板
   * @param request
   * @return
   */
  editTemplate(request: OperateRequest<StuffTemplate>): Promise<KbResult<number>>;

  /**
   * 根据业务业源查询模板
   * @param request
   * @return
   */
  getOperatorInfo(request: Request<StuffTemplateCondition>): Promise<KbResult<OperatorInfo>>;

  /**
   * 分页查询物料模版列表
   * @param pageQueryRequest
   * @return
   */
  query(
    pageQueryRequest: PageQueryRequest<StuffTemplateQueryCondition>,
  ): Promise<PageQueryResult<StuffTemplateDto[]>>;

  /**
   * 查询物料模版详情
   * @param id  模板ID
   * @return
   */
  queryInfo(id: Request<number>): Promise<KbResult<StuffTemplateDto>>;

  /**
   * 根据业务业源查询模板
   * @param request
   * @return
   */
  queryStuffTemplateSelection(
    request: Request<StuffTemplateCondition>,
  ): Promise<KbResult<StuffTemplateDto[]>>;

  /**
   * 物料模版状态更新
   * INVALID 失效
   * EFFECTIVE 生效
   * @param request
   * @return
   */
  upTemplateStatus(request: OperateRequest<StuffTemplateUpStatus>): Promise<KbResult<void>>;
}

export interface KbCodeService {
  /**
   * PC端创建口碑二维码
   * https://kbservcenter.alipay.com/proxy.json
   * 	    mappingValue=kbasset.createKBCode
   * 	    bindType=IN_SHOP_BINDING&
   * 	    remark=%E6%96%B0%E9%B9%8F%E6%B5%8B%E8%AF%95%E7%94%9F%E6%88%90&
   * 	    templateType=STICKER&
   * 	    templateNickName=KOUBEI_QRCODE_480&
   * 	    quantity=2&
   * 	    dataId=&
   * @param createReq req
   * @return str
   */
  create(createReq: CreateQrCodeReq): Promise<KbResult<string>>;

  /**
   * 生成记录
   *      https://kbservcenter.alipay.com/proxy.json?
   *      mappingValue=kbasset.pageQueryKBCodeBatch
   *      &pageNum=1
   *      &pageSize=10
   * @param req req
   * @return data
   */
  pageQueryKBCodeBatch(req: CodeBatchReq): Promise<KbPageResult<StuffQrcodeApplyDto>>;

  /**
   * 绑定门店
   *      https://kbservcenter.alipay.com/proxy.json?
   *      mappingValue=kbasset.pageQueryKBCodeBindInfo
   *      &bindTargetId=2022090800077000000038701516
   *      &merchantPrincipalId=2088542204453353
   *      &gmtModifiedStart=2022-12-01
   *      &gmtModifiedEnd=2022-12-08
   *      &pageNum=1
   *      &pageSize=10
   * @param req req
   * @return data
   */
  pageQueryKBCodeBindInfo(req: BindBatchReq): Promise<KbPageResult<QrcodeBindRelationDto>>;

  /**
   * 查看绑定门店的数据 查询列表
   *      https://kbservcenter.alipay.com/proxy.json?
   *      mappingValue=kbasset.pageQueryKBCodeBindTargetCodes
   *      &bindTargetId=2022102400077000000039800769
   *      &pageNum=1
   *      &pageSize=10
   * @param req
   * @return
   */
  pageQueryKBCodeBindTargetCodes(req: BindTargetCodesReq): Promise<KbPageResult<BindQrCodeVO>>;

  /**
   * 绑定商户
   *      https://kbservcenter.alipay.com/proxy.json?
   *      mappingValue=kbasset.pageQueryKBCodePidBindInfo
   *      &merchantPrincipalId=2088542204453353
   *      &gmtModifiedStart=2022-12-01
   *      &gmtModifiedEnd=2022-12-08
   *      &pageNum=1
   *      &pageSize=10
   * @param req req
   * @return data
   */
  pageQueryKBCodePidBindInfo(req: BindPidBatchReq): Promise<KbPageResult<QrcodeBindRelationDto>>;

  /**
   * 未绑定
   *
   *      https://kbservcenter.alipay.com/proxy.json?
   *      mappingValue=kbasset.pageQueryKBCodeUnbindBatch
   *      &pageNum=1
   *      &pageSize=10
   *      &batchId=123
   *      &gmtCreateStart=2022-12-01
   *      &gmtCreateEnd=2022-12-03
   * @return req
   */
  pageQueryKBCodeUnbindBatch(req: UnbindBatchReq): Promise<KbPageResult<StuffQrcodeApplyDto>>;

  /**
   * 查询列表
   *      https://kbservcenter.alipay.com/proxy.json?
   *      mappingValue=kbasset.pageQueryKBCodeUnbindCode
   *      &pageNum=1
   *      &pageSize=200
   *      &batchId=2793651
   * @param req req
   * @return data
   */
  pageQueryKBCodeUnbindCode(req: UnBindCodeReq): Promise<KbPageResult<QrCodeVO>>;

  /**
   * 蚂蚁侧是上传文件 -> 改到集团侧是直接传数据
   * Request URL:
   *      https://kbservcenter.alipay.com/sale/kbcode/bindCodeExcelUpload.json
   *      bindScene: KOUBEI_SHOP_CODEC
   *      templateNickName: KOUBEI_QRCODE_482
   *      largeMode:  undefined
   *      file:  (binary)
   * @param parseTextReq req
   * @return data
   */
  parseText(parseTextReq: ParseTextReq): Promise<KbResult<string>>;

  /**
   * 创建口碑码时的模版
   * https://kbservcenter.alipay.com/proxy.json?
   *      mappingValue=kbasset.queryKBCodeStuffTemplate
   *      &bizSource=KOUBEI_CODE
   *      &isKaOperator=N
   * @return data
   */
  queryCreateTemplate(): Promise<KbResult<KbCodeTemplateConfigVO[]>>;
}

export interface TestQueryService {
  genQrCode(text: string, secne: string, caption: string, expires: number): Promise<string>;

  invoke(accountId: string, serviceName: string, functionName: string, arg: any): Promise<string>;

  queryById(operatorId: string): Promise<string>;

  queryMerchantCoreSingle(partnerId: string): Promise<string>;

  queryShop(kbShopId: string): Promise<string>;

  queryShopIds(kbShopIds: string[]): Promise<string>;

  queryShopVide(kbShopId: string): Promise<ShopViewDTO>;

  queryUserJobs(str: string): Promise<string>;

  search(request: string): Promise<string>;

  testAdd(name: string): Promise<string>;

  testQuery(id: number): Promise<string>;
}

export interface SalesExecuteService {
  /**
   * 销售执行处理蚂蚁侧同步过来的数据
   * @param tableName 表名
   * @param operator CURD
   * @param data 数据
   * @return boolean
   */
  executeData(tableName: string, operator: string, data: string): Promise<boolean>;

  testTriple(scene: string, reqStr: string): Promise<string>;
}

/**
 * 通用的接口
 * <AUTHOR>
 */
export interface KbCodeCommonService {
  /**
   * 查商户
   *
   * https://kbservcenter.alipay.com/sale/merchant/
   *  queryByName.json?
   *  keyword=%E6%B5%8B%E8%AF%95&
   *  size=100
   * @param req req
   * @return data
   */
  queryByName(req: QueryByNameReq): Promise<CascaderVO[]>;

  /**
   * 查门店
   *
   * https://kbservcenter.alipay.com/sale/kbcode/
   *      searchAgentShops.json?
   *      shopName=%E6%B5%8B%E8%AF%95
   * @param req req
   * @return data
   */
  searchAgentShops(req: SearchAgentShopReq): Promise<KbCodeShopVO[]>;
}

/**
 * 口碑门店码管理接口
 * <AUTHOR> <EMAIL>
 * @version 1.0.0
 * @create 2023/1/28 16:49
 */
export interface KbShopQrCodeService {
  /**
   * 判断门店是否为快消行业门店
   * @param shopReq
   * @return
   */
  isRetailShop(shopReq: IsRetailShopReq): Promise<KbResult<boolean>>;

  /**
   * 展示商户码、收款二维码；（阿里本地通PC和销售工作台PC使用）
   *
   * 内部逻辑：先进行码查询，有则直接返回，无则创建（并绑定）后返回；
   * @param qrCodeReq
   * @return
   */
  showKbShopQrCode(qrCodeReq: ShowKbShopQrCodeReq): Promise<KbResult<string>>;

  /**
   * 展示店铺收款二维码和门店宣传码；（阿里本地通APP H5页面使用）
   *
   * 内部逻辑：先进行码查询，有则直接返回，无则创建（并绑定）后返回；
   * @param qrCodeH5Req
   * @return
   */
  showKbShopQrCodeH5(qrCodeH5Req: ShowKbShopQrCodeH5Req): Promise<KbResult<ShowKbShopQrCodeH5VO>>;
}

export interface AssetMaterialPaveService {
  /**
   * 批量创建物料铺设
   * kbasset.spiStuffPaveService.batchCreateStuffPave
   * @param request 创建请求
   * @return
   */
  batchCreateStuffPave(request: CreateStuffPaveRequest): Promise<KbResult<boolean>>;

  doSth(ids: number[], table: string): Promise<void>;

  /**
   * PC 物料验收页面显示验收详情
   * kbasset.spiPaveQueryService.queryPaveCheckDetailInfoByCheckId
   * 这个接口从 kbservcenter 上面 json：asset/stuffCheckDetail.json 接口迁移过来
   * @param checkId
   * @return
   */
  queryPaveCheckDetailInfoByCheckId(checkId: string): Promise<KbResult<PaveCheckAuditDetailVO>>;

  /**
   * 根据 paveId查询铺设详情
   * kbasset.spiPaveQueryService.queryPaveDetailInfoByPaveId
   * @param paveId
   * @return
   */
  queryPaveDetailInfoByPaveId(paveId: string): Promise<KbResult<PaveDetailInfoVO>>;

  /**
   * 根据登录人分页查询铺设列表
   * kbasset.spiPaveQueryService.queryPaveListByLoginUser
   * @param request
   * @return
   */
  queryPaveListByLoginUser(
    request: PageQueryPaveRequest,
  ): Promise<PageQueryResult<PaveDetailInfoVO[]>>;

  /**
   * 查询铺设主体
   * kbasset.spiPaveQueryService.queryPaveTarget
   * @param request
   * @return
   */
  queryPaveTarget(request: QueryTargetRequest): Promise<KbResult<PaveTargetVO>>;

  /**
   * 查询物料铺设的模板
   * kbasset.spiStuffPaveTemplateQueryService.queryPaveTemplate
   * @param request
   * @return
   */
  queryPaveTemplate(request: QueryTemplateRequest): Promise<KbResult<PaveTemplateVO[]>>;
}

export interface OssService {
  /**
   * 批量获取访问权限链接,可设置参数
   * @param imgs
   * @return
   */
  generateSignedUrl(imgs: string[]): Promise<{ [index: string]: string }>;

  /**
   * 获取访问权限链接，可设置参数
   * @param img
   * @return
   */
  generateSignedUrlSingle(img: string): Promise<string>;

  /**
   * 获取访问权限链接，可设置参数，送审用
   * @param img
   * @return
   */
  generateSignedUrlSingleForApproval(img: string): Promise<string>;

  /**
   * 获取oss签名
   * @return
   */
  getOssSignature(): Promise<OssSignatureCO>;

  /**
   * 上传文件
   * @param fileName
   * @return 可访问的url
   */
  putFileToOss(fileName: string): Promise<string>;
}

/**
 * * 弃用---改用ExportFileGateway
 * <AUTHOR>
 * @version Id: ExportFileGatewayService.java, v 0.1 2022/12/27 20:25 shengwei Exp $$
 */
export interface ExportFileGatewayService {
  /**
   * 文件导出
   * @param request
   * @return
   */
  exportFile(request: ExportFileGatewayRequest): Promise<KbResult<string>>;

  /**
   * 获得导出文件结果url
   * @param exportId
   * @return
   */
  getExportFileResultUrl(exportId: number): Promise<KbResult<string>>;

  /**
   * 分页查询导出记录
   * @param request
   * @return
   */
  pageQueryExportFileRecord(
    request: ExportFilePageQueryRecordGatewayRequest,
  ): Promise<KbPageResult<ExportFileRecordVO>>;
}

/**
 * <AUTHOR>
 * @version Id: ImportFileGatewayService.java, v 0.1 2022/12/27 20:25 shengwei Exp $$
 */
export interface ImportFileGatewayService {
  /**
   * 获得导入文件结果url
   * @param importId
   * @return
   */
  getImportFileResultUrl(importId: number): Promise<KbResult<string>>;

  /**
   * 获得导入文件成功结果url
   * @param importId
   * @return
   */
  getImportFileSuccessResultUrl(importId: number): Promise<KbResult<string>>;

  /**
   * 获得导入文件结果url
   * @param bizCode
   * @return
   */
  getImportTemplateFileResultUrl(bizCode: string): Promise<KbResult<string>>;

  /**
   * 文件上传
   * @param request
   * @return
   */
  importFile(request: ImportFileGatewayRequest): Promise<KbResult<string>>;

  /**
   * 分页查询导入记录
   * @param request
   * @return
   */
  pageQueryImportFileRecord(
    request: ImportFilePageQueryRecordGatewayRequest,
  ): Promise<KbPageResult<ImportFileRecordVO>>;
}

/**
 * 代运营任务查询
 * <AUTHOR>
 * @version : AgentOperationTaskQueryBucFacade.java, v 0.1 2023年05月27日 下午2:47 LiShu Exp $
 */
export interface AgentOperationTaskQueryBucFacade {
  /**
   * 查看代运营商户任务列表
   * @param agentOperationMerchantListRequest
   * @return
   */
  queryAgentOperationMerchantTaskList(
    agentOperationMerchantListRequest: AgentOperationMerchantTaskListRequest,
  ): Promise<GatewayResult<CommonPageData<AgentOperationMerchantTaskDTO>>>;

  /**
   * 查看代运营门店任务列表
   * @param agentOperationShopTaskListRequest
   * @return
   */
  queryAgentOperationShopTaskList(
    agentOperationShopTaskListRequest: AgentOperationShopTaskListRequest,
  ): Promise<GatewayResult<CommonPageData<AgentOperationShopTaskDTO>>>;

  /**
   * 查询代运营统计数据
   * @param agentOperationStatisticsRequest
   * @return
   */
  queryAgentOperationStatistics(
    agentOperationStatisticsRequest: AgentOperationStatisticsRequest,
  ): Promise<GatewayResult<AgentOperationStatisticsDTO>>;

  /**
   * 查看代运营任务详情
   * 目前门店任务详情和基建任务详情统一使用这一个接口进行业务承载
   * 方便前端统一使用相同的组件模版内部按照场景进行业务扩展
   * @param agentOperationTaskDetailRequest
   * @return
   */
  queryAgentOperationTaskDetail(
    agentOperationTaskDetailRequest: AgentOperationTaskDetailRequest,
  ): Promise<GatewayResult<AgentOperationTaskDetailDTO>>;

  /**
   * 查询代运营绩效核心数据
   * @param agentOperationStatisticsRequest
   * @return
   */
  queryAgentPerformanceData(
    agentOperationStatisticsRequest: AgentOperationPerformanceStatisticsRequest,
  ): Promise<GatewayResult<AgentPerformanceStatisticsDTO>>;

  /**
   * 查询门店异动数量等任务
   * @param agentOperationShopChangeRequest
   * @return
   */
  queryAgentShopChange(
    agentOperationShopChangeRequest: AgentOperationShopChangeRequest,
  ): Promise<GatewayResult<AgentOperationShopChangeDTO>>;

  /**
   * 商户详情查询
   * @param agentOperationMerchantDetailRequest
   * @return
   */
  queryMerchantDetail(
    agentOperationMerchantDetailRequest: AgentOperationMerchantDetailRequest,
  ): Promise<GatewayResult<AgentOperationMerchantDetailDTO>>;

  /**
   * 代运营短链服务
   * @param agentOperationShortLinkRequest
   * @return
   */
  queryShortLink(
    agentOperationShortLinkRequest: AgentOperationShortLinkRequest,
  ): Promise<GatewayResult<AgentOperationShortLinkDTO>>;
}

/**
 * <AUTHOR>
 * @date 2023/06/15
 */
export interface SmartWordYundingQueryFacade {
  /**
   * 模板实例召回
   * @param request
   * @return
   */
  recall(
    request: SmartWordTemplateInstRecallRequst,
  ): Promise<GatewayResult<BaseTemplateInstRecallDTO[]>>;
}

/**
 * 服务端配置查询服务
 * <AUTHOR>
 * @date 2023/07/11/20:06
 */
export interface BizConfigGatewayService {
  /**
   * 查询服务商工作台切流配置
   * @param request
   * @return
   */
  queryForAppMigration(
    request: GrayConfigQueryGwRequest,
  ): Promise<GwBaseResultDTO<BizGrayConfigGwResultVO>>;
}

/**
 * <AUTHOR>
 * @date 2023/06/05
 */
export interface SmartWordGatewayService {
  /**
   * 匹配智能话术
   * @param request
   * @return
   * @deprecated 这个接口返回值太定制了, 无法支撑升级诉求, 需要新开一个V2接口
   */
  match(request: SmartWordMatchRequest): Promise<GwBaseResultDTO<string>>;

  /**
   * 匹配智能话术
   * @param request
   * @return
   */
  matchV2(request: SmartWordMatchRequest): Promise<GwBaseResultDTO<SmartWordMatchResultVO>>;
}

/**
 * <AUTHOR>
 * @version $Id: VisitCategoryItemGateway.java, v 0.1 2023年09月20日 15:45 荒丘 Exp $
 */
export interface VisitCategoryItemGateway {
  /**
   * 拜访事项状态切换接口
   * @param request
   * @return
   */
  changeCategoryItemStatus(
    request: VisitCategoryItemStatusChangeRequest,
  ): Promise<GatewayResult<any>>;

  /**
   * 拜访分类事项创建接口
   * @param request
   * @return
   */
  createCategoryItem(request: VisitCategoryItemCreateRequest): Promise<GatewayResult<any>>;

  /**
   * 拜访分类事项删除接口
   * @param request
   * @return
   */
  deleteCategoryItem(request: VisitCategoryItemDeleteRequest): Promise<GatewayResult<any>>;

  /**
   * 拜访分类事项修改接口
   * @param request
   * @return
   */
  modifyCategoryItem(request: VisitCategoryItemModifyRequest): Promise<GatewayResult<any>>;

  /**
   * 拜访分类事项详情查询接口
   * @param request
   * @return
   */
  queryCategoryItemDetail(
    request: VisitCategoryItemDetailQueryRequest,
  ): Promise<GatewayResult<VisitCategoryVO>>;

  /**
   * 拜访分类事项分页查询接口
   * @param request
   * @return
   */
  queryCategoryItemList(
    request: VisitCategoryItemPageQueryRequest,
  ): Promise<GatewayResult<VisitCategoryEleVO[]>>;
}

/**
 * 公共基础配置接口
 * <AUTHOR>
 * @version v 0.1
 * @classname VisitCommonGateway
 * @date 2023-09-21 20:02
 */
export interface VisitCommonGateway {
  /**
   * 解密手机号
   * @param request
   * @return
   */
  decodePhone(request: VisitPhoneDecodeRequest): Promise<GatewayResult<VisitPhoneDecodeVO>>;

  /**
   * 拜访方式查询接口
   * @param request
   * @return
   */
  getVisitChannelList(request: VisitBaseRequest): Promise<GatewayResult<VisitBaseConfigInfoVO[]>>;

  /**
   * 拜访主体类型查询接口
   * @param request
   * @return
   */
  getVisitTargetTypeList(
    request: VisitBaseRequest,
  ): Promise<GatewayResult<VisitBaseConfigInfoVO[]>>;

  /**
   * 灰度控制
   * @param request
   * @return
   */
  greyConfig(request: VisitBaseRequest): Promise<GatewayResult<{ [index: string]: any }>>;
}

/**
 * <AUTHOR>
 * @version $Id: VisitConfigGateway.java, v 0.1 2023年09月20日 14:09 荒丘 Exp $
 */
export interface VisitConfigGateway {
  /**
   * 切换拜访配置状态
   * @param request
   * @return
   */
  changeConfigStatus(request: VisitConfigStatusChangeRequest): Promise<GatewayResult<any>>;

  /**
   * 创建拜访配置
   * @param request
   * @return
   */
  createConfig(request: VisitConfigCreateRequest): Promise<GatewayResult<any>>;

  /**
   * 删除拜访配置
   * @param request
   * @return
   */
  deleteConfig(request: VisitConfigDeleteRequest): Promise<GatewayResult<any>>;

  /**
   * 修改拜访配置
   * @param request
   * @return
   */
  modifyConfig(request: VisitConfigModifyRequest): Promise<GatewayResult<any>>;

  /**
   * 分页查询拜访配置列表
   * @param request
   * @return
   */
  pageQueryConfigList(
    request: VisitConfigPageQueryRequest,
  ): Promise<GatewayResult<Pagination<VisitConfigEleVO[]>>>;

  /**
   * 查询拜访配置详情
   * @param request
   * @return
   */
  queryConfigDetail(request: VisitConfigDetailQueryRequest): Promise<GatewayResult<VisitConfigVO>>;
}

/**
 * 拜访计划
 * <AUTHOR>
 * @version v 0.1
 * @classname VisitPlanGateway
 * @date 2023-09-20 11:02
 */
export interface VisitPlanGateway {
  /**
   * 主动取消拜访计划
   * -重复取消不会抛出异常
   * -取消已完成拜访计划会抛出异常
   * @param request
   * @return
   */
  cancelPlan(request: VisitPlanCancelRequest): Promise<GatewayResult<any>>;

  /**
   * 单独创建拜访计划
   * @param request
   * @return
   */
  createPlan(request: VisitPlanCreateRequest): Promise<GatewayResult<string>>;

  /**
   * 拜访计划分页查询请求
   * @param request
   * @return
   */
  pageQueryPlan(
    request: VisitPlanPageQueryRequest,
  ): Promise<GatewayResult<Pagination<VisitPlanInfoVO[]>>>;
}

/**
 * <AUTHOR>
 * @version $Id: VisitRecordGateway.java, v 0.1 2023年09月20日 16:23 荒丘 Exp $
 */
export interface VisitRecordGateway {
  /**
   * 拜访记录创建接口
   * @param request
   * @return
   */
  createRecord(
    request: VisitRecordCreateRequest,
  ): Promise<GatewayResult<VisitRecordCreateResultVO>>;

  /**
   * 拜访记录修改接口
   * @param request
   * @return
   */
  modifyRecord(
    request: VisitRecordModifyRequest,
  ): Promise<GatewayResult<VisitRecordModifyResultVO>>;

  /**
   * 拜访记录填写页面前置渲染接口
   * @param request
   * @return
   */
  prepare(request: VisitRecordPrepareRequest): Promise<GatewayResult<VisitRecordPrepareResultVO>>;

  /**
   * 用户拜访配置决策接口
   * @param request
   * @return
   */
  queryUserConfigList(
    request: VisitConfigDecisionRequest,
  ): Promise<GatewayResult<VisitConfigDecisionResultVO>>;
}

/**
 * 拜访记录查询
 * <AUTHOR>
 * @version v 0.1
 * @classname VisitRecordQueryGateway
 * @date 2023-09-20 11:12
 */
export interface VisitRecordQueryGateway {
  /**
   * 拜访记录导出请求
   * @param request
   * @return
   */
  exportRecord(request: ExportFileGatewayRequest): Promise<GatewayResult<string>>;

  /**
   * 拜访记录指定主体查询请求
   * @param request
   * @return
   */
  pageQueryByTarget(
    request: VisitRecordTargetQueryRequest,
  ): Promise<GatewayResult<Pagination<VisitRecordInfoVO[]>>>;

  /**
   * 拜访记录分页查询请求
   * @param request
   * @return
   */
  pageQueryRecord(
    request: VisitRecordPageQueryRequest,
  ): Promise<GatewayResult<Pagination<VisitRecordInfoVO[]>>>;

  /**
   * 查询意向选项
   * @return
   */
  queryIntentionList(request: VisitIntentionRequest): Promise<GatewayResult<IntentionOptionVO[]>>;

  /**
   * 拜访记录卡片信息查询
   * @param request
   * @return
   */
  queryTargetDigest(
    request: VisitRecordCardQueryRequest,
  ): Promise<GatewayResult<VisitRecordCardVO>>;

  /**
   * 拜访记录详情查询请求
   * @param request
   * @return
   */
  queryVisitRecordDetail(
    request: VisitRecordDetailQueryRequest,
  ): Promise<GatewayResult<VisitRecordDetailVO>>;
}

/**
 * <AUTHOR>
 * @version $Id: VisitTargetGateway.java, v 0.1 2023年09月20日 16:20 荒丘 Exp $
 */
export interface VisitTargetGateway {
  /**
   * 拜访主体详情查询
   * @param request
   * @return
   */
  queryTargetDetail(request: VisitTargetDetailQueryRequest): Promise<GatewayResult<VisitTargetVO>>;
}

/**
 * <AUTHOR>
 * @date 2023/07/03
 */
export interface SmartWordTemplateInfoMngGatewayService {
  /**
   * 创建智能话术
   * @param request
   * @return
   */
  create(request: SmartWordTemplateInfoSaveRequest): Promise<GatewayResult<string>>;

  /**
   * 禁用智能话术
   * @param disableRequest
   * @return
   */
  disable(disableRequest: SmartWordTemplateInfoDisableRequest): Promise<GatewayResult<string>>;

  /**
   * 启用智能话术
   * @param enableRequest
   * @return
   */
  enable(enableRequest: SmartWordTemplateInfoEnableRequest): Promise<GatewayResult<string>>;

  /**
   * 修改智能话术
   * @param request
   * @return
   */
  modify(request: SmartWordTemplateInfoSaveRequest): Promise<GatewayResult<string>>;
}

/**
 * <AUTHOR>
 * @date 2023/07/03
 */
export interface SmartWordTemplateInfoQueryGatewayService {
  /**
   * 查询c33智能话术配置
   * @param request
   * @return
   */
  queryDetail(
    request: SmartWordTemplateInfoDetailQueryRequest,
  ): Promise<GatewayResult<SmartWordTemplateInfoDTO>>;

  /**
   * 查询指标条件
   * @return
   */
  queryIndicatorCondition(
    request: SmartWordIndicatorConditionQueryRequest,
  ): Promise<GatewayResult<IndicatorDTO[]>>;

  /**
   * 查询C33标签配置项
   * @param request
   * @return
   */
  queryLabelConditions(
    request: SmartWordGroupRuleConfigQueryRequest,
  ): Promise<GatewayResult<RuleGroupConfigDTO[]>>;

  /**
   * 列表查询
   * @param request
   * @return
   */
  queryList(
    request: SmartWordTemplateInfoQueryRequest,
  ): Promise<GatewayResult<Pagination<SmartWordTemplateInfoDTO[]>>>;
}

/**
 * 陪访记录对外接口
 * <AUTHOR>
 * @version v 0.1
 * @classname VisitParticipantGateway
 * @date 2023-11-09 14:24
 */
export interface VisitParticipantGateway {
  /**
   * 陪访记录评价
   * @param request
   * @return
   */
  appraiseParticipant(
    request: VisitParticipantRecordAppraiseRequest,
  ): Promise<GatewayResult<boolean>>;

  /**
   * 陪访记录分页查询请求
   * @param request
   * @return
   */
  pageQueryRecord(
    request: VisitParticipantRecordPageQueryRequest,
  ): Promise<GatewayResult<Pagination<VisitRecordInfoVO[]>>>;

  /**
   * 陪访记录详情
   * @param request
   * @return
   */
  queryVisitParticipantDetail(
    request: VisitParticipantRecordDetailQueryRequest,
  ): Promise<GatewayResult<VisitRecordDetailVO>>;
}

/**
 * 外呼主体相关接口
 * 展业场景使用
 * <AUTHOR>
 * @version $Id: CallTargetGatewayService.java, v 0.1 2023年08月16日 11:08 荒丘 Exp $
 */
export interface CallTargetGatewayService {
  /**
   * 外呼主体数量统计查询接口
   *
   * https://aliyuque.antfin.com/kbfhy/fh9g17/xxo5h38s9dukl0yy#V4ecu
   * @param request
   * @return
   */
  countTarget(request: CallTargetCountRequest): Promise<GatewayResult<CallTargetCountVO[]>>;

  /**
   * 展业场景使用(查询销售小二有权限管理的主体列表)
   * 外呼主体分页查询接口
   *
   * https://aliyuque.antfin.com/kbfhy/fh9g17/xxo5h38s9dukl0yy#jaNOQ
   * @param request
   * @return
   */
  pageQueryTargetList(
    request: CallTargetPageQueryRequest,
  ): Promise<GatewayResult<Pagination<CallTargetVO[]>>>;

  /**
   * 查出主体全部联系人信息
   * @param request
   * @return
   */
  queryTargetContactInfo(
    request: CallTargetContactPersonQueryRequest,
  ): Promise<GatewayResult<CallTargetContactInfoVO[]>>;

  /**
   * 根据电话号码搜索主体列表
   * @param request
   * @return
   */
  searchTargetByPhone(
    request: CallTargetSearchByPhoneRequest,
  ): Promise<GatewayResult<Pagination<CallTargetVO[]>>>;
}

/**
 * <AUTHOR>
 * @date 2023/08/11
 */
export interface CallCenterManageGatewayService {
  /**
   * 阿里云云呼中心通用请求接口
   * @param action
   * @param request
   * @return
   */
  commonRequest(action: string, request: string): Promise<GatewayResult<string>>;

  /**
   * 保存通话记录
   * @param request
   * @return
   */
  saveCallRecord(request: CallRecordSaveRequest): Promise<GatewayResult<string>>;
}

/**
 * <AUTHOR>
 * @date 2023/08/11
 */
export interface CallCenterQueryGatewayService {
  /**
   * 获取未填写拜访信息的通话记录
   * @param request
   * @return
   */
  getCallRecordMissingVisit(
    request: CallRecordMissingVisitQueryRequest,
  ): Promise<GatewayResult<string[]>>;

  /**
   * 通话记录统计信息查询
   * @param request
   * @return
   */
  getCallRecordStatsInfo(
    request: CallRecordStatsInfoQueryRequest,
  ): Promise<GatewayResult<CallRecordStatsInfoVO>>;

  /**
   * 按主体查询需要记拜访的通话记录
   * @param request
   * @return
   */
  getTargetCallRecordMissingVisit(
    request: CallRecordMissVisitQueryRequest,
  ): Promise<GatewayResult<CallRecordExistQueryResultVO>>;

  /**
   * 获取当前用户的所有实例
   * @return
   */
  getUserInstances(
    request: CallUserInstanceQueryRequest,
  ): Promise<GatewayResult<CallInstanceInfoVO[]>>;

  /**
   * 挂机是否需要使用满意度推送判断接口
   * @param request
   * @return
   */
  useLaunchSurveyToReleaseCall(
    request: CallUseLaunchSurveyToReleaseCallRequest,
  ): Promise<GatewayResult<CallUseLaunchSurveyToReleaseCallVO>>;
}

/**
 * 给C31业务的bd网关接口
 * 与 {@linkplain CallYunDingGatewayService} 是相同的定位的
 * <AUTHOR>
 * @version $Id: CallGatewayService.java, v 0.1 2023年05月17日 17:55 荒丘 Exp $
 */
export interface CallGatewayService {
  /**
   * 外呼前置查询接口
   * @return
   */
  prepare(request: OutCallPrepareRequest): Promise<GwBaseResultDTO<OutCallPrepareResultVO>>;

  /**
   * 外呼阶段推进（响铃、挂机等）
   * @param request
   * @return
   */
  progressOutCall(
    request: OutCallProgressRequest,
  ): Promise<GwBaseResultDTO<OutCallProgressResultVO>>;

  /**
   * 电话面板查询接口
   * @param request
   * @return
   */
  queryCallBlockInfo(request: CallBlockQueryRequest): Promise<GwBaseResultDTO<CallBlockInfoVO>>;

  /**
   * 查询或者注册指定外呼渠道的账号
   * @param request
   * @return
   */
  queryOrRegisterAgent(request: CallAgentManageRequest): Promise<GwBaseResultDTO<CallAgentVO>>;

  /**
   * 查询通话记录简略信息
   * 用户前端发起外呼成功之后,查询电话状态使用
   * @param request
   * @return
   */
  queryRecord(request: CallRecordQueryRequest): Promise<GwBaseResultDTO<CallRecordSimpleVO>>;

  /**
   * 查询通话记录简略信息
   * 用户前端发起外呼成功之后,查询电话状态使用
   * @param request
   * @return
   */
  queryRecordDetail(
    request: CallRecordDetailQueryRequest,
  ): Promise<GwBaseResultDTO<CallRecordDetailVO>>;

  /**
   * 愿意点亮操作
   * @return
   */
  reportOutCallAdditionalActivity(
    request: OutCallAdditionalActivityRequest,
  ): Promise<GwBaseResultDTO<any>>;

  /**
   * 上报外呼展业结果
   * 愿意购买场景时,会做预下单操作:落展业任务,真实下单需要前端调用submit接口
   * @return
   */
  reportOutCallResult(request: OutCallResultReportRequest): Promise<GwBaseResultDTO<any>>;

  /**
   * 保存Bd小二的电销电话
   * @param request
   * @return
   */
  saveBdSalesPhone(request: BdSalesPhoneSaveRequest): Promise<GwBaseResultDTO<any>>;

  /**
   * 通话记录搜索列表查询接口
   * @param request
   * @return
   */
  searchCallRecord(
    request: CallRecordSearchRequest,
  ): Promise<GwBaseResultDTO<Pagination<CalRecordSearchListEleVO[]>>>;

  /**
   * 通话记录页面外呼组主体筛选组件接口
   * @param request
   * @return
   */
  searchTargetInfo(
    request: CallTargetInfoSearchRequest,
  ): Promise<GwBaseResultDTO<Pagination<CallTargetVO[]>>>;

  /**
   * 发起外呼(打电话)
   * @return
   */
  startOutCall(request: OutCallStartRequest): Promise<GwBaseResultDTO<OutCallStartResultVO>>;

  /**
   * 外呼展业结果为购买时的真实下单接口
   * @param request
   * @return
   */
  submitOrder(request: OutCallResultSubmitOrderRequest): Promise<GwBaseResultDTO<any>>;
}

/**
 * <AUTHOR>
 * @version $Id: CallShortNumberGateway.java, v 0.1 2023年12月13日 16:21 荒丘 Exp $
 */
export interface CallShortNumberGateway {
  /**
   * 云鼎接口未拨打时上报-取消隐私小号绑定记录
   * @param request
   * @return
   */
  cancelTargetCallRecordVisit(
    request: CallShortNumberCancelRequest,
  ): Promise<GatewayResult<boolean>>;

  /**
   * 待补齐小号外呼记录查询
   * @param request
   * @return
   */
  getBindRecordMissingVisit(
    request: BaseRequest,
  ): Promise<GatewayResult<CallBindRecordMissingResultVO>>;

  /**
   * 上报隐私小号绑定记录
   * @param request
   * @return
   */
  reportBindRecord(request: CallBindRecordReportRequest): Promise<GatewayResult<any>>;
}

/**
 * 给C33业务的云鼎网关接口
 * 与 {@linkplain CallGatewayService} 是相同的定位的
 * <AUTHOR>
 * @version $Id: CallYunDingGatewayService.java, v 0.1 2024年01月23日 17:25 荒丘 Exp $
 */
export interface CallYunDingGatewayService {
  /**
   * 外呼前置查询接口
   * @return
   */
  prepare(request: OutCallPrepareRequest): Promise<GatewayResult<OutCallPrepareResultVO>>;

  /**
   * 外呼阶段推进（响铃、挂机等）
   * @param request
   * @return
   */
  progressOutCall(request: OutCallProgressRequest): Promise<GatewayResult<OutCallProgressResultVO>>;

  /**
   * 发起外呼(打电话)
   * @return
   */
  startOutCall(request: OutCallStartRequest): Promise<GatewayResult<OutCallStartResultVO>>;
}

/**
 * getMeta: {"artifact":"com.alsc.kbt:alsc-kbt-intergration-toolkit-client:1.0.1.20240328-SNAPSHOT","canonicalName":"com.alsc.kbt.toolkit.enums.material.StuffTypeEnum"}
 *
 * Values:
 * - `BASIC` - 基础物料
 * - `ACTIVITY` - 活动物料
 * - `ACTUAL` - 设备物料
 * - `OTHER` - 其它物料
 * - `ACTIVITY_CODEMATERIAL` - 码物料
 * - `ACTIVITY_CODEMATERIAL_KBPAY`
 * - `ACTIVITY_CODEMATERIAL_MINI`
 * - `CODEMATERIAL_TEMP` - 码物料 @deprecated
 */
export type StuffTypeEnum =
  | 'BASIC'
  | 'ACTIVITY'
  | 'ACTUAL'
  | 'OTHER'
  | 'ACTIVITY_CODEMATERIAL'
  | 'ACTIVITY_CODEMATERIAL_KBPAY'
  | 'ACTIVITY_CODEMATERIAL_MINI'
  | 'CODEMATERIAL_TEMP';

/**
 * getMeta: {"artifact":"com.alsc.kbt:alsc-kbt-intergration-toolkit-client:1.0.1.20240328-SNAPSHOT","canonicalName":"com.alsc.kbt.toolkit.enums.material.StuffTemplateStatusEnum"}
 *
 * Values:
 * - `EFFECTIVE` - 生效中
 * - `INVALID` - 已失效
 * - `INIT` - 初始化
 * - `APPROVING` - 审批中
 * - `DISCARD`
 */
export type StuffTemplateStatusEnum = 'EFFECTIVE' | 'INVALID' | 'INIT' | 'APPROVING' | 'DISCARD';

/**
 * getMeta: {"artifact":"com.alsc.kbt:alsc-kbt-intergration-toolkit-client:1.0.1.20240328-SNAPSHOT","canonicalName":"com.alsc.kbt.toolkit.enums.visit.VisitFieldTypeEnum"}
 *
 * Values:
 * - `CHOOSE`
 * - `TEXT`
 * - `IMAGE`
 * - `FILE`
 */
export type VisitFieldTypeEnum = 'CHOOSE' | 'TEXT' | 'IMAGE' | 'FILE';

/**
 * getMeta: {"artifact":"com.alsc.kbt:alsc-kbt-intergration-toolkit-client:1.0.1.20240328-SNAPSHOT","canonicalName":"com.alsc.kbt.toolkit.enums.visit.VisitFieldOptionTypeEnum"}
 *
 * Values:
 * - `SINGLE_CHOOSE`
 * - `MUTI_CHOOSE`
 * - `NUMBER`
 * - `TEXT`
 * - `DATE`
 * - `IMAGE`
 * - `ONLY_PHOTO`
 * - `FILE`
 * - `EXCEL`
 */
export type VisitFieldOptionTypeEnum =
  | 'SINGLE_CHOOSE'
  | 'MUTI_CHOOSE'
  | 'NUMBER'
  | 'TEXT'
  | 'DATE'
  | 'IMAGE'
  | 'ONLY_PHOTO'
  | 'FILE'
  | 'EXCEL';
