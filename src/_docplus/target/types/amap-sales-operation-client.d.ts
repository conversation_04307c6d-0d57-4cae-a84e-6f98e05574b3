/* eslint-disable */
// Generated using typescript-generator version 2.32.889 on 2024-04-02 16:14:44.

import { IAction } from '@/types';

/**
 * 说明：代运营任务详情
 * <AUTHOR>
 * @version AgentOperationDetailRequest.java v 0.1 2024年03月06日 11:51 yuHao
 */
export interface AgentOperationDetailRequest extends BaseRequest, Serializable {
  /**
   * Getter method for property <tt>taskDetailScene</tt>.
   * @return property value of taskDetailScene
   */
  taskDetailScene: string;
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>taskFlowInstanceId</tt>.
   * @return property value of taskFlowInstanceId
   * @deprecated
   */
  taskFlowInstanceId: string;
}

export interface ResultDTO<T> extends BaseDTO<any> {
  result: boolean;
  code: string;
  message: string;
  version: string;
  timestamp: string;
  success: boolean;
  msgInfo: string;
  msgCode: string;
  traceId: string;
  data: T;
}

/**
 * 说明：任务详情
 * <AUTHOR>
 * @version AgentOperationDetailDTO.java v 0.1 2024年03月06日 11:50 yuHao
 */
export interface AgentOperationDetailDTO extends ToString {
  /**
   * Getter method for property <tt>taskFlowInstanceId</tt>.
   * @return property value of taskFlowInstanceId
   */
  taskFlowInstanceId: string;
  /**
   * Getter method for property <tt>agentOperationTaskJumpList</tt>.
   * @return property value of agentOperationTaskJumpList
   */
  agentOperationTaskJumpList: TaskJumpDTO[];
  /**
   * Getter method for property <tt>taskDetailDTOList</tt>.
   * @return property value of taskDetailDTOList
   */
  taskDetailDTOList: TaskDetailDTO[];
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>taskDetailScene</tt>.
   * @return property value of taskDetailScene
   */
  taskDetailScene: string;
}

/**
 * 说明：商户查询请求
 * <AUTHOR>
 * @version AgentOperationMerchantRelationListRequest.java v 0.1 2024年03月04日 15:35 yuHao
 */
export interface AgentOperationMerchantRelationListRequest extends PageRequest, Serializable {
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>source</tt>.
   * @return property value of source
   */
  source: string;
  /**
   * Getter method for property <tt>taskStatus</tt>.
   * @return property value of taskStatus
   */
  taskStatus: string;
  /**
   * Getter method for property <tt>commercializationQueryTags</tt>.
   * @return property value of commercializationQueryTags
   */
  commercializationQueryTags: string[];
  /**
   * Getter method for property <tt>shopBaseTaskFinishStatus</tt>.
   * @return property value of shopBaseTaskFinishStatus
   */
  shopBaseTaskFinishStatus: string;
  /**
   * Getter method for property <tt>advertisementTaskStatus</tt>.
   * @return property value of advertisementTaskStatus
   */
  advertisementTaskStatus: string;
  /**
   * Getter method for property <tt>sortBy</tt>.
   * @return property value of sortBy
   */
  sortBy: string;
  /**
   * Getter method for property <tt>sortType</tt>.
   * @return property value of sortType
   */
  sortType: string;
  /**
   * Getter method for property <tt>merchantIntentionCondition</tt>.
   * @return property value of merchantIntentionCondition
   */
  merchantIntentionCondition: string[];
  /**
   * Getter method for property <tt>merchantTag</tt>.
   * @return property value of merchantTag
   */
  merchantLevels: string[];
  /**
   * Getter method for property <tt>serviceSatisfaction</tt>.
   * @return property value of serviceSatisfaction
   */
  serviceSatisfaction: string[];
  /**
   * Getter method for property <tt>merchantOperatingState</tt>.
   * @return property value of merchantOperatingState
   */
  merchantOperatingState: string[];
  /**
   * Getter method for property <tt>operationSuggestions</tt>.
   * @return property value of operationSuggestions
   */
  operationSuggestions: string[];
  /**
   * Getter method for property <tt>serviceProgress</tt>.
   * @return property value of serviceProgress
   */
  serviceProgress: string;
}

/**
 *
 *
 * 带分页的result.</br>
 * <AUTHOR>
 * @version : PageResultDTO, v 0.1 2023/12/19 19:58 muhan Exp $
 */
export interface PageDTO<T> {
  /**
   * Getter method for property dataList.
   * @return property value of dataList
   */
  dataList: T[];
  /**
   * Getter method for property pageInfo.
   * @return property value of pageInfo
   */
  pageInfo: PageInfoDTO;
}

/**
 * 说明：人户关系列表
 * <AUTHOR>
 * @version AgentOperationMerchantRelationDTO.java v 0.1 2024年03月04日 15:34 yuHao
 */
export interface AgentOperationMerchantRelationDTO extends ToString {
  highPotentialValues: any;
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  /**
   * Getter method for property <tt>taskStatus</tt>.
   * @return property value of taskStatus
   */
  taskStatus: string;
  /**
   * Getter method for property <tt>adTaskCompletedTotalNum</tt>.
   * @return property value of adTaskCompletedTotalNum
   */
  adTaskCompletedTotalNum: string;
  /**
   * Getter method for property <tt>adTaskTotalNum</tt>.
   * @return property value of adTaskTotalNum
   */
  adTaskTotalNum: string;
  /**
   * Getter method for property <tt>infrastructTaskCompletedTotalNum</tt>.
   * @return property value of infrastructTaskCompletedTotalNum
   */
  infrastructTaskCompletedTotalNum: string;
  /**
   * Getter method for property <tt>infrastructTaskTotalNum</tt>.
   * @return property value of infrastructTaskTotalNum
   */
  infrastructTaskTotalNum: string;
  /**
   * Getter method for property <tt>showMerchantBusinessNews</tt>.
   * @return property value of showMerchantBusinessNews
   */
  showMerchantBusinessNews: boolean;
  /**
   * Getter method for property <tt>mainShopName</tt>.
   * @return property value of mainShopName
   */
  mainShopName: string;
  /**
   * Getter method for property <tt>adTaskCompletionProgress</tt>.
   * @return property value of adTaskCompletionProgress
   */
  adTaskCompletionProgress: string;
  /**
   * Getter method for property <tt>infrastructureCompletionProgress</tt>.
   * @return property value of infrastructureCompletionProgress
   */
  infrastructureCompletionProgress: string;
  /**
   * Getter method for property <tt>adCurrentMonthCost</tt>.
   * @return property value of adCurrentMonthCost
   */
  adCurrentMonthCost: string;
  /**
   * Getter method for property <tt>adCurrentBalance</tt>.
   * @return property value of adCurrentBalance
   */
  adCurrentBalance: string;
  /**
   * Getter method for property <tt>adRechargeCount</tt>.
   * @return property value of adRechargeCount
   */
  adRechargeCount: Number;
  /**
   * Getter method for property <tt>shopBaseTaskFinishStatus</tt>.
   * @return property value of shopBaseTaskFinishStatus
   */
  shopBaseTaskFinishStatus: string;
  /**
   * Getter method for property <tt>advertisementTaskStatus</tt>.
   * @return property value of advertisementTaskStatus
   */
  advertisementTaskStatus: string;
  /**
   * Getter method for property <tt>merchantLabels</tt>.
   * @return property value of merchantLabels
   */
  merchantLabels: MerchantLabelsDTO[];
  /**
   * Getter method for property <tt>suggestionLabels</tt>.
   * @return property value of suggestionLabels
   */
  suggestionLabels: SuggestionLabelDTO[];
  /**
   * Getter method for property <tt>merchantIntention</tt>.
   * @return property value of merchantIntention
   */
  merchantIntention: MerchantIntentionDTO[];
  /**
   * Getter method for property <tt>showCallButton</tt>.
   * @return property value of showCallButton
   */
  showCallButton: boolean;
  /**
   * Getter method for property <tt>serviceProgress</tt>.
   * @return property value of serviceProgress
   */
  serviceProgress: ServiceProgressDTO;
  /**
   * Getter method for property <tt>merchantLevel</tt>.
   * @return property value of merchantLevel
   */
  merchantLevel: MerchantLevelDTO;
  /**
   * Getter method for property <tt>serviceSatisfaction</tt>.
   * @return property value of serviceSatisfaction
   */
  serviceSatisfaction: ServiceSatisfactionDTO;
  /**
   * Getter method for property <tt>merchantOperatingState</tt>.
   * @return property value of merchantOperatingState
   */
  merchantOperatingState: MerchantOperatingStateDTO;
  /**
   * Getter method for property <tt>operationSuggestions</tt>.
   * @return property value of operationSuggestions
   */
  operationSuggestions: OperationSuggestionsDTO;
  /**
   * Getter method for property <tt>actions</tt>.
   * @return property value of actions
   */
  actions: AgentMerchantActionDTO[];
}

/**
 * 说明：代运营门店列表
 * <AUTHOR>
 * @version AgentOperationShopRelationListRequest.java v 0.1 2024年03月06日 11:49 yuHao
 */
export interface AgentOperationShopRelationListRequest extends PageRequest, Serializable {
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>shopName</tt>.
   * @return property value of shopName
   */
  shopName: string;
  /**
   * Getter method for property <tt>shopTaskStatus</tt>.
   * @return property value of shopTaskStatus
   */
  shopTaskStatus: string;
  /**
   * Getter method for property <tt>includeShopTaskProcess</tt>.
   * @return property value of includeShopTaskProcess
   */
  includeShopTaskProcess: boolean;
  /**
   * Getter method for property <tt>shopScoreConditionList</tt>.
   * @return property value of shopScoreConditionList
   */
  shopScoreConditionList: string[];
  /**
   * Getter method for property <tt>poiId</tt>.
   * @return property value of poiId
   */
  poiId: string;
  /**
   * Getter method for property <tt>sortBy</tt>.
   * @return property value of sortBy
   */
  sortBy: string;
  /**
   * Getter method for property <tt>sortType</tt>.
   * @return property value of sortType
   */
  sortType: string;
  /**
   * Getter method for property <tt>shangHuTongExpireCondition</tt>.
   * @return property value of shangHuTongExpireCondition
   */
  shangHuTongExpireCondition: string[];
  /**
   * Getter method for property <tt>merchantIntentionCondition</tt>.
   * @return property value of merchantIntentionCondition
   */
  merchantIntentionCondition: string[];
  /**
   * Getter method for property <tt>source</tt>.
   * @return property value of source
   */
  source: string;
}

/**
 * 说明：门店列表
 * <AUTHOR>
 * @version AgentOperationShopRelationDTO.java v 0.1 2024年03月06日 11:48 yuHao
 */
export interface AgentOperationShopRelationDTO extends ToString {
  /**
   * Getter method for property <tt>shopId</tt>.
   * @return property value of shopId
   */
  shopId: string;
  /**
   * Getter method for property <tt>shopName</tt>.
   * @return property value of shopName
   */
  shopName: string;
  /**
   * Getter method for property <tt>pid</tt>.
   * @return property value of pid
   */
  pid: string;
  /**
   * Getter method for property <tt>shopTaskStatus</tt>.
   * @return property value of shopTaskStatus
   */
  shopTaskStatus: string;
  /**
   * Getter method for property <tt>infrastructTaskCompletedTotalNum</tt>.
   * @return property value of infrastructTaskCompletedTotalNum
   */
  infrastructTaskCompletedTotalNum: string;
  /**
   * Getter method for property <tt>infrastructTaskTotalNum</tt>.
   * @return property value of infrastructTaskTotalNum
   */
  infrastructTaskTotalNum: string;
  /**
   * Getter method for property <tt>showShopBusinessNews</tt>.
   * @return property value of showShopBusinessNews
   */
  showShopBusinessNews: boolean;
  /**
   * Getter method for property <tt>taskFlowInstanceId</tt>.
   * @return property value of taskFlowInstanceId
   */
  taskFlowInstanceId: string;
  /**
   * Getter method for property <tt>shopQualityScore</tt>.
   * @return property value of shopQualityScore
   */
  shopQualityScore: string;
  /**
   * Getter method for property <tt>poiId</tt>.
   * @return property value of poiId
   */
  poiId: string;
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  annualSpuId: string;
  /**
   * Getter method for property <tt>shangHuTongExpireTime</tt>.
   * @return property value of shangHuTongExpireTime
   */
  shangHuTongExpireTime: string;
  /**
   * Getter method for property <tt>shangHuTongStatus</tt>.
   * @return property value of shangHuTongStatus
   */
  shangHuTongStatus: string;
  /**
   * Getter method for property <tt>shopLabels</tt>.
   * @return property value of shopLabels
   */
  shopLabels: string[];
  /**
   * Getter method for property <tt>shangHuTongSubStatus</tt>.
   * @return property value of shangHuTongSubStatus
   */
  shangHuTongSubStatus: string;
  /**
   * Getter method for property <tt>suggestionLabels</tt>.
   * @return property value of suggestionLabels
   */
  suggestionLabels: SuggestionLabelDTO[];
  /**
   * Getter method for property <tt>merchantIntention</tt>.
   * @return property value of merchantIntention
   */
  merchantIntention: MerchantIntentionDTO[];
  /**
   * Getter method for property <tt>payJumpList</tt>.
   * @return property value of payJumpList
   */
  payJumpList: TaskJumpDTO[];
  /**
   * Getter method for property <tt>address</tt>.
   * @return property value of address
   */
  address: string;
  /**
   * Getter method for property <tt>pic</tt>.
   * @return property value of pic
   */
  pic: string;
  /**
   * Getter method for property <tt>improveInfrastructure</tt>.
   * @return property value of improveInfrastructure
   */
  improveInfrastructure: string;
}

export interface AgentOperationShopChangeReq extends BaseRequest {
  channel: string;
}

export interface AgentOperationShopChangeDTO extends Serializable {
  /**
   * 门店分异动数
   */
  scoreAbnormalNumb: string;
  /**
   * 门店分下降数
   */
  scoreDelineNumb: string;
  /**
   * 4~5分门店
   */
  scoreFourToFiveNumb: string;
  /**
   * 5~6分门店
   */
  scoreFiveToSixNumb: string;
  /**
   * Getter method for property adTaskSummary.
   * @return property value of adTaskSummary
   */
  adTaskSummary: AdTaskSummaryDTO[];
  /**
   * 需要续约商户通的门店数
   */
  shangHuTongRenewalNumb: string;
  /**
   * 商户通预警对象
   */
  shangHuTongRenewalUrgentDTO: ShangHuTongRenewalUrgentDTO;
}

/**
 * 说明：商家数据
 * <AUTHOR>
 * @version MerchantBusinessDataRequest.java v 0.1 2024年02月23日 14:26 yuHao
 */
export interface MerchantOptBusinessDataRequest extends BaseRequest, Serializable {
  /**
   * Getter method for property <tt>shopIdList</tt>.
   * @return property value of shopIdList
   */
  shopIdList: string[];
  /**
   * Getter method for property <tt>startDate</tt>.
   * @return property value of startDate
   */
  startDate: number;
  /**
   * Getter method for property <tt>endDate</tt>.
   * @return property value of endDate
   */
  endDate: number;
}

/**
 * 说明：喜报返回结果
 * <AUTHOR>
 * @version MerchantBusinessNewsDTO.java v 0.1 2024年02月23日 14:32 yuHao
 */
export interface MerchantBusinessDTO extends ToString {
  /**
   * Getter method for property <tt>dataResult</tt>.
   * @return property value of dataResult
   */
  dataResult: { [index: string]: any };
  /**
   * Getter method for property <tt>merchantName</tt>.
   * @return property value of merchantName
   */
  merchantName: string;
  /**
   * Getter method for property <tt>businessNewType</tt>.
   * @return property value of businessNewType
   */
  businessNewType: string;
  /**
   * Getter method for property <tt>recharge</tt>.
   * @return property value of recharge
   */
  recharge: boolean;
  /**
   * Getter method for property <tt>shopScore</tt>.
   * @return property value of shopScore
   */
  shopScore: string;
  /**
   * Getter method for property <tt>shopRank</tt>.
   * @return property value of shopRank
   */
  shopRank: string;
  /**
   * Getter method for property <tt>taskSummary</tt>.
   * @return property value of taskSummary
   */
  taskSummary: TaskSummaryDTO;
}

export interface AgentOperationMerchantDetailReq extends BaseRequest {
  /**
   * 商户pid
   */
  pid: string;
  channel: string;
}

export interface AgentOperationMerchantDetailDTO extends Serializable {
  /**
   * 商户id
   */
  pid: string;
  /**
   * 商户名称
   */
  merchantName: string;
  /**
   * 主店名
   */
  mainShopName: string;
  /**
   * 广告当月总消耗
   */
  adCurrentMonthCost: string;
  /**
   * 广告现金余额
   */
  adCurrentBalance: string;
  /**
   * 累计充值次数
   */
  adRechargeCount: Number;
  /**
   * 商户标签
   */
  merchantLabels: string[];
  /**
   * 最近一次通话时间
   */
  lastCallTime: string;
  /**
   * 营业门店总数
   */
  shopOpenNum: number;
  /**
   * 商户通在约门店总数
   */
  shangHuTongShopNum: number;
}

export interface AgentOperationMerchantTaskDetailReq extends BaseRequest {
  /**
   * 商户ID
   */
  pid: string;
}

export interface AgentOperationMerchantTaskDetailDTO extends Serializable {
  /**
   * 商户ID
   */
  pid: string;
  /**
   * 应该服务次数描述
   */
  needServiceStr: string;
  /**
   * 实际服务次数
   */
  actualService: string;
  /**
   * 商户下任务列表
   */
  merchantTaskDetailDTOS: MerchantTaskDetailDTO[];
  serviceStatus: string;
}

export interface CommonOperatorInfo {
  /**
   * Getter method for property <tt>operatorId</tt>.
   * @return property value of operatorId
   */
  operatorId: string;
  /**
   * Getter method for property <tt>operatorType</tt>.
   * @return property value of operatorType
   */
  operatorType: string;
  /**
   * Getter method for property <tt>operatorName</tt>.
   * @return property value of operatorName
   */
  operatorName: string;
  /**
   * Getter method for property <tt>sellerId</tt>.
   * @return property value of sellerId
   */
  sellerId: string;
  /**
   * Getter method for property <tt>partnerId</tt>.
   * @return property value of partnerId
   */
  partnerId: string;
  /**
   * Getter method for property <tt>workId</tt>.
   * @return property value of workId
   */
  workId: string;
}

/**
 *
 *
 * client请求基类.</br>
 * <AUTHOR>
 * @version : BaseRequest, v 0.1 2023/12/19 20:24 muhan Exp $
 */
export interface BaseRequest {
  /**
   * Getter method for property requestId.
   * @return property value of requestId
   */
  requestId: string;
  /**
   * 通过操作员信息
   */
  commonOperatorInfo: CommonOperatorInfo;
}

export interface Serializable {}

export interface BaseDTO<T> extends Serializable {}

/**
 * 说明：不要减少字段
 * <AUTHOR>
 * @version TaskJumpDTO.java v 0.1 2024年03月11日 11:29 yuHao
 */
export interface TaskJumpDTO extends ToString {
  greyButton: boolean;
  /**
   * 按钮类型
   */
  buttonType: string;
  /**
   * 按钮描述
   */
  buttonText: string;
  /**
   * 按钮链接
   */
  jumpUrl: string;
  /**
   * Getter method for property <tt>client</tt>.
   * @return property value of client
   */
  client: string;
  /**
   * Getter method for property <tt>jumpType</tt>.
   * @return property value of jumpType
   */
  jumpType: string;
  /**
   * Getter method for property <tt>jumpTypeNew</tt>.
   * @return property value of jumpTypeNew
   */
  jumpTypeNew: string;
  /**
   * Getter method for property <tt>jumpUrlList</tt>.
   * @return property value of jumpUrlList
   */
  jumpUrlList: string[];
}

/**
 * 说明：任务详情
 * <AUTHOR>
 * @version TaskDetailDTO.java v 0.1 2024年03月05日 17:59 yuHao
 */
export interface TaskDetailDTO extends ToString {
  /**
   * Getter method for property <tt>taskName</tt>.
   * @return property value of taskName
   */
  taskName: string;
  /**
   * Getter method for property <tt>taskDesc</tt>.
   * @return property value of taskDesc
   */
  taskDesc: string;
  /**
   * Getter method for property <tt>taskTip</tt>.
   * @return property value of taskTip
   */
  taskTip: string;
  /**
   * Getter method for property <tt>taskDetailStatus</tt>.
   * @return property value of taskDetailStatus
   */
  taskDetailStatus: string;
  /**
   * Getter method for property <tt>taskScore</tt>.
   * @return property value of taskScore
   */
  taskScore: string;
  /**
   * Getter method for property <tt>taskIndicatorTargetNum</tt>.
   * @return property value of taskIndicatorTargetNum
   */
  taskIndicatorTargetNum: string;
  /**
   * Getter method for property <tt>taskIndicatorCompletedNum</tt>.
   * @return property value of taskIndicatorCompletedNum
   */
  taskIndicatorCompletedNum: string;
  /**
   * Getter method for property <tt>taskDetailJumpList</tt>.
   * @return property value of taskDetailJumpList
   */
  taskDetailJumpList: TaskJumpDTO[];
  /**
   * Getter method for property <tt>sopButtons</tt>.
   * @return property value of sopButtons
   */
  sopButtons: TaskJumpDTO[];
  /**
   * Getter method for property <tt>taskActualScore</tt>.
   * @return property value of taskActualScore
   */
  taskActualScore: string;
  /**
   * Getter method for property <tt>extInfo</tt>.
   * @return property value of extInfo
   */
  extInfo: { [index: string]: any };
}

/**
 * 说明：ToString
 * <AUTHOR>
 * @version ToString.java v 0.1 2024年02月28日 21:15 yuHao
 */
export interface ToString extends Serializable {}

export interface Page extends Serializable {
  pageSize: number;
  pageNo: number;
}

/**
 * <AUTHOR>
 * @date 2023/12/26 23:39
 * @since 1.0.0
 */
export interface PageRequest extends BaseRequest {
  page: Page;
  limit: number;
  offset: number;
}

/**
 * 页码分页信息模型
 * <AUTHOR>
 * @version $Id: PageNoInfoDTO.java, v 0.1 2019年07月16日 15:39 荒丘 Exp $
 */
export interface PageInfoDTO {
  /**
   * Getter method for property hasMore.
   * @return property value of hasMore
   */
  hasMore: boolean;
  /**
   * Getter method for property totalPage.
   * @return property value of totalPage
   */
  totalPage: number;
  /**
   * Getter method for property currentPageNo.
   * @return property value of currentPageNo
   */
  currentPageNo: number;
  /**
   * Getter method for property nextPageNo.
   * @return property value of nextPageNo
   */
  nextPageNo: number;
  /**
   * Getter method for property pageSize.
   * @return property value of pageSize
   */
  pageSize: number;
  /**
   * Getter method for property totalCount.
   * @return property value of totalCount
   */
  totalCount: number;
}

/**
 * 说明：
 * <AUTHOR>
 * @version MerchantLabelsDTO.java v 0.1 2024年03月12日 22:54 yuHao
 */
export interface MerchantLabelsDTO extends ToString {
  /**
   * Getter method for property <tt>code</tt>.
   * @return property value of code
   */
  code: string;
  /**
   * Getter method for property <tt>name</tt>.
   * @return property value of name
   */
  name: string;
}

/**
 * 说明：广告建议
 * <AUTHOR>
 * @version SuggestionLabelDTO.java v 0.1 2024年03月05日 17:59 yuHao
 */
export interface SuggestionLabelDTO extends ToString {
  /**
   * Getter method for property <tt>labelCode</tt>.
   * @return property value of labelCode
   */
  labelCode: string;
  /**
   * Getter method for property <tt>labelContent</tt>.
   * @return property value of labelContent
   */
  labelContent: string[];
}

/**
 * 说明：商户意向
 * <AUTHOR>
 * @version MerchantIntentionDTO.java v 0.1 2024年03月05日 17:59 yuHao
 */
export interface MerchantIntentionDTO extends ToString {
  /**
   * Getter method for property <tt>intentionDegree</tt>.
   * @return property value of intentionDegree
   */
  intentionDegree: string;
  /**
   * Getter method for property <tt>intentionDesc</tt>.
   * @return property value of intentionDesc
   */
  intentionDesc: string;
  /**
   * Getter method for property <tt>recordTime</tt>.
   * @return property value of recordTime
   */
  recordTime: string;
  /**
   * Getter method for property <tt>source</tt>.
   * @return property value of source
   */
  source: string;
}

/**
 * 说明：服务进度
 * <AUTHOR>
 * @version ServiceProgressDTO.java v 0.1 2024年03月12日 21:44 yuHao
 */
export interface ServiceProgressDTO extends ToString {
  /**
   * Getter method for property <tt>taskCnt</tt>.
   * @return property value of taskCnt
   */
  taskCnt: string;
  /**
   * Getter method for property <tt>completedTaskCnt</tt>.
   * @return property value of completedTaskCnt
   */
  completedTaskCnt: string;
  /**
   * Getter method for property <tt>code</tt>.
   * @return property value of code
   */
  code: string;
  /**
   * Getter method for property <tt>name</tt>.
   * @return property value of name
   */
  name: string;
}

/**
 * 说明：商户分层dto
 * <AUTHOR>
 * @version MerchantLevelDTO.java v 0.1 2024年03月12日 21:39 yuHao
 */
export interface MerchantLevelDTO extends ToString {
  /**
   * Getter method for property <tt>code</tt>.
   * @return property value of code
   */
  code: string;
  /**
   * Getter method for property <tt>name</tt>.
   * @return property value of name
   */
  name: string;
}

/**
 * 说明：满意度
 * <AUTHOR>
 * @version ServiceSatisfactionDTO.java v 0.1 2024年03月12日 21:40 yuHao
 */
export interface ServiceSatisfactionDTO extends ToString {
  /**
   * Getter method for property <tt>score</tt>.
   * @return property value of score
   */
  score: string;
  /**
   * Getter method for property <tt>code</tt>.
   * @return property value of code
   */
  code: string;
  /**
   * Getter method for property <tt>name</tt>.
   * @return property value of name
   */
  name: string;
}

/**
 * 说明：红黄绿灯
 * <AUTHOR>
 * @version MerchantOperatingStateDTO.java v 0.1 2024年03月12日 21:41 yuHao
 */
export interface MerchantOperatingStateDTO extends ToString {
  /**
   * Getter method for property <tt>code</tt>.
   * @return property value of code
   */
  code: string;
  /**
   * Getter method for property <tt>name</tt>.
   * @return property value of name
   */
  name: string;
}

/**
 * 说明：运营建议
 * <AUTHOR>
 * @version OperationSuggestionsDTO.java v 0.1 2024年03月12日 21:42 yuHao
 */
export interface OperationSuggestionsDTO extends ToString {
  /**
   * Getter method for property <tt>code</tt>.
   * @return property value of code
   */
  code: string;
  /**
   * Getter method for property <tt>name</tt>.
   * @return property value of name
   */
  name: string;
}

/**
 * 说明：商户列表行动点
 * <AUTHOR>
 * @version AgentMerchantActionDTO.java v 0.1 2024年03月20日 11:33 yuHao
 */
export interface AgentMerchantActionDTO extends ToString {
  /**
   * Getter method for property <tt>buttonName</tt>.
   * @return property value of buttonName
   */
  buttonName: string;
  /**
   * Getter method for property <tt>buttonCode</tt>.
   * @return property value of buttonCode
   */
  buttonCode: string;
}

export interface AdTaskSummaryDTO extends Serializable {
  /**
   * Getter method for property taskName.
   * @return property value of taskName
   */
  taskName: string;
  /**
   * Getter method for property taskDesc.
   * @return property value of taskDesc
   */
  taskDesc: string;
  /**
   * Getter method for property taskCount.
   * @return property value of taskCount
   */
  taskCount: number;
  /**
   * Getter method for property pcJumpUrl.
   * @return property value of pcJumpUrl
   */
  pcJumpUrl: string;
}

export interface ShangHuTongRenewalUrgentDTO {
  day: number;
  week: number;
  month: number;
}

/**
 * 说明：
 * <AUTHOR>
 * @version TaskSummaryNewDTO.java v 0.1 2024年02月23日 14:44 yuHao
 */
export interface TaskSummaryDTO extends ToString {
  /**
   * Getter method for property <tt>shopName</tt>.
   * @return property value of shopName
   */
  shopName: string;
  /**
   * Getter method for property <tt>diagnoseHomeResultDTO</tt>.
   * @return property value of diagnoseHomeResultDTO
   */
  diagnoseHomeResultDTO: DiagnoseHomeResultDTO;
  /**
   * Getter method for property <tt>list</tt>.
   * @return property value of list
   */
  list: StoreTaskItem[];
}

export interface MerchantTaskDetailDTO extends Serializable {
  /**
   * 任务名称
   */
  taskName: string;
  /**
   * 任务描述
   */
  taskDesc: string;
  /**
   * 任务提示
   */
  taskTip: string;
  /**
   * 任务状态
   */
  taskDetailStatus: string;
  /**
   * 任务指标目标数
   */
  taskIndicatorTargetNum: string;
  /**
   * 任务指标已完成规定数
   */
  taskIndicatorCompletedNum: string;
  /**
   * 任务详情跳转
   */
  taskDetailJumpList: (TaskJumpDTO & IAction)[];
  /**
   * 服务沟通频次
   */
  taskServiceNum: string;
  /**
   * 任务类型
   * BUILD_RELATIONSHIP:建联
   * DEMAND_FOCUS:需求聚焦
   * COMPLIANCE_TRAINING:日常培训
   * AD_TOU_FANG_CONFIRM:广告投放确认
   * NEW_SIGN_FU_PAN:新签复盘
   * STOP_DROPPING:停投召回沟通
   * STORE_INFRASTRUCTURE:门店基础设施
   * FU_PAN:复盘
   * AD_EXTENSION:广告续充
   * NIAN_FEI__EXTENSION:年费续约
   * USP_OPTIMIZE:usp优化
   */
  taskType: string;
}

/**
 * 说明：
 * <AUTHOR>
 * @version DiagnoseHomeResultNewDTO.java v 0.1 2024年02月23日 14:44 yuHao
 */
export interface DiagnoseHomeResultDTO extends ToString {
  /**
   * Getter method for property <tt>merchantScore</tt>.
   * @return property value of merchantScore
   */
  merchantScore: string;
  /**
   * Getter method for property <tt>diagnoseCode</tt>.
   * @return property value of diagnoseCode
   */
  diagnoseCode: string;
  /**
   * Getter method for property <tt>diagnoseResult</tt>.
   * @return property value of diagnoseResult
   */
  diagnoseResult: string;
  /**
   * Getter method for property <tt>diagnoseIcon</tt>.
   * @return property value of diagnoseIcon
   */
  diagnoseIcon: string;
  /**
   * Getter method for property <tt>diagnoseDesc</tt>.
   * @return property value of diagnoseDesc
   */
  diagnoseDesc: string;
  /**
   * Getter method for property <tt>list</tt>.
   * @return property value of list
   */
  list: BgcUnFinishedItem[];
}

/**
 * 说明：
 * <AUTHOR>
 * @version StoreTaskNewItem.java v 0.1 2024年02月23日 14:45 yuHao
 */
export interface StoreTaskItem extends ToString {
  /**
   * Getter method for property <tt>taskCode</tt>.
   * @return property value of taskCode
   */
  taskCode: string;
  /**
   * Getter method for property <tt>title</tt>.
   * @return property value of title
   */
  title: string;
  /**
   * Getter method for property <tt>subTitle</tt>.
   * @return property value of subTitle
   */
  subTitle: string;
  /**
   * Getter method for property <tt>desc</tt>.
   * @return property value of desc
   */
  desc: string;
  /**
   * Getter method for property <tt>icon</tt>.
   * @return property value of icon
   */
  icon: string;
  /**
   * Getter method for property <tt>buttonText</tt>.
   * @return property value of buttonText
   */
  buttonText: string;
  /**
   * Getter method for property <tt>label</tt>.
   * @return property value of label
   */
  label: string;
  /**
   * Getter method for property <tt>score</tt>.
   * @return property value of score
   */
  score: string;
  /**
   * Getter method for property <tt>link</tt>.
   * @return property value of link
   */
  link: string;
  /**
   * Getter method for property <tt>status</tt>.
   * @return property value of status
   */
  status: number;
  /**
   * Getter method for property <tt>tagName</tt>.
   * @return property value of tagName
   */
  tagName: string;
  /**
   * Getter method for property <tt>level</tt>.
   * @return property value of level
   */
  level: number;
  /**
   * Getter method for property <tt>activeTag</tt>.
   * @return property value of activeTag
   */
  activeTag: string;
  /**
   * Getter method for property <tt>alert</tt>.
   * @return property value of alert
   */
  alert: AlertItem;
  /**
   * Getter method for property <tt>tipsAlert</tt>.
   * @return property value of tipsAlert
   */
  tipsAlert: TipsAlertItem;
  /**
   * Getter method for property <tt>scoreProcessBar</tt>.
   * @return property value of scoreProcessBar
   */
  scoreProcessBar: ScoreProcessBar;
}

/**
 * 说明：
 * <AUTHOR>
 * @version BgcUnFinishedNewItem.java v 0.1 2024年02月23日 14:51 yuHao
 */
export interface BgcUnFinishedItem extends ToString {
  /**
   * Getter method for property <tt>title</tt>.
   * @return property value of title
   */
  title: string;
  /**
   * Getter method for property <tt>undoCount</tt>.
   * @return property value of undoCount
   */
  undoCount: number;
}

/**
 * 说明：
 * <AUTHOR>
 * @version AlertINewtem.java v 0.1 2024年02月23日 14:46 yuHao
 */
export interface AlertItem extends ToString {
  /**
   * Getter method for property <tt>title</tt>.
   * @return property value of title
   */
  title: string;
  /**
   * Getter method for property <tt>desc</tt>.
   * @return property value of desc
   */
  desc: string;
  /**
   * Getter method for property <tt>imag</tt>.
   * @return property value of imag
   */
  imag: string;
  /**
   * Getter method for property <tt>btnText</tt>.
   * @return property value of btnText
   */
  btnText: string;
  /**
   * Getter method for property <tt>schema</tt>.
   * @return property value of schema
   */
  schema: string;
}

/**
 * 说明：
 * <AUTHOR>
 * @version TipsAlertNewItem.java v 0.1 2024年02月23日 14:47 yuHao
 */
export interface TipsAlertItem extends ToString {
  /**
   * Getter method for property <tt>type</tt>.
   * @return property value of type
   */
  type: string;
  /**
   * Getter method for property <tt>title</tt>.
   * @return property value of title
   */
  title: string;
  /**
   * Getter method for property <tt>desc</tt>.
   * @return property value of desc
   */
  desc: string;
  /**
   * Getter method for property <tt>image</tt>.
   * @return property value of image
   */
  image: string;
  /**
   * Getter method for property <tt>btnText</tt>.
   * @return property value of btnText
   */
  btnText: string;
  /**
   * Getter method for property <tt>schema</tt>.
   * @return property value of schema
   */
  schema: any;
}

/**
 * 说明：
 * <AUTHOR>
 * @version ScoreProcessNewBar.java v 0.1 2024年02月23日 14:48 yuHao
 */
export interface ScoreProcessBar extends ToString {
  /**
   * Getter method for property <tt>completedCount</tt>.
   * @return property value of completedCount
   */
  completedCount: number;
  /**
   * Getter method for property <tt>totalCompleteLimit</tt>.
   * @return property value of totalCompleteLimit
   */
  totalCompleteLimit: number;
  /**
   * Getter method for property <tt>countUnit</tt>.
   * @return property value of countUnit
   */
  countUnit: string;
  /**
   * Getter method for property <tt>scoreList</tt>.
   * @return property value of scoreList
   */
  scoreList: ScoreItem[];
}

export interface ScoreItem {
  /**
   * Getter method for property <tt>score</tt>.
   * @return property value of score
   */
  score: number;
  /**
   * Getter method for property <tt>completeLimit</tt>.
   * @return property value of completeLimit
   */
  completeLimit: number;
}

/**
 * 说明：代运营查询
 * <AUTHOR>
 * @version AgentOperationQueryFacade.java v 0.1 2024年02月22日 19:53 yuHao
 */
export interface AgentOperationQueryFacade {
  /**
   * 查看代运营任务详情
   * @param request
   * @return
   */
  queryAgentOperationDetail(
    request: AgentOperationDetailRequest,
  ): Promise<ResultDTO<AgentOperationDetailDTO>>;

  /**
   * 查询商户运维任务列表
   * @param request
   * @return 商户关系列表
   */
  queryAgentOperationMerchantList(
    request: AgentOperationMerchantRelationListRequest,
  ): Promise<ResultDTO<PageDTO<AgentOperationMerchantRelationDTO>>>;

  /**
   * 查看代运营门店任务列表
   * @param request
   * @return
   */
  queryAgentOperationShopList(
    request: AgentOperationShopRelationListRequest,
  ): Promise<ResultDTO<PageDTO<AgentOperationShopRelationDTO>>>;

  /**
   * 商户异动信息统计
   * @param agentOperationShopChangeReq
   * @return
   */
  queryAgentShopChange(
    agentOperationShopChangeReq: AgentOperationShopChangeReq,
  ): Promise<ResultDTO<AgentOperationShopChangeDTO>>;

  /**
   * 查询喜报数据
   * @param request
   *         * @return
   */
  queryMerchantBusinessNews(
    request: MerchantOptBusinessDataRequest,
  ): Promise<ResultDTO<MerchantBusinessDTO>>;

  /**
   * 商户详情查询
   * @param agentOperationMerchantDetailRequest
   * @return
   */
  queryMerchantDetail(
    agentOperationMerchantDetailRequest: AgentOperationMerchantDetailReq,
  ): Promise<ResultDTO<AgentOperationMerchantDetailDTO>>;

  /**
   * 商户运维任务详情
   * @param agentOperationMerchantTaskDetailReq
   * @return
   */
  queryMerchantTaskDetail(
    agentOperationMerchantTaskDetailReq: AgentOperationMerchantTaskDetailReq,
  ): Promise<ResultDTO<AgentOperationMerchantTaskDetailDTO>>;
}
