/* tslint:disable */
/* eslint-disable */
// Generated using typescript-generator version 2.32.889 on 2024-04-15 15:26:57.

export interface IndicatorInfoCreateRequest extends BaseRequest {
    indicatorInfoDTO: IndicatorInfoDTO;
}

export interface OperatorContext extends ToString {
    operatorType: string;
    operatorId: string;
    operatorName: string;
    requestChannel: string;
    extInfo: { [index: string]: string };
    runtimeObjectContext: { [index: string]: any };
}

export interface Result<T> extends BaseResult {
    value: T;
}

export interface BdLastSelectFieldsCreateRequest extends BaseRequest {
    bdId: string;
    pid: string;
    fields: string;
}

export interface GatewayResult<T> extends Serializable {
    success: boolean;
    resultMessage: string;
    resultCode: string;
    data: T;
    extInfo: { [index: string]: any };
}

export interface InsightRuleManageRequest extends BaseRequest {
    ruleDTO: InsightRuleDTO;
}

export interface BatchDataQueryRequest extends BaseRequest {
    dataQueryRequestList: DataQueryRequest[];
}

export interface DataQueryResponse extends BaseResult {
    applicationDataList: ApplicationDataVO[];
}

export interface AuthQueryRequest extends BaseRequest {
}

export interface AclAuthRequest extends BaseRequest {
    firstCateCode: string;
    secondCateCode: string[];
}

export interface DataQueryRequest extends PageRequest {
    applicationCode: string;
    resultType: string;
    requestParams: { [index: string]: string };
}

export interface IndicatorInfoDTO extends BaseDTO {
    indicatorId: string;
    fieldId: string;
    indicatorCode: string;
    indicatorName: string;
    indicatorDesc: string;
    indicatorStatus: string;
    valueType: string;
    optional: boolean;
}

export interface BaseRequest extends ToString {
    requestId: string;
    requestSource: string;
    requestChannel: string;
    bizContextInfos: { [index: string]: string };
    extInfo: { [index: string]: string };
}

export interface ToString extends Serializable {
}

export interface BaseResult extends ToString {
    success: boolean;
    retry: boolean;
    errorCode: string;
    errorMsg: string;
    extInfo: { [index: string]: string };
    resultCode: ResultCode;
}

export interface InsightRuleDTO extends BaseDTO {
    insightRuleId: string;
    insightRuleCode: string;
    insightRuleName: string;
    insightRuleDesc: string;
    applicationId: string;
    insightRuleStatus: string;
    selectRule: SelectRuleDTO;
    whereRule: WhereRuleDTO;
    havingRule: HavingRuleDTO;
    orderByRule: OrderByRuleDTO;
    limitRule: LimitRuleDTO;
}

export interface ApplicationDataVO extends ToString {
    applicationCode: string;
    ruleDataList: RuleDataVO[];
}

export interface PageRequest extends BaseRequest {
    pageNo: number;
    pageSize: number;
    start: number;
}

export interface BaseDTO extends ToString {
    extInfo: { [index: string]: string };
}

export interface SelectRuleDTO extends BaseDTO {
    indicators: IndicatorDefinitionDTO[];
}

export interface WhereRuleDTO extends BaseDTO {
    logicType: string;
    children: WhereRuleDTO[];
    indicators: IndicatorCompareDTO[];
}

export interface HavingRuleDTO extends BaseDTO {
    indicators: IndicatorCompareDTO[];
}

export interface OrderByRuleDTO extends BaseDTO {
    sortType: string;
    indicator: IndicatorDefinitionDTO;
}

export interface LimitRuleDTO extends ToString {
    pageNum: number;
    pageSize: number;
}

export interface RuleDataVO extends PageResult<any> {
    ruleId: LongAsUnion;
    ruleCode: string;
    ruleName: string;
    indicatorList: IndicatorVO[];
}

export interface IndicatorDefinitionDTO extends BaseDTO {
    indicatorCode: string;
    aggMethod: string;
    alias: string;
}

export interface IndicatorCompareDTO extends IndicatorDefinitionDTO {
    compareType: string;
    values: string[];
}

export interface IndicatorVO extends ToString {
    key: string;
    keyName: string;
    parentKey: string;
    desc: string;
}

export interface PageResult<T> extends MultiResult<T> {
    totalSize: LongAsUnion;
    pageNo: number;
    pageSize: number;
    totalPage: number;
}

export interface MultiResult<T> extends BaseResult {
    values: T[];
}

export interface IndicatorInfoManageFacade {

    createIndicatorInfo(arg0: IndicatorInfoCreateRequest, arg1: OperatorContext): Promise<Result<string>>;
}

export interface BdLastSelectFieldsManageFacade {

    createOrUpdate(arg0: BdLastSelectFieldsCreateRequest): Promise<GatewayResult<any>>;
}

export interface InsightRuleManageFacade {

    createInsightRule(arg0: InsightRuleManageRequest, arg1: OperatorContext): Promise<Result<string>>;
}

export interface DataQueryGwFacade {

    batchQueryData(arg0: BatchDataQueryRequest): Promise<GatewayResult<DataQueryResponse>>;

    queryApplicationAuthCodes(arg0: AuthQueryRequest): Promise<GatewayResult<{ [index: string]: string }>>;

    queryAuthByCodes(arg0: AclAuthRequest): Promise<GatewayResult<{ [index: string]: boolean }>>;

    queryData(arg0: DataQueryRequest): Promise<GatewayResult<DataQueryResponse>>;
}

export interface Serializable {
}

export interface SqlAble extends Serializable {

    sql(): Promise<string>;
}

export interface ResultCode {

    getErrorCode(): Promise<string>;

    getErrorMsg(): Promise<string>;
}

export type LongAsUnion = string | number;