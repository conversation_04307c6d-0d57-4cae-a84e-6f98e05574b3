/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.amap.sales.data
 * ArtifactId: amap-sales-data-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  IndicatorInfoManageFacade,
  IndicatorInfoCreateRequest, 
  OperatorContext, 
  Result,
} from '@/_docplus/target/types/amap-sales-data-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class IndicatorInfoManageFacadeClient implements Partial<IndicatorInfoManageFacade> {
  public async createIndicatorInfo(arg0: Partial<IndicatorInfoCreateRequest>, arg1: Partial<OperatorContext>, option?: GwRequestOption): Promise<Result<string>> {
    const innerKey = `amap-sales-data.IndicatorInfoManageFacade.createIndicatorInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0, arg1],
    }, option);
    return res;
  }
}

export default new IndicatorInfoManageFacadeClient();
