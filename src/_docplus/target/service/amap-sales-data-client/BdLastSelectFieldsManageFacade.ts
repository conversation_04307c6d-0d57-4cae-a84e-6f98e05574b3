/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.amap.sales.data
 * ArtifactId: amap-sales-data-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  BdLastSelectFieldsManageFacade,
  BdLastSelectFieldsCreateRequest, 
  GatewayResult,
} from '@/_docplus/target/types/amap-sales-data-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class BdLastSelectFieldsManageFacadeClient implements Partial<BdLastSelectFieldsManageFacade> {
  public async createOrUpdate(arg0: Partial<BdLastSelectFieldsCreateRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `amap-sales-data.BdLastSelectFieldsManageFacade.createOrUpdate:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new BdLastSelectFieldsManageFacadeClient();
