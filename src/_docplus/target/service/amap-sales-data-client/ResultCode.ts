/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.amap.sales.data
 * ArtifactId: amap-sales-data-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  ResultCode,
} from '@/_docplus/target/types/amap-sales-data-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class ResultCodeClient implements Partial<ResultCode> {
  public async getErrorCode(option?: GwRequestOption): Promise<string> {
    const innerKey = `amap-sales-data.ResultCode.getErrorCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [],
    }, option);
    return res;
  }

  public async getErrorMsg(option?: GwRequestOption): Promise<string> {
    const innerKey = `amap-sales-data.ResultCode.getErrorMsg:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [],
    }, option);
    return res;
  }
}

export default new ResultCodeClient();
