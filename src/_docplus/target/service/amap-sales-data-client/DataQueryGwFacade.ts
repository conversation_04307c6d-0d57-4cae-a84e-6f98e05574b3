/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.amap.sales.data
 * ArtifactId: amap-sales-data-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  DataQueryGwFacade,
  BatchDataQueryRequest, 
  GatewayResult, 
  DataQueryResponse, 
  AuthQueryRequest, 
  AclAuthRequest, 
  DataQueryRequest,
} from '@/_docplus/target/types/amap-sales-data-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class DataQueryGwFacadeClient implements Partial<DataQueryGwFacade> {
  public async batchQueryData(arg0: Partial<BatchDataQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<DataQueryResponse>> {
    const innerKey = `amap-sales-data.DataQueryGwFacade.batchQueryData:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryApplicationAuthCodes(arg0: Partial<AuthQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<{[index: string]: string}>> {
    const innerKey = `amap-sales-data.DataQueryGwFacade.queryApplicationAuthCodes:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryAuthByCodes(arg0: Partial<AclAuthRequest>, option?: GwRequestOption): Promise<GatewayResult<{[index: string]: boolean}>> {
    const innerKey = `amap-sales-data.DataQueryGwFacade.queryAuthByCodes:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }

  public async queryData(arg0: Partial<DataQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<DataQueryResponse>> {
    const innerKey = `amap-sales-data.DataQueryGwFacade.queryData:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [arg0],
    }, option);
    return res;
  }
}

export default new DataQueryGwFacadeClient();
