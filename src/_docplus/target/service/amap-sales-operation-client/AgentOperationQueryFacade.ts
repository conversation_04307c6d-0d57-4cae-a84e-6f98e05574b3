/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.amap.sales.operation
 * ArtifactId: amap-sales-operation-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  AgentOperationQueryFacade,
  AgentOperationDetailRequest, 
  ResultDTO, 
  AgentOperationDetailDTO, 
  AgentOperationMerchantRelationListRequest, 
  PageDTO, 
  AgentOperationMerchantRelationDTO, 
  AgentOperationShopRelationListRequest, 
  AgentOperationShopRelationDTO, 
  AgentOperationShopChangeReq, 
  AgentOperationShopChangeDTO, 
  MerchantOptBusinessDataRequest, 
  MerchantBusinessDTO, 
  AgentOperationMerchantDetailReq, 
  AgentOperationMerchantDetailDTO, 
  AgentOperationMerchantTaskDetailReq, 
  AgentOperationMerchantTaskDetailDTO,
} from '@/_docplus/target/types/amap-sales-operation-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class AgentOperationQueryFacadeClient implements Partial<AgentOperationQueryFacade> {
  public async queryAgentOperationDetail(request: Partial<AgentOperationDetailRequest>, option?: GwRequestOption): Promise<ResultDTO<AgentOperationDetailDTO>> {
    const innerKey = `amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryAgentOperationMerchantList(request: Partial<AgentOperationMerchantRelationListRequest>, option?: GwRequestOption): Promise<ResultDTO<PageDTO<AgentOperationMerchantRelationDTO>>> {
    const innerKey = `amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationMerchantList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryAgentOperationShopList(request: Partial<AgentOperationShopRelationListRequest>, option?: GwRequestOption): Promise<ResultDTO<PageDTO<AgentOperationShopRelationDTO>>> {
    const innerKey = `amap-sales-operation.AgentOperationQueryFacade.queryAgentOperationShopList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryAgentShopChange(agentOperationShopChangeReq: Partial<AgentOperationShopChangeReq>, option?: GwRequestOption): Promise<ResultDTO<AgentOperationShopChangeDTO>> {
    const innerKey = `amap-sales-operation.AgentOperationQueryFacade.queryAgentShopChange:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationShopChangeReq],
    }, option);
    return res;
  }

  public async queryMerchantBusinessNews(request: Partial<MerchantOptBusinessDataRequest>, option?: GwRequestOption): Promise<ResultDTO<MerchantBusinessDTO>> {
    const innerKey = `amap-sales-operation.AgentOperationQueryFacade.queryMerchantBusinessNews:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryMerchantDetail(agentOperationMerchantDetailRequest: Partial<AgentOperationMerchantDetailReq>, option?: GwRequestOption): Promise<ResultDTO<AgentOperationMerchantDetailDTO>> {
    const innerKey = `amap-sales-operation.AgentOperationQueryFacade.queryMerchantDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationMerchantDetailRequest],
    }, option);
    return res;
  }

  public async queryMerchantTaskDetail(agentOperationMerchantTaskDetailReq: Partial<AgentOperationMerchantTaskDetailReq>, option?: GwRequestOption): Promise<ResultDTO<AgentOperationMerchantTaskDetailDTO>> {
    const innerKey = `amap-sales-operation.AgentOperationQueryFacade.queryMerchantTaskDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationMerchantTaskDetailReq],
    }, option);
    return res;
  }
}

export default new AgentOperationQueryFacadeClient();
