/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitParticipantGateway,
  VisitParticipantRecordAppraiseRequest, 
  GatewayResult, 
  VisitParticipantRecordPageQueryRequest, 
  Pagination, 
  VisitRecordInfoVO, 
  VisitParticipantRecordDetailQueryRequest, 
  VisitRecordDetailVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitParticipantGatewayClient implements Partial<VisitParticipantGateway> {
  public async appraiseParticipant(request: Partial<VisitParticipantRecordAppraiseRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitParticipantGateway.appraiseParticipant:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async pageQueryRecord(request: Partial<VisitParticipantRecordPageQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<VisitRecordInfoVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitParticipantGateway.pageQueryRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryVisitParticipantDetail(request: Partial<VisitParticipantRecordDetailQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitRecordDetailVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitParticipantGateway.queryVisitParticipantDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitParticipantGatewayClient();
