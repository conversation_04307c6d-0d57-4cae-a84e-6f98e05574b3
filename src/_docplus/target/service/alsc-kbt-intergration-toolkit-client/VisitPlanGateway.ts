/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitPlanGateway,
  VisitPlanCancelRequest, 
  GatewayResult, 
  VisitPlanCreateRequest, 
  VisitPlanPageQueryRequest, 
  Pagination, 
  VisitPlanInfoVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitPlanGatewayClient implements Partial<VisitPlanGateway> {
  public async cancelPlan(request: Partial<VisitPlanCancelRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitPlanGateway.cancelPlan:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async createPlan(request: Partial<VisitPlanCreateRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitPlanGateway.createPlan:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async pageQueryPlan(request: Partial<VisitPlanPageQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<VisitPlanInfoVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitPlanGateway.pageQueryPlan:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitPlanGatewayClient();
