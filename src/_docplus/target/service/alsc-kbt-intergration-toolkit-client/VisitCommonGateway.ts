/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitCommonGateway,
  VisitPhoneDecodeRequest, 
  GatewayResult, 
  VisitPhoneDecodeVO, 
  VisitBaseRequest, 
  VisitBaseConfigInfoVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitCommonGatewayClient implements Partial<VisitCommonGateway> {
  public async decodePhone(request: Partial<VisitPhoneDecodeRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitPhoneDecodeVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCommonGateway.decodePhone:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getVisitChannelList(request: Partial<VisitBaseRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitBaseConfigInfoVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCommonGateway.getVisitChannelList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getVisitTargetTypeList(request: Partial<VisitBaseRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitBaseConfigInfoVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCommonGateway.getVisitTargetTypeList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async greyConfig(request: Partial<VisitBaseRequest>, option?: GwRequestOption): Promise<GatewayResult<{[index: string]: any}>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCommonGateway.greyConfig:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitCommonGatewayClient();
