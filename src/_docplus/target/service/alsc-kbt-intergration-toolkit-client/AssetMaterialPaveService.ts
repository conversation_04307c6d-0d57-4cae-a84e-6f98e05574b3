/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  AssetMaterialPaveService,
  CreateStuffPaveRequest, 
  KbResult, 
  PaveCheckAuditDetailVO, 
  PaveDetailInfoVO, 
  PageQueryPaveRequest, 
  PageQueryResult, 
  QueryTargetRequest, 
  PaveTargetVO, 
  QueryTemplateRequest, 
  PaveTemplateVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class AssetMaterialPaveServiceClient implements Partial<AssetMaterialPaveService> {
  public async batchCreateStuffPave(request: Partial<CreateStuffPaveRequest>, option?: GwRequestOption): Promise<KbResult<boolean>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetMaterialPaveService.batchCreateStuffPave:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async doSth(ids: number[], table: string, option?: GwRequestOption): Promise<void> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetMaterialPaveService.doSth:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [ids, table],
    }, option);
    return res;
  }

  public async queryPaveCheckDetailInfoByCheckId(checkId: string, option?: GwRequestOption): Promise<KbResult<PaveCheckAuditDetailVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetMaterialPaveService.queryPaveCheckDetailInfoByCheckId:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [checkId],
    }, option);
    return res;
  }

  public async queryPaveDetailInfoByPaveId(paveId: string, option?: GwRequestOption): Promise<KbResult<PaveDetailInfoVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetMaterialPaveService.queryPaveDetailInfoByPaveId:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [paveId],
    }, option);
    return res;
  }

  public async queryPaveListByLoginUser(request: Partial<PageQueryPaveRequest>, option?: GwRequestOption): Promise<PageQueryResult<PaveDetailInfoVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetMaterialPaveService.queryPaveListByLoginUser:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryPaveTarget(request: Partial<QueryTargetRequest>, option?: GwRequestOption): Promise<KbResult<PaveTargetVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetMaterialPaveService.queryPaveTarget:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryPaveTemplate(request: Partial<QueryTemplateRequest>, option?: GwRequestOption): Promise<KbResult<PaveTemplateVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetMaterialPaveService.queryPaveTemplate:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new AssetMaterialPaveServiceClient();
