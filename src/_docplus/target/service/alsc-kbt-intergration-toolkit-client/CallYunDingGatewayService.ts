/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  CallYunDingGatewayService,
  OutCallPrepareRequest, 
  GatewayResult, 
  OutCallPrepareResultVO, 
  OutCallProgressRequest, 
  OutCallProgressResultVO, 
  OutCallStartRequest, 
  OutCallStartResultVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class CallYunDingGatewayServiceClient implements Partial<CallYunDingGatewayService> {
  public async prepare(request: Partial<OutCallPrepareRequest>, option?: GwRequestOption): Promise<GatewayResult<OutCallPrepareResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallYunDingGatewayService.prepare:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async progressOutCall(request: Partial<OutCallProgressRequest>, option?: GwRequestOption): Promise<GatewayResult<OutCallProgressResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallYunDingGatewayService.progressOutCall:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async startOutCall(request: Partial<OutCallStartRequest>, option?: GwRequestOption): Promise<GatewayResult<OutCallStartResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallYunDingGatewayService.startOutCall:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new CallYunDingGatewayServiceClient();
