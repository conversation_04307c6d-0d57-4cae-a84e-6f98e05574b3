/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  AlarmQueryService,
  KbResult, 
  AlarmCategoryCountResponse, 
  AlarmTotalCountResponse, 
  AlarmDetailQueryRequest, 
  KbPageResult, 
  AlarmDetailResponse,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class AlarmQueryServiceClient implements Partial<AlarmQueryService> {
  public async fetchCategoryCount(userId: number, option?: GwRequestOption): Promise<KbResult<AlarmCategoryCountResponse[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmQueryService.fetchCategoryCount:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [userId],
    }, option);
    return res;
  }

  public async fetchCount(userId: number, option?: GwRequestOption): Promise<KbResult<AlarmTotalCountResponse>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmQueryService.fetchCount:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [userId],
    }, option);
    return res;
  }

  public async listDetails(request: Partial<AlarmDetailQueryRequest>, option?: GwRequestOption): Promise<KbPageResult<AlarmDetailResponse>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmQueryService.listDetails:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryById(id: number, option?: GwRequestOption): Promise<KbResult<AlarmDetailResponse>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmQueryService.queryById:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [id],
    }, option);
    return res;
  }
}

export default new AlarmQueryServiceClient();
