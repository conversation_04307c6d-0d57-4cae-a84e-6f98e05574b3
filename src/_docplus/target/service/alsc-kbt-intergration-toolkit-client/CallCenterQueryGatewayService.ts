/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  CallCenterQueryGatewayService,
  CallRecordMissingVisitQueryRequest, 
  GatewayResult, 
  CallRecordStatsInfoQueryRequest, 
  CallRecordStatsInfoVO, 
  CallRecordMissVisitQueryRequest, 
  CallRecordExistQueryResultVO, 
  CallUserInstanceQueryRequest, 
  CallInstanceInfoVO, 
  CallUseLaunchSurveyToReleaseCallRequest, 
  CallUseLaunchSurveyToReleaseCallVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class CallCenterQueryGatewayServiceClient implements Partial<CallCenterQueryGatewayService> {
  public async getCallRecordMissingVisit(request: Partial<CallRecordMissingVisitQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<string[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallCenterQueryGatewayService.getCallRecordMissingVisit:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getCallRecordStatsInfo(request: Partial<CallRecordStatsInfoQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<CallRecordStatsInfoVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallCenterQueryGatewayService.getCallRecordStatsInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getTargetCallRecordMissingVisit(request: Partial<CallRecordMissVisitQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<CallRecordExistQueryResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallCenterQueryGatewayService.getTargetCallRecordMissingVisit:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getUserInstances(request: Partial<CallUserInstanceQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<CallInstanceInfoVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallCenterQueryGatewayService.getUserInstances:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async useLaunchSurveyToReleaseCall(request: Partial<CallUseLaunchSurveyToReleaseCallRequest>, option?: GwRequestOption): Promise<GatewayResult<CallUseLaunchSurveyToReleaseCallVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallCenterQueryGatewayService.useLaunchSurveyToReleaseCall:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new CallCenterQueryGatewayServiceClient();
