/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitCategoryItemGateway,
  VisitCategoryItemStatusChangeRequest, 
  GatewayResult, 
  VisitCategoryItemCreateRequest, 
  VisitCategoryItemDeleteRequest, 
  VisitCategoryItemModifyRequest, 
  VisitCategoryItemDetailQueryRequest, 
  VisitCategoryVO, 
  VisitCategoryItemPageQueryRequest, 
  VisitCategoryEleVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitCategoryItemGatewayClient implements Partial<VisitCategoryItemGateway> {
  public async changeCategoryItemStatus(request: Partial<VisitCategoryItemStatusChangeRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCategoryItemGateway.changeCategoryItemStatus:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async createCategoryItem(request: Partial<VisitCategoryItemCreateRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCategoryItemGateway.createCategoryItem:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async deleteCategoryItem(request: Partial<VisitCategoryItemDeleteRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCategoryItemGateway.deleteCategoryItem:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async modifyCategoryItem(request: Partial<VisitCategoryItemModifyRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCategoryItemGateway.modifyCategoryItem:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryCategoryItemDetail(request: Partial<VisitCategoryItemDetailQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitCategoryVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCategoryItemGateway.queryCategoryItemDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryCategoryItemList(request: Partial<VisitCategoryItemPageQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitCategoryEleVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitCategoryItemGateway.queryCategoryItemList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitCategoryItemGatewayClient();
