/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  SmartWordYundingQueryFacade,
  SmartWordTemplateInstRecallRequst, 
  GatewayResult, 
  BaseTemplateInstRecallDTO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class SmartWordYundingQueryFacadeClient implements Partial<SmartWordYundingQueryFacade> {
  public async recall(request: Partial<SmartWordTemplateInstRecallRequst>, option?: GwRequestOption): Promise<GatewayResult<BaseTemplateInstRecallDTO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordYundingQueryFacade.recall:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new SmartWordYundingQueryFacadeClient();
