/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitConfigGateway,
  VisitConfigStatusChangeRequest, 
  GatewayResult, 
  VisitConfigCreateRequest, 
  VisitConfigDeleteRequest, 
  VisitConfigModifyRequest, 
  VisitConfigPageQueryRequest, 
  Pagination, 
  VisitConfigEleVO, 
  VisitConfigDetailQueryRequest, 
  VisitConfigVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitConfigGatewayClient implements Partial<VisitConfigGateway> {
  public async changeConfigStatus(request: Partial<VisitConfigStatusChangeRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitConfigGateway.changeConfigStatus:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async createConfig(request: Partial<VisitConfigCreateRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitConfigGateway.createConfig:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async deleteConfig(request: Partial<VisitConfigDeleteRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitConfigGateway.deleteConfig:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async modifyConfig(request: Partial<VisitConfigModifyRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitConfigGateway.modifyConfig:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async pageQueryConfigList(request: Partial<VisitConfigPageQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<VisitConfigEleVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitConfigGateway.pageQueryConfigList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryConfigDetail(request: Partial<VisitConfigDetailQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitConfigVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitConfigGateway.queryConfigDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitConfigGatewayClient();
