/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  SmartWordTemplateInfoQueryGatewayService,
  SmartWordTemplateInfoDetailQueryRequest, 
  GatewayResult, 
  SmartWordTemplateInfoDTO, 
  SmartWordIndicatorConditionQueryRequest, 
  IndicatorDTO, 
  SmartWordGroupRuleConfigQueryRequest, 
  RuleGroupConfigDTO, 
  SmartWordTemplateInfoQueryRequest, 
  Pagination,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class SmartWordTemplateInfoQueryGatewayServiceClient implements Partial<SmartWordTemplateInfoQueryGatewayService> {
  public async queryDetail(request: Partial<SmartWordTemplateInfoDetailQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<SmartWordTemplateInfoDTO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoQueryGatewayService.queryDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryIndicatorCondition(request: Partial<SmartWordIndicatorConditionQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<IndicatorDTO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoQueryGatewayService.queryIndicatorCondition:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryLabelConditions(request: Partial<SmartWordGroupRuleConfigQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<RuleGroupConfigDTO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoQueryGatewayService.queryLabelConditions:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryList(request: Partial<SmartWordTemplateInfoQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<SmartWordTemplateInfoDTO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoQueryGatewayService.queryList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new SmartWordTemplateInfoQueryGatewayServiceClient();
