/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  CallCenterManageGatewayService,
  GatewayResult, 
  CallRecordSaveRequest,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class CallCenterManageGatewayServiceClient implements Partial<CallCenterManageGatewayService> {
  public async commonRequest(action: string, request: string, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallCenterManageGatewayService.commonRequest:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [action, request],
    }, option);
    return res;
  }

  public async saveCallRecord(request: Partial<CallRecordSaveRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallCenterManageGatewayService.saveCallRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new CallCenterManageGatewayServiceClient();
