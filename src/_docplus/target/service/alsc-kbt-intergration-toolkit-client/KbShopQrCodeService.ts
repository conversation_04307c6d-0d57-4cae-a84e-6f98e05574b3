/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  KbShopQrCodeService,
  IsRetailShopReq, 
  KbResult, 
  ShowKbShopQrCodeReq, 
  ShowKbShopQrCodeH5Req, 
  ShowKbShopQrCodeH5VO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class KbShopQrCodeServiceClient implements Partial<KbShopQrCodeService> {
  public async isRetailShop(shopReq: Partial<IsRetailShopReq>, option?: GwRequestOption): Promise<KbResult<boolean>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbShopQrCodeService.isRetailShop:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [shopReq],
    }, option);
    return res;
  }

  public async showKbShopQrCode(qrCodeReq: Partial<ShowKbShopQrCodeReq>, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbShopQrCodeService.showKbShopQrCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [qrCodeReq],
    }, option);
    return res;
  }

  public async showKbShopQrCodeH5(qrCodeH5Req: Partial<ShowKbShopQrCodeH5Req>, option?: GwRequestOption): Promise<KbResult<ShowKbShopQrCodeH5VO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbShopQrCodeService.showKbShopQrCodeH5:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [qrCodeH5Req],
    }, option);
    return res;
  }
}

export default new KbShopQrCodeServiceClient();
