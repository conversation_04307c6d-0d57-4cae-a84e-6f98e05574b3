/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  CallShortNumberGateway,
  CallShortNumberCancelRequest, 
  GatewayResult, 
  BaseRequest, 
  CallBindRecordMissingResultVO, 
  CallBindRecordReportRequest,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class CallShortNumberGatewayClient implements Partial<CallShortNumberGateway> {
  public async cancelTargetCallRecordVisit(request: Partial<CallShortNumberCancelRequest>, option?: GwRequestOption): Promise<GatewayResult<boolean>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallShortNumberGateway.cancelTargetCallRecordVisit:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getBindRecordMissingVisit(request: Partial<BaseRequest>, option?: GwRequestOption): Promise<GatewayResult<CallBindRecordMissingResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallShortNumberGateway.getBindRecordMissingVisit:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async reportBindRecord(request: Partial<CallBindRecordReportRequest>, option?: GwRequestOption): Promise<GatewayResult<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallShortNumberGateway.reportBindRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new CallShortNumberGatewayClient();
