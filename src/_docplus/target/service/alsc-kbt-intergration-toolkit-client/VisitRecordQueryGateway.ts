/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitRecordQueryGateway,
  ExportFileGatewayRequest, 
  GatewayResult, 
  VisitRecordTargetQueryRequest, 
  Pagination, 
  VisitRecordInfoVO, 
  VisitRecordPageQueryRequest, 
  VisitIntentionRequest, 
  IntentionOptionVO, 
  VisitRecordCardQueryRequest, 
  VisitRecordCardVO, 
  VisitRecordDetailQueryRequest, 
  VisitRecordDetailVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitRecordQueryGatewayClient implements Partial<VisitRecordQueryGateway> {
  public async exportRecord(request: Partial<ExportFileGatewayRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordQueryGateway.exportRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async pageQueryByTarget(request: Partial<VisitRecordTargetQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<VisitRecordInfoVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordQueryGateway.pageQueryByTarget:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async pageQueryRecord(request: Partial<VisitRecordPageQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<VisitRecordInfoVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordQueryGateway.pageQueryRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryIntentionList(request: Partial<VisitIntentionRequest>, option?: GwRequestOption): Promise<GatewayResult<IntentionOptionVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordQueryGateway.queryIntentionList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryTargetDigest(request: Partial<VisitRecordCardQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitRecordCardVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordQueryGateway.queryTargetDigest:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryVisitRecordDetail(request: Partial<VisitRecordDetailQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitRecordDetailVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordQueryGateway.queryVisitRecordDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitRecordQueryGatewayClient();
