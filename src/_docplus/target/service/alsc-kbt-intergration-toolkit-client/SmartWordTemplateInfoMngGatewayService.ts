/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  SmartWordTemplateInfoMngGatewayService,
  SmartWordTemplateInfoSaveRequest, 
  GatewayResult, 
  SmartWordTemplateInfoDisableRequest, 
  SmartWordTemplateInfoEnableRequest,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class SmartWordTemplateInfoMngGatewayServiceClient implements Partial<SmartWordTemplateInfoMngGatewayService> {
  public async create(request: Partial<SmartWordTemplateInfoSaveRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoMngGatewayService.create:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async disable(disableRequest: Partial<SmartWordTemplateInfoDisableRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoMngGatewayService.disable:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [disableRequest],
    }, option);
    return res;
  }

  public async enable(enableRequest: Partial<SmartWordTemplateInfoEnableRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoMngGatewayService.enable:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [enableRequest],
    }, option);
    return res;
  }

  public async modify(request: Partial<SmartWordTemplateInfoSaveRequest>, option?: GwRequestOption): Promise<GatewayResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordTemplateInfoMngGatewayService.modify:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new SmartWordTemplateInfoMngGatewayServiceClient();
