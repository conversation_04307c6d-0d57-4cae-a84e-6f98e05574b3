/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  TestQueryService,
  ShopViewDTO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class TestQueryServiceClient implements Partial<TestQueryService> {
  public async genQrCode(text: string, secne: string, caption: string, expires: number, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.genQrCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [text, secne, caption, expires],
    }, option);
    return res;
  }

  public async invoke(accountId: string, serviceName: string, functionName: string, arg: any, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.invoke:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [accountId, serviceName, functionName, arg],
    }, option);
    return res;
  }

  public async queryById(operatorId: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.queryById:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [operatorId],
    }, option);
    return res;
  }

  public async queryMerchantCoreSingle(partnerId: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.queryMerchantCoreSingle:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [partnerId],
    }, option);
    return res;
  }

  public async queryShop(kbShopId: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.queryShop:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [kbShopId],
    }, option);
    return res;
  }

  public async queryShopIds(kbShopIds: string[], option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.queryShopIds:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [kbShopIds],
    }, option);
    return res;
  }

  public async queryShopVide(kbShopId: string, option?: GwRequestOption): Promise<ShopViewDTO> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.queryShopVide:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [kbShopId],
    }, option);
    return res;
  }

  public async queryUserJobs(str: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.queryUserJobs:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [str],
    }, option);
    return res;
  }

  public async search(request: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.search:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async testAdd(name: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.testAdd:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [name],
    }, option);
    return res;
  }

  public async testQuery(id: number, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.TestQueryService.testQuery:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [id],
    }, option);
    return res;
  }
}

export default new TestQueryServiceClient();
