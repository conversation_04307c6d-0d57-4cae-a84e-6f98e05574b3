/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  CallGatewayService,
  OutCallPrepareRequest, 
  GwBaseResultDTO, 
  OutCallPrepareResultVO, 
  OutCallProgressRequest, 
  OutCallProgressResultVO, 
  CallBlockQueryRequest, 
  CallBlockInfoVO, 
  CallAgentManageRequest, 
  CallAgentVO, 
  CallRecordQueryRequest, 
  CallRecordSimpleVO, 
  CallRecordDetailQueryRequest, 
  CallRecordDetailVO, 
  OutCallAdditionalActivityRequest, 
  OutCallResultReportRequest, 
  BdSalesPhoneSaveRequest, 
  CallRecordSearchRequest, 
  Pagination, 
  CalRecordSearchListEleVO, 
  CallTargetInfoSearchRequest, 
  CallTargetVO, 
  OutCallStartRequest, 
  OutCallStartResultVO, 
  OutCallResultSubmitOrderRequest,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class CallGatewayServiceClient implements Partial<CallGatewayService> {
  public async prepare(request: Partial<OutCallPrepareRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<OutCallPrepareResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.prepare:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async progressOutCall(request: Partial<OutCallProgressRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<OutCallProgressResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.progressOutCall:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryCallBlockInfo(request: Partial<CallBlockQueryRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<CallBlockInfoVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.queryCallBlockInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryOrRegisterAgent(request: Partial<CallAgentManageRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<CallAgentVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.queryOrRegisterAgent:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryRecord(request: Partial<CallRecordQueryRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<CallRecordSimpleVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.queryRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryRecordDetail(request: Partial<CallRecordDetailQueryRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<CallRecordDetailVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.queryRecordDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async reportOutCallAdditionalActivity(request: Partial<OutCallAdditionalActivityRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.reportOutCallAdditionalActivity:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async reportOutCallResult(request: Partial<OutCallResultReportRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.reportOutCallResult:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async saveBdSalesPhone(request: Partial<BdSalesPhoneSaveRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.saveBdSalesPhone:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async searchCallRecord(request: Partial<CallRecordSearchRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<Pagination<CalRecordSearchListEleVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.searchCallRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async searchTargetInfo(request: Partial<CallTargetInfoSearchRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<Pagination<CallTargetVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.searchTargetInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async startOutCall(request: Partial<OutCallStartRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<OutCallStartResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.startOutCall:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async submitOrder(request: Partial<OutCallResultSubmitOrderRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<any>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallGatewayService.submitOrder:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new CallGatewayServiceClient();
