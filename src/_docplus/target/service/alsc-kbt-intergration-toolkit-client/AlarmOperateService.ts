/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  AlarmOperateService,
  AlarmOperateRequest, 
  KbResult,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class AlarmOperateServiceClient implements Partial<AlarmOperateService> {
  public async add(request: Partial<AlarmOperateRequest>, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmOperateService.add:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async archive(alarmId: number, option?: GwRequestOption): Promise<KbResult<void>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmOperateService.archive:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [alarmId],
    }, option);
    return res;
  }

  public async ignore(alarmId: number, ignoreStatus: number, option?: GwRequestOption): Promise<KbResult<void>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmOperateService.ignore:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [alarmId, ignoreStatus],
    }, option);
    return res;
  }

  public async sendMessage(alarmId: number, option?: GwRequestOption): Promise<KbResult<void>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AlarmOperateService.sendMessage:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [alarmId],
    }, option);
    return res;
  }
}

export default new AlarmOperateServiceClient();
