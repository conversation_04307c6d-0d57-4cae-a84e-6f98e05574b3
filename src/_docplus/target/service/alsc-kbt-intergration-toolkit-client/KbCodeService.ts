/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  KbCodeService,
  CreateQrCodeReq, 
  KbResult, 
  CodeBatchReq, 
  KbPageResult, 
  StuffQrcodeApplyDto, 
  BindBatchReq, 
  QrcodeBindRelationDto, 
  BindTargetCodesReq, 
  BindQrCodeVO, 
  BindPidBatchReq, 
  UnbindBatchReq, 
  UnBindCodeReq, 
  QrCodeVO, 
  ParseTextReq, 
  KbCodeTemplateConfigVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class KbCodeServiceClient implements Partial<KbCodeService> {
  public async create(createReq: Partial<CreateQrCodeReq>, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.create:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [createReq],
    }, option);
    return res;
  }

  public async pageQueryKBCodeBatch(req: Partial<CodeBatchReq>, option?: GwRequestOption): Promise<KbPageResult<StuffQrcodeApplyDto>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.pageQueryKBCodeBatch:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }

  public async pageQueryKBCodeBindInfo(req: Partial<BindBatchReq>, option?: GwRequestOption): Promise<KbPageResult<QrcodeBindRelationDto>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.pageQueryKBCodeBindInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }

  public async pageQueryKBCodeBindTargetCodes(req: Partial<BindTargetCodesReq>, option?: GwRequestOption): Promise<KbPageResult<BindQrCodeVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.pageQueryKBCodeBindTargetCodes:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }

  public async pageQueryKBCodePidBindInfo(req: Partial<BindPidBatchReq>, option?: GwRequestOption): Promise<KbPageResult<QrcodeBindRelationDto>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.pageQueryKBCodePidBindInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }

  public async pageQueryKBCodeUnbindBatch(req: Partial<UnbindBatchReq>, option?: GwRequestOption): Promise<KbPageResult<StuffQrcodeApplyDto>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.pageQueryKBCodeUnbindBatch:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }

  public async pageQueryKBCodeUnbindCode(req: Partial<UnBindCodeReq>, option?: GwRequestOption): Promise<KbPageResult<QrCodeVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.pageQueryKBCodeUnbindCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }

  public async parseText(parseTextReq: Partial<ParseTextReq>, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.parseText:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [parseTextReq],
    }, option);
    return res;
  }

  public async queryCreateTemplate(option?: GwRequestOption): Promise<KbResult<KbCodeTemplateConfigVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeService.queryCreateTemplate:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [],
    }, option);
    return res;
  }
}

export default new KbCodeServiceClient();
