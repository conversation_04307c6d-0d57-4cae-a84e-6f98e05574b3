/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitTargetGateway,
  VisitTargetDetailQueryRequest, 
  GatewayResult, 
  VisitTargetVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitTargetGatewayClient implements Partial<VisitTargetGateway> {
  public async queryTargetDetail(request: Partial<VisitTargetDetailQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitTargetVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitTargetGateway.queryTargetDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitTargetGatewayClient();
