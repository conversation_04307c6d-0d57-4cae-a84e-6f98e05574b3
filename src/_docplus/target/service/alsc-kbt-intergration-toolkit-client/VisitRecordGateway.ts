/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  VisitRecordGateway,
  VisitRecordCreateRequest, 
  GatewayResult, 
  VisitRecordCreateResultVO, 
  VisitRecordModifyRequest, 
  VisitRecordModifyResultVO, 
  VisitRecordPrepareRequest, 
  VisitRecordPrepareResultVO, 
  VisitConfigDecisionRequest, 
  VisitConfigDecisionResultVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class VisitRecordGatewayClient implements Partial<VisitRecordGateway> {
  public async createRecord(request: Partial<VisitRecordCreateRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitRecordCreateResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordGateway.createRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async modifyRecord(request: Partial<VisitRecordModifyRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitRecordModifyResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordGateway.modifyRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async prepare(request: Partial<VisitRecordPrepareRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitRecordPrepareResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordGateway.prepare:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryUserConfigList(request: Partial<VisitConfigDecisionRequest>, option?: GwRequestOption): Promise<GatewayResult<VisitConfigDecisionResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.VisitRecordGateway.queryUserConfigList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new VisitRecordGatewayClient();
