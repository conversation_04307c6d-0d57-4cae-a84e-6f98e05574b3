/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  KbCodeCommonService,
  QueryByNameReq, 
  CascaderVO, 
  SearchAgentShopReq, 
  KbCodeShopVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class KbCodeCommonServiceClient implements Partial<KbCodeCommonService> {
  public async queryByName(req: Partial<QueryByNameReq>, option?: GwRequestOption): Promise<CascaderVO[]> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeCommonService.queryByName:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }

  public async searchAgentShops(req: Partial<SearchAgentShopReq>, option?: GwRequestOption): Promise<KbCodeShopVO[]> {
    const innerKey = `alsc-kbt-intergration-toolkit.KbCodeCommonService.searchAgentShops:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [req],
    }, option);
    return res;
  }
}

export default new KbCodeCommonServiceClient();
