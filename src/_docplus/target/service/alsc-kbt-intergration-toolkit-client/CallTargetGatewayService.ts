/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  CallTargetGatewayService,
  CallTargetCountRequest, 
  GatewayResult, 
  CallTargetCountVO, 
  CallTargetPageQueryRequest, 
  Pagination, 
  CallTargetVO, 
  CallTargetContactPersonQueryRequest, 
  CallTargetContactInfoVO, 
  CallTargetSearchByPhoneRequest,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class CallTargetGatewayServiceClient implements Partial<CallTargetGatewayService> {
  public async countTarget(request: Partial<CallTargetCountRequest>, option?: GwRequestOption): Promise<GatewayResult<CallTargetCountVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallTargetGatewayService.countTarget:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async pageQueryTargetList(request: Partial<CallTargetPageQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<CallTargetVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallTargetGatewayService.pageQueryTargetList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async queryTargetContactInfo(request: Partial<CallTargetContactPersonQueryRequest>, option?: GwRequestOption): Promise<GatewayResult<CallTargetContactInfoVO[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallTargetGatewayService.queryTargetContactInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async searchTargetByPhone(request: Partial<CallTargetSearchByPhoneRequest>, option?: GwRequestOption): Promise<GatewayResult<Pagination<CallTargetVO[]>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.CallTargetGatewayService.searchTargetByPhone:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new CallTargetGatewayServiceClient();
