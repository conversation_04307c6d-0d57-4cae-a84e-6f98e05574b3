/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  ExportFileGatewayService,
  ExportFileGatewayRequest, 
  KbResult, 
  ExportFilePageQueryRecordGatewayRequest, 
  KbPageResult, 
  ExportFileRecordVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class ExportFileGatewayServiceClient implements Partial<ExportFileGatewayService> {
  public async exportFile(request: Partial<ExportFileGatewayRequest>, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ExportFileGatewayService.exportFile:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getExportFileResultUrl(exportId: number, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ExportFileGatewayService.getExportFileResultUrl:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [exportId],
    }, option);
    return res;
  }

  public async pageQueryExportFileRecord(request: Partial<ExportFilePageQueryRecordGatewayRequest>, option?: GwRequestOption): Promise<KbPageResult<ExportFileRecordVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ExportFileGatewayService.pageQueryExportFileRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new ExportFileGatewayServiceClient();
