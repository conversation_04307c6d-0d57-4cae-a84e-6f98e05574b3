/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  SmartWordGatewayService,
  SmartWordMatchRequest, 
  GwBaseResultDTO, 
  SmartWordMatchResultVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class SmartWordGatewayServiceClient implements Partial<SmartWordGatewayService> {
  public async match(request: Partial<SmartWordMatchRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordGatewayService.match:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async matchV2(request: Partial<SmartWordMatchRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<SmartWordMatchResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.SmartWordGatewayService.matchV2:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new SmartWordGatewayServiceClient();
