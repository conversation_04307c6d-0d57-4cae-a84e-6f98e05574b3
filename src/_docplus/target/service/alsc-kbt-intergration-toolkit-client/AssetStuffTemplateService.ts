/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  AssetStuffTemplateService,
  OperateRequest, 
  StuffTemplate, 
  KbResult, 
  StuffCopyTemplate, 
  Request, 
  StuffTemplateCondition, 
  OperatorInfo, 
  PageQueryRequest, 
  StuffTemplateQueryCondition, 
  PageQueryResult, 
  StuffTemplateDto, 
  StuffTemplateUpStatus,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class AssetStuffTemplateServiceClient implements Partial<AssetStuffTemplateService> {
  public async addTemplate(request: Partial<OperateRequest<StuffTemplate>>, option?: GwRequestOption): Promise<KbResult<number>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.addTemplate:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async copyTemplate(request: Partial<OperateRequest<StuffCopyTemplate>>, option?: GwRequestOption): Promise<KbResult<number>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.copyTemplate:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async editTemplate(request: Partial<OperateRequest<StuffTemplate>>, option?: GwRequestOption): Promise<KbResult<number>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.editTemplate:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async getOperatorInfo(request: Partial<Request<StuffTemplateCondition>>, option?: GwRequestOption): Promise<KbResult<OperatorInfo>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.getOperatorInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async query(pageQueryRequest: Partial<PageQueryRequest<StuffTemplateQueryCondition>>, option?: GwRequestOption): Promise<PageQueryResult<StuffTemplateDto[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.query:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [pageQueryRequest],
    }, option);
    return res;
  }

  public async queryInfo(id: Partial<Request<number>>, option?: GwRequestOption): Promise<KbResult<StuffTemplateDto>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.queryInfo:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [id],
    }, option);
    return res;
  }

  public async queryStuffTemplateSelection(request: Partial<Request<StuffTemplateCondition>>, option?: GwRequestOption): Promise<KbResult<StuffTemplateDto[]>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.queryStuffTemplateSelection:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async upTemplateStatus(request: Partial<OperateRequest<StuffTemplateUpStatus>>, option?: GwRequestOption): Promise<KbResult<void>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AssetStuffTemplateService.upTemplateStatus:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new AssetStuffTemplateServiceClient();
