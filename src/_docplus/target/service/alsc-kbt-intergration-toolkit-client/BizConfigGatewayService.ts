/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  BizConfigGatewayService,
  GrayConfigQueryGwRequest, 
  GwBaseResultDTO, 
  BizGrayConfigGwResultVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class BizConfigGatewayServiceClient implements Partial<BizConfigGatewayService> {
  public async queryForAppMigration(request: Partial<GrayConfigQueryGwRequest>, option?: GwRequestOption): Promise<GwBaseResultDTO<BizGrayConfigGwResultVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.BizConfigGatewayService.queryForAppMigration:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new BizConfigGatewayServiceClient();
