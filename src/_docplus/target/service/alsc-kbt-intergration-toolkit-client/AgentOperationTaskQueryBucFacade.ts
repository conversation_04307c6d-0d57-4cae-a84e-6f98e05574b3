/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  AgentOperationTaskQueryBucFacade,
  AgentOperationMerchantTaskListRequest, 
  GatewayResult, 
  CommonPageData, 
  AgentOperationMerchantTaskDTO, 
  AgentOperationShopTaskListRequest, 
  AgentOperationShopTaskDTO, 
  AgentOperationStatisticsRequest, 
  AgentOperationStatisticsDTO, 
  AgentOperationTaskDetailRequest, 
  AgentOperationTaskDetailDTO, 
  AgentOperationPerformanceStatisticsRequest, 
  AgentPerformanceStatisticsDTO, 
  AgentOperationShopChangeRequest, 
  AgentOperationShopChangeDTO, 
  AgentOperationMerchantDetailRequest, 
  AgentOperationMerchantDetailDTO, 
  AgentOperationShortLinkRequest, 
  AgentOperationShortLinkDTO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class AgentOperationTaskQueryBucFacadeClient implements Partial<AgentOperationTaskQueryBucFacade> {
  public async queryAgentOperationMerchantTaskList(agentOperationMerchantListRequest: Partial<AgentOperationMerchantTaskListRequest>, option?: GwRequestOption): Promise<GatewayResult<CommonPageData<AgentOperationMerchantTaskDTO>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryAgentOperationMerchantTaskList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationMerchantListRequest],
    }, option);
    return res;
  }

  public async queryAgentOperationShopTaskList(agentOperationShopTaskListRequest: Partial<AgentOperationShopTaskListRequest>, option?: GwRequestOption): Promise<GatewayResult<CommonPageData<AgentOperationShopTaskDTO>>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryAgentOperationShopTaskList:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationShopTaskListRequest],
    }, option);
    return res;
  }

  public async queryAgentOperationStatistics(agentOperationStatisticsRequest: Partial<AgentOperationStatisticsRequest>, option?: GwRequestOption): Promise<GatewayResult<AgentOperationStatisticsDTO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryAgentOperationStatistics:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationStatisticsRequest],
    }, option);
    return res;
  }

  public async queryAgentOperationTaskDetail(agentOperationTaskDetailRequest: Partial<AgentOperationTaskDetailRequest>, option?: GwRequestOption): Promise<GatewayResult<AgentOperationTaskDetailDTO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryAgentOperationTaskDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationTaskDetailRequest],
    }, option);
    return res;
  }

  public async queryAgentPerformanceData(agentOperationStatisticsRequest: Partial<AgentOperationPerformanceStatisticsRequest>, option?: GwRequestOption): Promise<GatewayResult<AgentPerformanceStatisticsDTO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryAgentPerformanceData:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationStatisticsRequest],
    }, option);
    return res;
  }

  public async queryAgentShopChange(agentOperationShopChangeRequest: Partial<AgentOperationShopChangeRequest>, option?: GwRequestOption): Promise<GatewayResult<AgentOperationShopChangeDTO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryAgentShopChange:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationShopChangeRequest],
    }, option);
    return res;
  }

  public async queryMerchantDetail(agentOperationMerchantDetailRequest: Partial<AgentOperationMerchantDetailRequest>, option?: GwRequestOption): Promise<GatewayResult<AgentOperationMerchantDetailDTO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryMerchantDetail:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationMerchantDetailRequest],
    }, option);
    return res;
  }

  public async queryShortLink(agentOperationShortLinkRequest: Partial<AgentOperationShortLinkRequest>, option?: GwRequestOption): Promise<GatewayResult<AgentOperationShortLinkDTO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.AgentOperationTaskQueryBucFacade.queryShortLink:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [agentOperationShortLinkRequest],
    }, option);
    return res;
  }
}

export default new AgentOperationTaskQueryBucFacadeClient();
