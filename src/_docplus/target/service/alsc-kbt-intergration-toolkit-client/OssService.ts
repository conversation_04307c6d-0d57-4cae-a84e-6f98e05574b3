/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  OssService,
  OssSignatureCO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class OssServiceClient implements Partial<OssService> {
  public async generateSignedUrl(imgs: string[], option?: GwRequestOption): Promise<{[index: string]: string}> {
    const innerKey = `alsc-kbt-intergration-toolkit.OssService.generateSignedUrl:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [imgs],
    }, option);
    return res;
  }

  public async generateSignedUrlSingle(img: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.OssService.generateSignedUrlSingle:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [img],
    }, option);
    return res;
  }

  public async generateSignedUrlSingleForApproval(img: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.OssService.generateSignedUrlSingleForApproval:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [img],
    }, option);
    return res;
  }

  public async getOssSignature(option?: GwRequestOption): Promise<OssSignatureCO> {
    const innerKey = `alsc-kbt-intergration-toolkit.OssService.getOssSignature:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [],
    }, option);
    return res;
  }

  public async putFileToOss(fileName: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.OssService.putFileToOss:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [fileName],
    }, option);
    return res;
  }
}

export default new OssServiceClient();
