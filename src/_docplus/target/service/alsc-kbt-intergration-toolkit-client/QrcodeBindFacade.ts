/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  QrcodeBindFacade,
  DownloadQRCodeReq, 
  KbResult, 
  BindCodeReq, 
  BindResVO, 
  QueryShopListByPoiReq, 
  KbPageResult, 
  KBShopSearchVO, 
  QueryKbCodeReq, 
  ShopKbCodeDto, 
  VerifyCodeReq, 
  VerifyCodeVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class QrcodeBindFacadeClient implements Partial<QrcodeBindFacade> {
  public async batchDownloadQRCode(downloadQRCodeReq: Partial<DownloadQRCodeReq>, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.QrcodeBindFacade.batchDownloadQRCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [downloadQRCodeReq],
    }, option);
    return res;
  }

  public async bindCode(bindCodeReq: Partial<BindCodeReq>, option?: GwRequestOption): Promise<KbResult<BindResVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.QrcodeBindFacade.bindCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [bindCodeReq],
    }, option);
    return res;
  }

  public async isInPercentOnBind(option?: GwRequestOption): Promise<KbResult<boolean>> {
    const innerKey = `alsc-kbt-intergration-toolkit.QrcodeBindFacade.isInPercentOnBind:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [],
    }, option);
    return res;
  }

  public async needBuildShopCode(shopId: string, appSource: string, option?: GwRequestOption): Promise<KbResult<boolean>> {
    const innerKey = `alsc-kbt-intergration-toolkit.QrcodeBindFacade.needBuildShopCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [shopId, appSource],
    }, option);
    return res;
  }

  public async queryMyShopListByPoi(queryShopListByPoiReq: Partial<QueryShopListByPoiReq>, option?: GwRequestOption): Promise<KbPageResult<KBShopSearchVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.QrcodeBindFacade.queryMyShopListByPoi:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [queryShopListByPoiReq],
    }, option);
    return res;
  }

  public async queryShopKbCode(queryKbCodeReq: Partial<QueryKbCodeReq>, option?: GwRequestOption): Promise<KbResult<ShopKbCodeDto>> {
    const innerKey = `alsc-kbt-intergration-toolkit.QrcodeBindFacade.queryShopKbCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [queryKbCodeReq],
    }, option);
    return res;
  }

  public async verifyCode(verifyCodeReq: Partial<VerifyCodeReq>, option?: GwRequestOption): Promise<KbResult<VerifyCodeVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.QrcodeBindFacade.verifyCode:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [verifyCodeReq],
    }, option);
    return res;
  }
}

export default new QrcodeBindFacadeClient();
