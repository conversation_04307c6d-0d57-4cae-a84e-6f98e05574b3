/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  SalesExecuteService,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class SalesExecuteServiceClient implements Partial<SalesExecuteService> {
  public async executeData(tableName: string, operator: string, data: string, option?: GwRequestOption): Promise<boolean> {
    const innerKey = `alsc-kbt-intergration-toolkit.SalesExecuteService.executeData:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [tableName, operator, data],
    }, option);
    return res;
  }

  public async testTriple(scene: string, reqStr: string, option?: GwRequestOption): Promise<string> {
    const innerKey = `alsc-kbt-intergration-toolkit.SalesExecuteService.testTriple:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [scene, reqStr],
    }, option);
    return res;
  }
}

export default new SalesExecuteServiceClient();
