/**
 * Generated By @ali/alsc-docplus-cli
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-intergration-toolkit-client
 * Version: -
 */
import client, { GwRequestOption } from '@/_docplus/target/request/client';

import {
  ImportFileGatewayService,
  KbResult, 
  ImportFileGatewayRequest, 
  ImportFilePageQueryRecordGatewayRequest, 
  KbPageResult, 
  ImportFileRecordVO,
} from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';

import { DOC_MODE } from '@/_docplus/target/request/const';

class ImportFileGatewayServiceClient implements Partial<ImportFileGatewayService> {
  public async getImportFileResultUrl(importId: number, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ImportFileGatewayService.getImportFileResultUrl:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [importId],
    }, option);
    return res;
  }

  public async getImportFileSuccessResultUrl(importId: number, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ImportFileGatewayService.getImportFileSuccessResultUrl:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [importId],
    }, option);
    return res;
  }

  public async getImportTemplateFileResultUrl(bizCode: string, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ImportFileGatewayService.getImportTemplateFileResultUrl:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [bizCode],
    }, option);
    return res;
  }

  public async importFile(request: Partial<ImportFileGatewayRequest>, option?: GwRequestOption): Promise<KbResult<string>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ImportFileGatewayService.importFile:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }

  public async pageQueryImportFileRecord(request: Partial<ImportFilePageQueryRecordGatewayRequest>, option?: GwRequestOption): Promise<KbPageResult<ImportFileRecordVO>> {
    const innerKey = `alsc-kbt-intergration-toolkit.ImportFileGatewayService.pageQueryImportFileRecord:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    }, option);
    return res;
  }
}

export default new ImportFileGatewayServiceClient();
