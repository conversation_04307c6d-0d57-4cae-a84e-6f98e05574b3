import { defineAppConfig } from '@ice/runtime';
import './global.less';
import { defineChildConfig } from '@ali/ice-plugin-qiankun/types';

const isDev = process.env.NODE_ENV === 'development';

export default defineAppConfig(() => {
  let basename: string;
  if (isDev) {
    basename = '';
  } else if (typeof window !== 'undefined' && window?.__POWERED_BY_QIANKUN__) {
    basename = '/micro/app/alsc-merchants/xy-task-pc';
  } else {
    basename = '/sale-webapp/xy-task-pc';
  }
  return {
    router: {
      basename,
    },
  };
});

export const qiankun = defineChildConfig(() => {
  return {
    mount: () => {
      console.log('mount');
      console.log(
        `%c最近更新时间: ${BUILD_TIME?.value}`,
        'background:#3B82FE; padding: 4px; padding-right: 8px; border-radius: 4px; color: #fff;',
      );
    },
    unmount: () => {
      console.log('unmount');
    },
  };
});
