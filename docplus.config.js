/**
 * Generated By @ali/alsc-docplus-cli (Ver.1.3.21)
 * @see https://yuque.antfin.com/alsc-gateway/docplus/ouo0ls
 */
module.exports = {
  // 云鼎appId
  app: ['alsc-kbt-intergration-toolkit', 'amap-sales-operation', 'amap-sales-data'],
  // 内置模板: 默认（云鼎网关）; 云鼎网关 - default; 销售网络标准接口 - sn;
  template: 'default',
  // 文档模式: 版本模式 - version; 分支模式 - repo;(具体每项配置参见站点帮助文档)
  mode: {
    'alsc-kbt-intergration-toolkit': {
      'alsc-kbt-intergration-toolkit-client': {
        // branch: 'feature/20240122_19168517_daiyunying_five_1',
        envSign: 'PRE',
      },
    },
    'amap-sales-operation': {
      'amap-sales-operation-client': {
        branch: 'feature/20240222_19447878_operation_five_1',
        envSign: '20240313-pre-d16yz8',
      },
    },
    'amap-sales-data': {
      'amap-sales-data-client': {
        envSign: 'PRE',
      },
    },
  },
  // 是否开启控制台挂件
  debug: false,
  // __forceDebug: true,
  // 生成代码目录配置，默认路径为
  output: {
    // API过滤配置(保留符合过滤条件的接口)。支持树形结构
    apiFilter: null,
    // 生成文件根目录。默认：工程目录/src
    rootDir: '_docplus/target',
    // Typescript类型声明文件位置。默认: 工程目录/src/types
    types: '',
    // API请求代码位置。默认: 工程目录/src/service
    service: '',
    // 网络请求库代码位置。默认：工程目录/src/request
    client: '',
  },
  custom: {
    client: '@ali/kb-fetch',
  },
};
