{
  "compilerOptions": {
    "module": "esnext",
    "target": "ES6",
    "lib": ["es6", "dom"],
    "sourceMap": true,
    "allowJs": true,
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": false,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": false,
    "strictNullChecks": false,
    "experimentalDecorators": true,
    "alwaysStrict": true,
    "skipLibCheck": true,
    "noEmit": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["./src/*"],
      "ice": [".ice"]
    }
  },
  "include": ["src"]
  // "exclude": ["node_modules", "build", "scripts", "acceptance-tests", "webpack", "jest"]
}
